import request from '@/utils/request'

// 查询集水沟列表
export function listShoulderRecords(data) {
  return request({
    url: '/baseData/shoulder/getListPage',
    method: 'post',
    data:data
  })
}

// 查询集水沟详细
export function getShoulder(detectionRecordsId) {
  return request({
    url: '/baseData/shoulder/get/' + detectionRecordsId,
    method: 'get'
  })
}

// 新增集水沟
export function addShoulder(data) {
  return request({
    url: '/baseData/shoulder/add',
    method: 'post',
    data: data
  })
}

// 暂存集水沟
export function tempaddShoulder(data) {
  return request({
    url: '/baseData/shoulder/tempAdd',
    method: 'post',
    data: data
  })
}

// 修改集水沟
export function updateShoulder(data) {
  return request({
    url: '/baseData/shoulder/edit',
    method: 'put',
    data: data
  })
}

// 删除集水沟
export function delShoulder(detectionRecordsIds) {
  return request({
    url: '/baseData/shoulder/delete/'+detectionRecordsIds.join(','),
    method: 'delete',
  
  })
}






