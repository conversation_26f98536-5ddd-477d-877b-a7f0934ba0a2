import request from '@/utils/request';

// 文件管理分页查询
export function getFileManagementPage(data) {
  return request({
    url: '/jgjcadmin/api/basedata/file-management/page',
    method: 'post',
    data
  });
}

// 文件管理删除
export function deleteFileManagement(data) {
  return request({
    url: '/jgjcadmin/api/basedata/file-management/delete',
    method: 'post',
    data
  });
}

// 文件管理新增
export function saveFileManagement(data) {
  return request({
    url: '/jgjcadmin/api/basedata/file-management/save',
    method: 'post',
    data
  });
}

// 文件管理编辑
export function updateFileManagement(data) {
  return request({
    url: '/jgjcadmin/api/basedata/file-management/update',
    method: 'post',
    data
  });
}
