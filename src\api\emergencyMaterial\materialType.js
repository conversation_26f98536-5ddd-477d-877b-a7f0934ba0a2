import request from '@/utils/request'

// 获取类型树
export function getMaterialTypeTree() {
  return request({
    url: '/manager/emergency/material/type/get/tree',
    method: 'post',
  })
}

// 添加类型
export function addTreeDetail(data) {
  return request({
    url: '/manager/emergency/material/type/add',
    method: 'post',
    data
  })
}

// 编辑类型
export function editTreeDetail(data) {
  return request({
    url: '/manager/emergency/material/type/edit',
    method: 'put',
    data
  })
}

// 删除类型
export function delTreeDetail(id) {
  return request({
    url: `/manager/emergency/material/type/delete/${id}`,
    method: 'delete',
  })
}

// 获取物资列表
export function getMaterialList(data) {
  return request({
    url: '/manager/emergency/material/assets/list',
    method: 'post',
    data
  })
}

// 添加物资
export function addMaterial(data) {
  return request({
    url: '/manager/emergency/material/assets/add',
    method: 'post',
    data
  })
}

// 修改物资
export function editMaterial(data) {
  return request({
    url: '/manager/emergency/material/assets/edit',
    method: 'put',
    data
  })
}

// 删除物资
export function delMaterial(id) {
  return request({
    url: `/manager/emergency/material/assets/delete/${id}`,
    method: 'delete',
  })
}
