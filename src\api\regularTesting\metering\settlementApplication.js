import request from '@/utils/request'

// 定检结算计量列表
export function listSettlecalc(data) {
  return request({
    url: `manager/calc/check/settlecalc/list`,
    method: 'post',
    data
  })
}

// 新增定检结算计量单
export function addSettlecalc(data) {
  return request({
    url: `manager/calc/check/settlecalc/add`,
    method: 'post',
    data
  })
}

// 编辑定检结算计量单
export function editSettlecalc(data) {
  return request({
    url: `manager/calc/check/settlecalc/edit`,
    method: 'put',
    data
  })
}

// 删除定检结算计量单
export function deleteSettlecalc(id) {
  return request({
    url: `manager/calc/check/settlecalc/delete/${id}`,
    method: 'delete',
  })
}

// 结算计量单审核节点信息
export function getNodeInfo(data) {
  return request({
    url: `manager/calc/check/settlecalc/node/info`,
    method: 'post',
    data
  })
}

// 根据结算计量单id查询待结算的中间计量单(分页)
export function listBySid(data) {
  return request({
    url: `manager/calc/check/intermediate/list/uncalc`,
    method: 'post',
    data
  })
}

// 新增结算计量单明细
export function addMiddle(data) {
  return request({
    url: `manager/calc/check/settlecalc/detail/add`,
    method: 'post',
    data
  })
}

// 根据结算计量单id查询中间计量单明细列表
export function middleListBySid(data) {
  return request({
    url: `manager/calc/check/settlecalc/detail/list`,
    method: 'post',
    data
  })
}

// 删除结算计量单明细
export function deleteMiddle(id) {
  return request({
    url: `manager/calc/check/settlecalc/detail/delete/${id}`,
    method: 'delete',
  })
}

// 根据结算计量单id查询任务单列表
export function listEvent(data) {
  return request({
    url: `manager/calc/check/settlecalc/detail/list/visa`,
    method: 'post',
    data
  })
}


// 根据结算计量单id查询所有报价清单-子目(分页))
export function listScheme(data) {
  return request({
    url: `manager/calc/check/settle/method/list/scheme`,
    method: 'post',
    data
  })
}


// 通过结算计量单id查询结算计量单扣款清单列表
export function fetchDeductionList(data) {
  return request({
    url: `manager/calc/check/deduction/list`,
    method: 'post',
    data
  })
}

// 新增结算计量单扣款清单
export function addDeduction(data) {
  return request({
    url: `manager/calc/check/deduction/add`,
    method: 'post',
    data
  })
}

// 编辑结算计量单扣款清单
export function editDeduction(data) {
  return request({
    url: `manager/calc/check/deduction/edit`,
    method: 'put',
    data
  })
}

// 删除结算计量单扣款清单
export function deleteDeduction(id) {
  return request({
    url: `manager/calc/check/deduction/delete/${id}`,
    method: 'delete',
  })
}

// 新增结算计量单费用调整
export function addFundAdjust(data) {
  return request({
    url: `manager/calc/check/fundadjust/add`,
    method: 'post',
    data
  })
}

// 查询费用调整列表
export function fetchFundAdjustList(data) {
  return request({
    url: `manager/calc/check/fundadjust/list`,
    method: 'post',
    data
  })
}

// 编辑结算计量单费用调整
export function editFundAdjust(data) {
  return request({
    url: `manager/calc/check/fundadjust/edit`,
    method: 'put',
    data
  })
}


// 删除结算计量单费用调整
export function deleteFundAdjust(id) {
  return request({
    url: `manager/calc/check/fundadjust/delete/${id}`,
    method: 'delete',
  })
}


// 新增材料调整
export function addMaterialAdjust(data) {
  return request({
    url: `manager/calc/check/materialadjust/add`,
    method: 'post',
    data
  })
}

// 根据结算计量单id查询所有子目数据
export function listAllScheme(data) {
  return request({
      url: '/manager/calc/check/settle/method/list/all/scheme',
      method: 'post',
      data
  });
}

// 查询材料调整列表
export function fetchMaterialAdjustList(data) {
  return request({
    url: `manager/calc/check/materialadjust/list`,
    method: 'post',
    data
  })
}


// 编辑材料调整
export function editMaterialAdjust(data) {
  return request({
    url: `manager/calc/check/materialadjust/edit`,
    method: 'put',
    data
  })
}


// 删除材料调整
export function deleteMaterialAdjust(id) {
  return request({
    url: `manager/calc/check/materialadjust/delete/${id}`,
    method: 'delete',
  })
}

// 提交结算计量单
export function processSettle(data) {
  return request({
    url: `manager/calc/check/settlecalc/process`,
    method: 'post',
    data
  })
}

// 查询计量期数列表(不分页)
export function getNumberList() {
  return request({
    url: `manager/number/listAll`,
    method: 'get',
  })
}

// 查询上期计量单列表
export function getPreCalc(data) {
  return request({
    url: `manager/calc/check/settlecalc/last/list`,
    method: 'post',
    data
  })
}


// 获取报表预览
export function getPreviewInfo(params) {
  return request({
    url: '/manager/calc/check/settlecalc/report/preview',
    method: 'get',
    params,
  });
}

// 获取报表预览
export function getPreviewInfoById(id) {
  return request({
    url: `/manager/calc/check/settlecalc/report/preview?id=${id}`,
    method: 'get'
  });
}

// 报表下载
export function downloadReport(params) {
  return request({
    url: '/manager/calc/check/settlecalc/report/download',
    method: 'get',
    params,
  });
}

// 更新费用
export function updateFund(id) {
  return request({
    url: `/manager/calc/check/settlecalc/update/fund?id=${id}`,
    method: 'post',
  });
}

export function withdraw(data) {
  return request({
    url: '/manager/calc/check/settlecalc/withdraw',
    method: 'post',
    data
  });
}
