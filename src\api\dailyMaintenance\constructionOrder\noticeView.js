import request from '@/utils/request'
// 查询日养施工通知单查看列表(分页)
export function listNoticeView(data) {
    return request({
        url: '/manager/constructionView/list',
        method: 'post',
        data: data
    });
}

// 查询日养施工通知单查看详情
export function getConstructionDetail(id) {
    return request({
        url: `/manager/constructionView/getConstructionDetail/${id}`,
        method: 'get'
    });
}

// 撤回
export function rejectNoticeView(data) {
    return request({
        url: '/manager/constructionView/reject',
        method: 'put',
        data: data
    });
}

// 批量撤回
export function batchRejectNoticeView(data) {
    return request({
        url: '/manager/constructionView/batchReject',
        method: 'put',
        data: data
    });
}

// 时间编辑
export function updateTime(data) {
    return request({
        url: '/manager/constructionView/updateTime',
        method: 'put',
        data: data
    });
}

// 导出日养施工通知单审核列表
export function exportList() {
    return request({
        url: '/manager/constructionView/export',
        method: 'post'
    });
}



// 修改下发时间
export function updateIssuanceTime(data) {
  return request({
    url: '/manager/construction/updateIssuanceTime',
    method: 'post',
    data
  });
}


// 已下发撤回
export function rejectConstruction(id) {
  return request({
    url: `/manager/constructionView/rejectConstruction/${id}`,
    method: 'get',
  });
}
