import request from '@/utils/request'

// 查询涉灾隐患点—桥梁列表
export function listBridgeHazards(query) {
  return request({
    url: '/middleData/bridgeHazards/list',
    method: 'post',
    params: {pageNum: query.pageNum, pageSize: query.pageSize},
    data: query
  })
}

// 查询涉灾隐患点—桥梁详细
export function getBridgeHazards(id) {
  return request({
    url: '/middleData/bridgeHazards/get/' + id,
    method: 'get'
  })
}

// 新增涉灾隐患点—桥梁
export function addBridgeHazards(data) {
  return request({
    url: '/middleData/bridgeHazards/add',
    method: 'post',
    data: data
  })
}

// 修改涉灾隐患点—桥梁
export function updateBridgeHazards(data) {
  return request({
    url: '/middleData/bridgeHazards/edit',
    method: 'put',
    data: data
  })
}

// 删除
export function delData(data) {
  return request({
    url: `/middleData/bridgeHazards/delete`,
    method: 'delete',
    data: data.idList,
  });
}

// 批量审核
export function batchAudit(data) {
  return request({
    url: '/middleData/bridgeHazards/batchAudit',
    method: 'post',
    data: data
  })
}
