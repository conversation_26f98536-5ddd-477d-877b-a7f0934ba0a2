import request from '@/utils/request'

// 查询涉灾隐患点—隧道列表
export function listTunnelHazards(query) {
  return request({
    url: '/middleData/tunnelHazards/list',
    method: 'post',
    params: {pageNum: query.pageNum, pageSize: query.pageSize},
    data: query
  })
}

// 查询涉灾隐患点—隧道详细
export function getTunnelHazards(id) {
  return request({
    url: '/middleData/tunnelHazards/get/' + id,
    method: 'get'
  })
}

// 新增涉灾隐患点—隧道
export function addTunnelHazards(data) {
  return request({
    url: '/middleData/tunnelHazards/add',
    method: 'post',
    data: data
  })
}

// 修改涉灾隐患点—隧道
export function updateTunnelHazards(data) {
  return request({
    url: '/middleData/tunnelHazards/edit',
    method: 'put',
    data: data
  })
}

// 删除
export function delData(data) {
  return request({
    url: `/middleData/tunnelHazards/delete`,
    method: 'delete',
    data: data.idList,
  });
}

// 批量审核
export function batchAudit(data) {
  return request({
    url: '/middleData/tunnelHazards/batchAudit',
    method: 'post',
    data: data
  })
}
