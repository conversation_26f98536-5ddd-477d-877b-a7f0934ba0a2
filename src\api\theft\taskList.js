import request from '@/utils/request'
// 查询养护工程施工单列表(分页) [任务单编制模块共用的查询接口]
/**
 * 查询养护工程施工单列表(分页) [任务单编制模块共用的查询接口]
 * @param {Object} data - 请求参数
 * @returns {Promise} - 返回 Promise 对象
 */
export function listConstruction(data) {
  return request({
    url: '/manager/theft/construction/list',
    method: 'post',
    data: data
  });
}

// 查询养护工程施工单列表(分页) [任务单审核,任务单签发,任务单接收,任务单开工登记，完工模块共用的查询接口]
/**
 * 查询养护工程施工单列表(分页) [任务单审核,任务单签发,任务单接收,任务单开工登记，完工模块共用的查询接口]
 * @param {Object} data - 请求参数
 * @returns {Promise} - 返回 Promise 对象
 */
export function pendingListConstruction(data) {
  return request({
    url: '/manager/theft/construction/pending/list',
    method: 'post',
    data: data
  });
}

// 查询养护工程施工单附件信息(分页)
/**
 * 查询养护工程施工单附件信息(分页)
 * @param {Object} params - 请求参数
 * @returns {Promise} - 返回 Promise 对象
 */
export function fileListConstruction(id) {
  return request({
    url: `/manager/theft/construction/get/file/${id}`,
    method: 'get',
  });
}

// 查询养护工程施工单列表(不分页)
/**
 * 查询养护工程施工单列表(不分页)
 * @returns {Promise} - 返回 Promise 对象
 */
export function listAllConstruction() {
  return request({
    url: '/manager/theft/construction/listAll',
    method: 'get'
  });
}

// 根据id查询养护工程施工单数据
/**
 * 根据id查询养护工程施工单数据
 * @param {string} id - 施工单ID
 * @returns {Promise} - 返回 Promise 对象
 */
export function getConstructionById(id) {
  return request({
    url: `/manager/theft/construction/get/${id}`,
    method: 'get'
  });
}

// 新增养护工程施工单
/**
 * 新增养护工程施工单
 * @param {Object} data - 请求参数
 * @returns {Promise} - 返回 Promise 对象
 */
export function addConstruction(data) {
  return request({
    url: '/manager/theft/construction/add',
    method: 'post',
    data: data
  });
}

// 修改养护工程施工单
/**
 * 修改养护工程施工单
 * @param {Object} data - 请求参数
 * @returns {Promise} - 返回 Promise 对象
 */
export function editConstruction(data) {
  return request({
    url: '/manager/theft/construction/edit',
    method: 'put',
    data: data
  });
}

// 更新养护工程施工单是否完结
/**
 * 更新养护工程施工单是否完结
 * @param {Object} data - 请求参数
 * @returns {Promise} - 返回 Promise 对象
 */
export function updateIsEndConstruction(data) {
  return request({
    url: '/manager/theft/construction/isEnd',
    method: 'post',
    data: data
  });
}

// 新增养护工程施工单附件
/**
 * 新增养护工程施工单附件
 * @param {Object} data - 请求参数
 * @returns {Promise} - 返回 Promise 对象
 */
export function addFileConstruction(data) {
  return request({
    url: '/manager/theft/construction/add/file',
    method: 'post',
    data: data
  });
}

// 删除养护工程施工单附件
/**
 * 删除养护工程施工单附件
 * @param {string} id - 附件ID
 * @returns {Promise} - 返回 Promise 对象
 */
export function deleteFileConstruction(id) {
  return request({
    url: `/manager/theft/construction/delete/file/${id}`,
    method: 'put'
  });
}

// 删除养护工程施工单
/**
 * 删除养护工程施工单
 * @param {string} id - 施工单ID
 * @returns {Promise} - 返回 Promise 对象
 */
export function deleteConstruction(id) {
  return request({
    url: `/manager/theft/construction/delete/${id}`,
    method: 'delete'
  });
}

// 养护工程施工单提交与审核
/**
 * 养护工程施工单提交与审核
 * @param {Object} data - 请求参数
 * @returns {Promise} - 返回 Promise 对象
 */
export function processConstruction(data) {
  return request({
    url: '/manager/theft/construction/process',
    method: 'post',
    data: data
  });
}

// 养护工程施工单审核节点信息
/**
 * 养护工程施工单审核节点信息
 * @param {Object} data - 请求参数
 * @returns {Promise} - 返回 Promise 对象
 */
export function getNodeInfo(data) {
  return request({
    url: '/manager/theft/construction/node/info',
    method: 'post',
    data: data
  });
}

// 施工单查看列表(分页) [设计，施工，检测任务单查看模块共用的查询接口]
/**
 * 施工单查看列表(分页) [设计，施工，检测任务单查看模块共用的查询接口]
 * @param {Object} data - 请求参数
 * @returns {Promise} - 返回 Promise 对象
 */
export function viewConstruction(data) {
  return request({
    url: '/manager/theft/construction/view',
    method: 'post',
    data: data
  });
}


// 根据施工单id查询被损被盗施工单明细列表(分页)
export function viewConstructionDetail(data) {
  return request({
    url: '/manager/theft/construction/detail/list',
    method: 'post',
    data: data
  });
}



// 监理单位接收列表
export function supPendingList(data) {
  return request({
    url: '/manager/theft/construction/sup/pending/list',
    method: 'post',
    data,
  });
}


// 设计单位接收列表
export function designPendingList(data) {
  return request({
    url: '/manager/theft/construction/design/pending/list',
    method: 'post',
    data,
  });
}



//  养护工程施工单设计单位接收
export function designProcess(data) {
  return request({
    url: '/manager/theft/construction/design/process',
    method: 'post',
    data,
  });
}


// 养护工程施工单监理单位接收
export function supProcess(data) {
  return request({
    url: '/manager/theft/construction/sup/process',
    method: 'post',
    data,
  });
}


// 设计任务单查看(分页)
export function viewDesignConstructionList(data) {
  return request({
    url: '/manager/theft/construction/design/view',
    method: 'post',
    data,
  });
}

// 施工单查看列表(分页)
export function viewSupConstructionList(data) {
  return request({
    url: '/manager/theft/construction/sup/view',
    method: 'post',
    data,
  });
}

/**
 * 施工单预览
 * @param {string} id - 施工单ID
 * @returns {Promise} - 返回 Promise 对象
 */
export function previewConstruction(id) {
  return request({
    url: `/manager/theft/construction/preview?id=${id}`,
    method: 'get'
  });
}

/**
 * 检测任务单预览
 * @param {string} id - 检测任务单ID
 * @returns {Promise} - 返回 Promise 对象
 */
export function previewCheck(id) {
  return request({
    url: `/manager/theft/construction/check/preview?id=${id}`,
    method: 'get'
  });
}

/**
 * 设计任务单预览
 * @param {string} id - 设计任务单ID
 * @returns {Promise} - 返回 Promise 对象
 */
export function previewDesign(id) {
  return request({
    url: `/manager/theft/construction/design/preview?id=${id}`,
    method: 'get'
  });
}

/**
 * 监理任务单预览
 * @param {string} id - 监理任务单ID
 * @returns {Promise} - 返回 Promise 对象
 */
export function previewSup(id) {
  return request({
    url: `/manager/theft/construction/sup/preview?id=${id}`,
    method: 'get'
  });
}

// 根据工程id,合同id查询养护工程费用明细列表(不分页)
export function getProDetail(data) {
  return request({
    url: '/manager/theft/project/detail/list/data',
    method: 'get',
    params: data
  });
}

// 根据id查询养护工程施工单操作记录
export function getTaskRecord(id) {
  return request({
    url: `/manager/theft/construction/get/record/${id}`,
    method: 'get',
  });
}

export function completedList(data) {
  return request({
    url: '/manager/theft/construction/completed/list',
    method: 'post',
    data: data
  });
}

export function updateTheftIssueDate(data) {
  return request({
    url: '/manager/theft/construction/update/issueDate',
    method: 'post',
    data: data
  })
}



export function withdraw(data) {
  return request({
    url: '/manager/theft/construction/withdraw',
    method: 'post',
    data
  });
}
