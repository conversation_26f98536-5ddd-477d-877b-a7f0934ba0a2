import request from '@/utils/request'

// 头模板管理新增
export function saveReportHeaderTemplate(query) {
  return request({
    url: '/jgjcadmin/api/evaluationReport/reportHeaderTemplate/save',
    method: 'post',
    data: query
  })
}

// 头模板管理编辑
export function updateReportHeaderTemplate(query) {
  return request({
    url: '/jgjcadmin/api/evaluationReport/reportHeaderTemplate/update',
    method: 'post',
    data: query
  })
}

// 头模板管理删除
export function deleteReportHeaderTemplate(query) {
  return request({
    url: '/jgjcadmin/api/evaluationReport/reportHeaderTemplate/delete',
    method: 'post',
    data: query
  })
}

// 头模板管理查询
export function getReportHeaderTemplatePage(query) {
  return request({
    url: '/jgjcadmin/api/evaluationReport/reportHeaderTemplate/page',
    method: 'get',
    params: query
  })
}
