import request from '@/utils/request'

export function saveAlertRule(data) {
  return request({
    url: '/jgjcadmin/api/healthassessment/alertruletype/save',
    method: 'post',
    data
  });
}

export function updateAlertRule(data) {
  return request({
    url: '/jgjcadmin/api/healthassessment/alertruletype/update',
    method: 'post',
    data
  });
}

export function deleteAlertRule(data) {
  return request({
    url: '/jgjcadmin/api/healthassessment/alertruletype/delete',
    method: 'post',
    data
  });
}

export function getAlertRulePage(data) {
  return request({
    url: '/jgjcadmin/api/healthassessment/alertruletype/page',
    method: 'get',
    params: data
  });
}
