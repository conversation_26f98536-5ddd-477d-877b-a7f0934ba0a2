import request from '@/utils/request'

// 查询日养施工通知单确认列表(分页)
export function listConfirm(data) {
    return request({
        url: '/manager/constructionConfirm/list',
        method: 'post',
        data: data
    });
}

// 查询日养施工通知单确认详情
export function getConstructionDetail(id) {
    return request({
        url: `/manager/constructionConfirm/getConstructionDetail/${id}`,
        method: 'get'
    });
}

// 接收
export function receiveConfirm(data) {
    return request({
        url: '/manager/constructionConfirm/receive',
        method: 'put',
        data: data
    });
}

// 批量接收
export function batchReceiveConfirm(data) {
    return request({
        url: '/manager/constructionConfirm/batchReceive',
        method: 'put',
        data: data
    });
}

// 驳回
export function rejectConfirm(data) {
    return request({
        url: '/manager/constructionConfirm/reject',
        method: 'put',
        data: data
    });
}

// 批量驳回
export function batchRejectConfirm(data) {
    return request({
        url: '/manager/constructionConfirm/batchReject',
        method: 'put',
        data: data
    });
}
