import request from '@/utils/request'

// 查询列表
export function getListPage(data) {
  return request({
    url: '/baseData/longitudinal/slope/getListPage',
    method: 'post',
    data
  })
}

// 增
export function addSlope(data) {
  return request({
    url: "/baseData/longitudinal/slope/add",
    method: "post",
    data,
  });
}

// 删
export function delSlope(data) {
  return request({
    url: '/baseData/longitudinal/slope/delete/'+data,
    method: 'delete',
  })
}

// 改
export function updateSlope(data) {
  return request({
    url: '/baseData/longitudinal/slope/edit',
    method: 'put',
    data: data
  })
}

// 查
export function getSlope(id) {

  return request({
    url: `/baseData/longitudinal/slope/get/${id}`,
    method: "get",
  });
}

// 暂存
export function tempSlope(data) {
  return request({
    url: "/baseData/longitudinal/slope/temp/add",
    method: "post",
    data,
  });
}