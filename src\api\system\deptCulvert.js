import request from '@/utils/request'

// 查询涵洞资产权限列表
export function listDeptCulvert(query) {
  return request({
    url: '/system/deptCulvert/list',
    method: 'get',
    params: query
  })
}

// 根据条件查询已关联桥梁ID
export function getDefaultSelected(query) {
  return request({
    url: '/system/deptCulvert/getDefaultSelected',
    method: 'get',
    params: query
  })
}

// 批量新增
export function addAllDeptCulvert(data) {
  return request({
    url: '/system/deptCulvert/addAllDeptCulvert',
    method: 'post',
    data: data
  })
}

// 查询涵洞资产权限详细
export function getDeptCulvert(id) {
  return request({
    url: '/system/deptCulvert/get/' + id,
    method: 'get'
  })
}

// 新增涵洞资产权限
export function addDeptCulvert(data) {
  return request({
    url: '/system/deptCulvert/add',
    method: 'post',
    data: data
  })
}

// 修改涵洞资产权限
export function updateDeptCulvert(data) {
  return request({
    url: '/system/deptCulvert/edit',
    method: 'put',
    data: data
  })
}

// 删除涵洞资产权限
export function delDeptCulvert(id) {
  return request({
    url: '/system/deptCulvert/delete/' + id,
    method: 'delete'
  })
}
