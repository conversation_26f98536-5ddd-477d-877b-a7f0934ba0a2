import request from '@/utils/request'

// 新增灾害数据（水毁）
export function createRiskWaterDestruction(query) {
  return request({
    url: '/disaster/disaster-risk/water-destruction/add',
    method: 'post',
    data: query
  })
}

// 更新灾害数据（水毁）
export function updateRiskWaterDestruction(query) {
  return request({
    url: '/disaster/disaster-risk/water-destruction/edit',
    method: 'put',
    data: query
  })
}