import request from '@/utils/request'

// 查询监测类型列表
export function listMonitoringType(query) {
  return request({
    url: '/jgjc-manager/api/console/jc/monitoringType/findPage',
    method: 'post',
    data: query
  })
}

// 查询监测类型详细
export function getMonitoringType(id) {
  return request({
    url: '/jgjc-manager/api/console/jc/monitoringType/findById?id=' + id,
    method: 'get'
  })
}

// 新增监测类型
export function addMonitoringType(data) {
  return request({
    url: '/jgjc-manager/api/console/jc/monitoringType/create',
    method: 'post',
    data: data
  })
}

// 修改监测类型
export function updateMonitoringType(data) {
  return request({
    url: '/jgjc-manager/api/console/jc/monitoringType/modify',
    method: 'post',
    data: data
  })
}

// 删除监测类型
export function delMonitoringType(id) {
  return request({
    url: '/jgjc-manager/api/console/jc/monitoringType/removeById?id=' + id,
    method: 'delete'
  })
}
// 批量删除监测类型
export function delMonitoringTypeBatch(ids) {
  return request({
    url: '/jgjc-manager/api/console/jc/monitoringType/batchRemove',
    method: 'post',
    data: ids
  })
}
// 获取所有监测类型
export function findAllMonitoringType() {
  return request({
    url: '/jgjc-manager/api/console/jc/monitoringType/findAll',
    method: 'get'
  })
}
