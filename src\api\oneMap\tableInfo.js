import request from "@/utils/request";

/**
 * 查询数据库表列表
 * @param {*} query {tableName:表名称,tableComment:	表描述}
 * @returns
 */
export function getTableList(query) {
  return request({
    url: "/oneMap/tableInfo/getTableList",
    method: "get",
    params: query,
  });
}

/**
 * 查询数据库表字段信息
 * @param {*} query {tableName:表名称,columnName:列名称,columnType:列类型}
 * @returns
 */
export function getTableColumnList(query) {
  return request({
    url: "/oneMap/tableInfo/getTableColumnList",
    method: "get",
    params: query,
  });
}

// 公共 请求 获取一张图 表格数据等数据
export function publicRequest(url, method, params) {
  return request({
    url,
    method: method,
    params: method == "get" ? params : null,
    data: method == "post" || method == "put" ? params : null,
    notVerifyDuplicates: true
  });
}

// 导出表格数据
export function exportTable(url, method, params) {
  return request({
    url,
    method: method,
    params,
    notVerifyDuplicates: true
  });
}
