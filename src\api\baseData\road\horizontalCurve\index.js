import request from '@/utils/request'

// 查询列表
export function getListPage(data) {
  return request({
    url: '/baseData/horizontalCurve/getListPage',
    method: 'post',
    data
  })
}

// 增
export function addHorizontalCurve(data) {
  return request({
    url: "/baseData/horizontalCurve/add",
    method: "post",
    data,
  });
}

// 删
export function delHorizontalCurve(data) {
  return request({
    url: '/baseData/horizontalCurve/delete',
    method: 'delete',
    data
  })
}

// 改
export function updateHorizontalCurve(data) {
  return request({
    url: '/baseData/horizontalCurve/edit',
    method: 'put',
    data: data
  })
}

// 查
export function getHorizontalCurve(id) {
  return request({
    url: `/baseData/horizontalCurve/get/${id}`,
    method: "get",
  });
}

// 暂存
export function tempHorizontalCurve(data) {
  return request({
    url: "/baseData/horizontalCurve/temp/add",
    method: "post",
    data,
  });
}