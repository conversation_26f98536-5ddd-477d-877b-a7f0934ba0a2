import request from '@/utils/request'

// 查询流程数据（分页）
export function queryPageProcess(query) {
  return request({
    url: '/workFlow/process-definition/list',
    method: 'get',
    params: query
  })
}

// 查询流程图数据
export function queryProcessChart(query) {
  return request({
    url: '/workFlow/process-definition/getDefinitionXML',
    method: 'get',
    params: query
  })
}

// 部署流程数据
export function createProcess(query) {
  return request({
    url: '/workFlow/process-definition/addDeploymentByString',
    method: 'post',
    // headers: {
    //   'Content-Type': 'application/x-www-form-urlencoded'
    // },
    headers: { 'Content-Type': 'application/xml' },
    data: query
  })
}


// 删除流程数据
export function deleteProcess(id) {
  return request({
    url: `/workFlow/process-definition/remove/${id}`,
    method: 'delete',
  })
}

// 流程激活/挂起
export function updateProcessStatus(query) {
  return request({
    url: '/workFlow/process-definition/suspendOrActiveApply',
    method: 'post',
    data: query
  })
}