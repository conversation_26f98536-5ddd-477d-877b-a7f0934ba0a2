import request from '@/utils/request'

// 声光报警器管理
export function getAlertMachinePage(params) {
	return request({
		url: '/jgjcadmin/api/basedata/alert-machine/page',
		method: 'post',
		data: params
	})
}

export function deleteAlertMachine(data) {
	return request({
		url: '/jgjcadmin/api/basedata/alert-machine/delete',
		method: 'post',
		data
	})
}

export function saveAlertMachine(data) {
	return request({
		url: '/jgjcadmin/api/basedata/alert-machine/save',
		method: 'post',
		data
	})
}

export function updateAlertMachine(data) {
	return request({
		url: '/jgjcadmin/api/basedata/alert-machine/update',
		method: 'post',
		data
	})
}

// 声光报警记录管理
export function getAlertRecordPage(params) {
	return request({
		url: '/jgjcadmin/api/basedata/alert-machine-record/page',
		method: 'post',
		data: params
	})
}

export function deleteAlertRecord(data) {
	return request({
		url: '/jgjcadmin/api/basedata/alert-machine-record/delete',
		method: 'post',
		data
	})
}

export function saveAlertRecord(data) {
	return request({
		url: '/jgjcadmin/api/basedata/alert-machine-record/save',
		method: 'post',
		data
	})
}

export function updateAlertRecord(data) {
	return request({
		url: '/jgjcadmin/api/basedata/alert-machine-record/update',
		method: 'post',
		data
	})
}

// 结构物树管理
export function getJCDomainTree() {
	return request({
		url: '/jgjcadmin/api/flash/structure/getJCDomainTree',
		method: 'post'
	})
}

export function getSensorDeviceTreeNode(data) {
	return request({
		url: '/jgjcadmin/api/basedata/sensor-management/getSensorDeviceTreeNode',
		method: 'post',
		data
	})
}
