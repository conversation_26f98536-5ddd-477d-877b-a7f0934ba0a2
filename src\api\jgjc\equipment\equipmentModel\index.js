import request from '@/utils/request';


export function addDeviceModel(data) {
  return request({
    url: '/jgjcadmin/api/basedata/devicemodel/save',
    method: 'post',
    data
  });
}

export function updateDeviceModel(data) {
  return request({
    url: '/jgjcadmin/api/basedata/devicemodel/update',
    method: 'post',
    data
  });
}

export function deleteDeviceModel(data) {
  return request({
    url: '/jgjcadmin/api/basedata/devicemodel/delete',
    method: 'post',
    data
  });
}

export function listDeviceModels(data) {
  return request({
    url: '/jgjcadmin/api/basedata/devicemodel/page',
    method: 'post',
    data
  });
}

export function batchAddDeviceModels(data) {
  return request({
    url: '/jgjcadmin/api/basedata/devicemodel/batchAdd',
    method: 'post',
    data
  });
}
