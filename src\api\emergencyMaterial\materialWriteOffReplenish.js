import request from '@/utils/request'

// 应急物资核销补充单审核节点信息
export function getNodeInfo(data) {
  return request({
    url: '/manager/emergency/material/verification/supplement/node/info',
    method: 'post',
    data
  })
}

// 应急物资核销补充单申请列表
export function getWriteOffReplenishList(data) {
  return request({
    url: '/manager/emergency/material/verification/supplement/list',
    method: 'post',
    data
  })
}

// 删除申请入库单
export function delWriteOffReplenish(id) {
  return request({
    url: `/manager/emergency/material/verification/supplement/delete/${id}`,
    method: 'delete',
  })
}

//  应急物资入库单审核
export function submitWriteOffReplenish(data) {
  return request({
    url: '/manager/emergency/material/verification/supplement/process',
    method: 'post',
    data
  })
}

// 新增入库单时查询核销记录
export function getWriteOffRecord(data) {
  return request({
    url: '/manager/emergency/material/verification/data',
    method: 'post',
    data
  })
}

// 新增核销补充单
export function addWriteOffReplenish(data) {
  return request({
    url: '/manager/emergency/material/verification/supplement/add',
    method: 'post',
    data
  })
}

// 修改核销补充单
export function editWriteOffReplenish(data) {
  return request({
    url: '/manager/emergency/material/verification/supplement/edit',
    method: 'put',
    data
  })
}

// 根据id查询核销补充单数据
export function getDetailWriteOffReplenish(id) {
  return request({
    url: `/manager/emergency/material/verification/supplement/get/${id}`,
    method: 'get'
  })
}

// 根据物资库id与入库单id查询应急物资核销补充单明细列表
export function getWriteOffReplenishDetailList(data) {
  return request({
    url: '/manager/emergency/material/inbound/detail/list',
    method: 'post',
    data
  })
}

// 获取入库单审核列表
export function getWriteOffReplenishAuditList (data) {
  return request({
    url: '/manager/emergency/material/verification/supplement/pending/list',
    method: 'post',
    data
  })
}

// 获取入库单结果列表
export function getWriteOffReplenishResultList(data) {
  return request({
    url: '/manager/emergency/material/verification/supplement/view',
    method: 'post',
    data
  })
}


// 报表
export function previewReport(id) {
  return request({
    url: `/manager/emergency/material/verification/supplement/report/preview?id=${id}`,
    method: 'get',
  })
}
