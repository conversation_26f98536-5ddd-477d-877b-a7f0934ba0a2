import request from '@/utils/request'

// 查询列表
export function getListPage(data) {
  return request({
    url: '/baseData/route/snowCovered/getListPage',
    method: 'post',
    data
  })
}

// 增
export function addCovered(data) {
  return request({
    url: "/baseData/route/snowCovered/add",
    method: "post",
    data,
  });
}

// 暂存
export function tempCovered(data) {
  return request({
    url: "/baseData/route/snowCovered/temp/add",
    method: "post",
    data,
  });
}

// 删
export function delCovered(data) {
  return request({
    url: '/baseData/route/snowCovered/delete',
    method: 'delete',
    data
  })
}

// 改
export function updateCovered(data) {
  return request({
    url: '/baseData/route/snowCovered/edit',
    method: 'post',
    data: data
  })
}

// 查
export function getCovered(id) {

  return request({
    url: `/baseData/route/snowCovered/get/${id}`,
    method: "get",
  });
}

