import request from '@/utils/request'

// 查询报价体系详情数据列表
export function listMaiconunitrelation(query) {
  return request({
    url: '/patrol/maiconunitrelation/list',
    method: 'get',
    params: query
  })
}

// 查询报价体系详情数据详细
export function getMaiconunitrelation(id) {
  return request({
    url: '/patrol/maiconunitrelation/get/' + id,
    method: 'get'
  })
}

// 新增报价体系详情数据
export function addMaiconunitrelation(data) {
  return request({
    url: '/patrol/maiconunitrelation/add',
    method: 'post',
    data: data
  })
}

// 修改报价体系详情数据
export function updateMaiconunitrelation(data) {
  return request({
    url: '/patrol/maiconunitrelation/edit',
    method: 'put',
    data: data
  })
}

// 删除报价体系详情数据
export function delMaiconunitrelation(id) {
  return request({
    url: '/patrol/maiconunitrelation/delete/' + id,
    method: 'delete'
  })
}
