import request from '@/utils/request'

// 查询隧道机电巡查明细列表
export function listDeviceCheckDetail(query) {
  return request({
    url: '/patrol/deviceCheckDetail/list',
    method: 'get',
    params: query
  })
}

// 查询隧道机电巡查明细详细
export function getDeviceCheckDetail(id) {
  return request({
    url: '/patrol/deviceCheckDetail/get/' + id,
    method: 'get'
  })
}

// 新增隧道机电巡查明细
export function addDeviceCheckDetail(data) {
  return request({
    url: '/patrol/deviceCheckDetail/add',
    method: 'post',
    data: data
  })
}

// 修改隧道机电巡查明细
export function updateDeviceCheckDetail(data) {
  return request({
    url: '/patrol/deviceCheckDetail/edit',
    method: 'put',
    data: data
  })
}

// 删除隧道机电巡查明细
export function delDeviceCheckDetail(id) {
  return request({
    url: '/patrol/deviceCheckDetail/delete/' + id,
    method: 'delete'
  })
}
