import request from '@/utils/request'

// 查询考核项目List
export function queryExamineProjectList(query) {
  return request({
    url: '/examine/examine-project/list',
    method: 'get',
    params: query
  })
}

export function getExamineTypeByType(query) {
  return request({
    url: '/examine/examine-project/getExamineTypeByType',
    method: 'get',
    params: query
  })
}

export function getExamineTypeList() {
  return request({
    url: '/examine/examine-project/getExamineTypeList',
    method: 'get'
  })
}

// 新增考核项目
export function addExamineProject(data) {
  return request({
    url: '/examine/examine-project/add',
    method: 'post',
    data: data
  })
}

// 编辑考核项目
export function editExamineProject(data) {
  return request({
    url: '/examine/examine-project/edit',
    method: 'put',
    data: data
  })
}

// 删除考核项目
export function deleteExamineProject(ids) {
  return request({
    url: `/examine/examine-project/${ids}`,
    method: 'delete',
  })
}

// 考核项目下发
export function issuedProject(projectId) {
  return request({
    url: '/examine/examine-project/issued',
    method: 'post',
    params: {projectId}
  })
}

export function getSectTaskStatusList(projectId) {
  return request({
    url: '/examine/examine-project/getProjectWithTaskInfo',
    method: 'get',
    params: {projectId}
  })
}
