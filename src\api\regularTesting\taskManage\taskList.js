import request from '@/utils/request'

//定检任务单查看列表(分页)
export function viewConstructionList(data) {
  return request({
    url: '/manager/check/construction/list',
    method: 'post',
    data,
  });
}

//获取定检任务单列表(分页)
export function getConstructionList(data) {
  return request({
    url: '/manager/check/construction/pending/list',
    method: 'post',
    data,
  });
}

// 新增定检任务单
export function addConstruction(data) {
  return request({
    url: '/manager/check/construction/add',
    method: 'post',
    data,
  });
}

// 修改定检任务单
export function editConstruction(data) {
  return request({
    url: '/manager/check/construction/edit',
    method: 'put',
    data,
  });
}

// 更改定检任务单状态
export function changeConstructionStatus(data) {
  return request({
    url: '/manager/check/construction/process',
    method: 'post',
    data,
  });
}

// 获取定期检测任务单审核节点信息
export const getConstructionNodeInfo = (data) => {
  return request({
    url: '/manager/check/construction/node/info',
    method: 'post',
    data,
  });
}

// 删除定检任务单
export function deleteConstruction(id) {
  return request({
    url: `/manager/check/construction/delete/${id}`,
    method: 'delete',
  });
}

// 获取定检任务单详情
export function getConstruction(id) {
  return request({
    url: `/manager/check/construction/get/${id}`,
    method: 'get',
  });
}

// 查询定检任务单附件信息
export function getConstructionFileList(data) {
  return request({
    url: '/manager/check/construction/file/list',
    method: 'post',
    data,
  });
}

// 新增定检任务单附件
export function addConstructionFile(data) {
  return request({
    url: '/manager/check/construction/file/add',
    method: 'post',
    data,
  });
}

// 获取定检任务单子目列表(分页)
export function getConstructionDetailList(data) {
  return request({
    url: '/manager/check/construction/detail/list',
    method: 'post',
    data,
  });
}

// 获取定检任务单子目列表(不分页)
export function getConstructionDetailListAll(params) {
  return request({
    url: '/manager/check/construction/detail/listAll',
    method: 'get',
    params,
  });
}

// 获取预览信息
export function getPreviewInfo(params) {
  return request({
    url: '/manager/check/construction/preview',
    method: 'get',
    params,
  });
}

export function getTaskOperationRecord(data) {
  return request({
    url: '/manager/check/construction/operation/record',
    method: 'post',
    data,
  });
}

// 根据项目id与合同id查询合同清单
export function getConListByProIdAndConId(data) {
  return request({
    url: '/manager/check/project/detail/list/data',
    method: 'post',
    data,
  });
}

export function withdraw(data) {
  return request({
    url: '/manager/check/construction/withdraw',
    method: 'post',
    data: data
  })
}
