import request from '@/utils/request'

// 查询部门与部门关联列表
export function listSub(query) {
  return request({
    url: '/system/sub/list',
    method: 'get',
    params: query
  })
}

// 查询部门与部门关联详细
export function getSub(deptId) {
  return request({
    url: '/system/sub/' + deptId,
    method: 'get'
  })
}

// 新增部门与部门关联
export function addSub(data) {
  return request({
    url: '/system/sub',
    method: 'post',
    data: data
  })
}

// 修改部门与部门关联
export function updateSub(data) {
  return request({
    url: '/system/sub',
    method: 'put',
    data: data
  })
}

// 删除部门与部门关联
export function delSub(deptId) {
  return request({
    url: '/system/sub/' + deptId,
    method: 'delete'
  })
}
