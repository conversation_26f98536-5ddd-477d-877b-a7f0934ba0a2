import request from '@/utils/request'

// 查询列表
export function getListPage(data) {
  return request({
    url: '/baseData/facility/getListPage',
    method: 'post',
    data
  })
}

// 增
export function addFacility(data) {
  return request({
    url: "/baseData/facility/add",
    method: "post",
    data,
  });
}

// 删
export function delFacility(data) {
  return request({
    url: '/baseData/facility/delete/'+data,
    method: 'delete',
  })
}

// 改
export function updateFacility(data) {
  return request({
    url: '/baseData/facility/edit',
    method: 'put',
    data: data
  })
}

// 查
export function getFacility(id) {

  return request({
    url: `/baseData/facility/get/${id}`,
    method: "get",
  });
}

// 暂存
export function tempFacility(data) {
  return request({
    url: "/baseData/facility/temp/add",
    method: "post",
    data,
  });
}

//查询资产子类
export function getAssetSubclass(params) {
  return request({
    url: "/baseData/asset/type/getList",
    method: "get",
    params
  });
}

//查动态数据
export function getDynamicData(params) {
  return request({
    url: "/baseData/dynamic/fields/getList",
    method: "get",
    params
  });
}


//交通安全设施历史数据(附属设施数据、路面数据、绿化数据)

export function getFacilityHisListPage(data) {
  return request({
    url: '/baseData/facility/his/getListPage',
    method: 'post',
    data
  })
}