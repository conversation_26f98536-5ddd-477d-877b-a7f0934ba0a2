import request from '@/utils/request'

// 获取ID获取开工登记报价方法
export function getRegisterMethods(id) {
  return request({
    url: `/manager/check/construction/register/get/${id}`,
    method: 'get',
  });
}

// 获取ID获取开工登记报价方法
export function getFinishedMethods(id) {
  return request({
    url: `/manager/check/construction/finished/get/${id}`,
    method: 'get',
  });
}


export function getConstructionRecord(data) {
  return request({
    url: `/manager/check/construction/node/info`,
    method: 'post',
    data
  });
}

// 列表获取开工登记子目（分页）
export function getRegisterDetailList(data) {
  return request({
    url: `/manager/check/construction/register/method/list`,
    method: 'post',
    data
  });
}

// 列表获取完工登记子目（分页）
export function getFinishedDetailList(data) {
  return request({
    url: `/manager/check/construction/finished/method/list`,
    method: 'post',
    data
  });
}

export function updateFinishedDetail(data) {
  return request({
    url: '/manager/check/construction/finished/edit',
    method: 'put',
    data
  })
}

// 获取签证单预览信息
export function getVisaPreviewInfo(params) {
  return request({
    url: '/manager/check/construction/visa/preview',
    method: 'get',
    params,
  });
}

// 获取维修档案预览信息
export function getMaintainPreviewInfo(params) {
  return request({
    url: '/manager/check/construction/maintain/preview',
    method: 'get',
    params,
  });
}

// 获取修复反馈预览信息
export function getRepairPreviewInfo(params) {
  return request({
    url: '/manager/check/construction/repair/preview',
    method: 'get',
    params,
  });
}

// 施工档案下载
export function downloadMaintainZip(params) {
  return request({
    url: '/manager/check/construction/file/download',
    method: 'get',
    params,
  });
}
