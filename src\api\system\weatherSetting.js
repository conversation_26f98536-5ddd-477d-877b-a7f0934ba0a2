import request from '@/utils/request'

// 查询部门与部门关联列表
export function getListPage(data) {
  return request({
    url: '/oneMap/weather/warning/level/getListPage',
    method: 'post',
    data: data
  })
}

// 查询短信模板列表
export function getMsgListPage(data) {
  return request({
    url: '/oneMap/weather/sms/template/getListPage',
    method: 'post',
    data: data
  })
}

// 查询短信模板列表
export function getMsgList(data) {
  return request({
    url: '/oneMap/weather/sms/template/getList',
    method: 'post',
    data: data
  })
}

// 更新天气预报预警级别数据
export function edit(data) {
  return request({
    url: '/oneMap/weather/warning/level/edit',
    method: 'put',
    data: data
  })
}

// 新增天气预报预警级别数据
export function add(data) {
  return request({
    url: '/oneMap/weather/warning/level/add',
    method: 'post',
    data: data
  })
}

// 删除天气预报预警级别数据
export function del(ids) {
  return request({
    url: `/oneMap/weather/warning/level/delete/${ids}`,
    method: 'delete',
  })
}

// 新增短信模板数据
export function addMsg(data) {
  return request({
    url: '/oneMap/weather/sms/template/add',
    method: 'post',
    data: data
  })
}

// 更新短信模板数据
export function editMsg(data) {
  return request({
    url: '/oneMap/weather/sms/template/edit',
    method: 'put',
    data: data
  })
}

// 删除短信模板数据
export function delMsg(ids) {
  return request({
    url: `/oneMap/weather/sms/template/delete/${ids}`,
    method: 'delete',
  })
}
