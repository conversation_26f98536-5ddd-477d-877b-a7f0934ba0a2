import request from '@/utils/request'

// 查询巡查频率配置列表
export function listFrequencySettings(query) {
  return request({
    url: '/patrol/frequencySettings/list',
    method: 'get',
    params: query
  })
}

// 查询巡查频率配置详细
export function getFrequencySettings(id) {
  return request({
    url: '/patrol/frequencySettings/get/' + id,
    method: 'get'
  })
}

// 新增巡查频率配置
export function addFrequencySettings(data) {
  return request({
    url: '/patrol/frequencySettings/add',
    method: 'post',
    data: data
  })
}

// 修改巡查频率配置
export function updateFrequencySettings(data) {
  return request({
    url: '/patrol/frequencySettings/edit',
    method: 'put',
    data: data
  })
}

// 批量修改巡查频率配置
export function updateAll(data) {
  return request({
    url: '/patrol/frequencySettings/editAll',
    method: 'put',
    data: data
  })
}

// 删除巡查频率配置
export function delFrequencySettings(id) {
  return request({
    url: '/patrol/frequencySettings/delete/' + id,
    method: 'delete'
  })
}
