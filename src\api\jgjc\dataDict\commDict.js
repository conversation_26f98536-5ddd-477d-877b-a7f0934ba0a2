import request from '@/utils/request'

// 查询字典项列表
export function listDictData(query) {
  return request({
    url: '/jgjcadmin/api/dictData/page',
    method: 'get',
    params: query
  })
}

// 查询字典项详细
export function getDictData(id) {
  return request({
    url: '/jgjcadmin/api/dictData/' + id,
    method: 'get'
  })
}

// 新增字典项
export function addDictData(data) {
  return request({
    url: '/jgjcadmin/api/dictData/save',
    method: 'post',
    data: data
  })
}

// 修改字典项
export function updateDictData(data) {
  return request({
    url: '/jgjcadmin/api/dictData/update',
    method: 'post',
    data: data
  })
}

// 删除字典项
export function delDictData(data) {
  return request({
    url: '/jgjcadmin/api/dictData/delete',
    method: 'post',
    data: data
  })
}
