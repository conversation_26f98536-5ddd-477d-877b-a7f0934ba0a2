import request from '@/utils/request'

// 查询列表
export function getListPage(data) {
  return request({
    url: '/baseData/route/longDownhill/getListPage',
    method: 'post',
    data
  })
}

// 增
export function addIceCovered(data) {
  return request({
    url: "/baseData/route/longDownhill/add",
    method: "post",
    data,
  });
}

// 暂存
export function tempIceCovered(data) {
  return request({
    url: "/baseData/route/longDownhill/tempAdd",
    method: "post",
    data,
  });
}

// 删
export function delIceCovered(ids) {
  return request({
    url: `/baseData/route/longDownhill/delete/${ids}`,
    method: 'delete',
  })
}

// 改
export function updateIceCovered(data) {
  return request({
    url: '/baseData/route/longDownhill/edit',
    method: 'put',
    data: data
  })
}

// 查
export function getIceCovered(id) {

  return request({
    url: `/baseData/route/longDownhill/get/${id}`,
    method: "get",
  });
}

