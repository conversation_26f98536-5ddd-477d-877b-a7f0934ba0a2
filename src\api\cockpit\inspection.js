import request from "@/utils/request";

/**
 * 巡检查专题-巡查病害上报数
 * @returns 
 */
export function getDiseaseReportCount() {
  return request({
    url: `/manager/constructionStat/getCurrentMonthCount`,
    method: "get",
    notVerifyDuplicates: true,
  });
}

/**
 * 巡检查专题-日常巡查
 * @returns 
 */

export function getDailyPatrol() {
  return request({
    url: "/patrol/patrolCount/dailyPatrolCount",
    method: "get",
    notVerifyDuplicates: true,
  });
}

/**
 * 巡检查专题-桥梁经常检查
 * @returns 
 */
export function getBridgePatrol() {
  return request({
    url: "/patrol/patrolCount/bridgeOftenCount",
    method: "get",
    notVerifyDuplicates: true,
  });
}

/**
 * 巡检查专题-隧道经常检查
 * @returns 
 */
export function getTunnelPatrol() {
  return request({
    url: "/patrol/patrolCount/tunnelOftenCount",
    method: "get",
    notVerifyDuplicates: true,
  });
}

/**
 * 巡检查专题-涵洞经常检查
 * @returns 
 */
export function getHolePatrol() {
  return request({
    url: "/patrol/patrolCount/culvertOftenCount",
    method: "get",
    notVerifyDuplicates: true,
  });
}

/**
 * 巡检查专题-各管理处桥梁经常检查情况
 * @returns 
 */

export function getBridgeOftenCountByManager() {
  return request({
    url: "/patrol/patrolCount/deptBridgeOftenCountList",
    method: "get",
    notVerifyDuplicates: true,
  });
}

/**
 * 巡检查专题-各管理处隧道经常检查情况
 * @returns 
 */
export function getTunnelOftenCountByManager() {
  return request({
    url: "/patrol/patrolCount/deptTunnelOftenCountList",
    method: "get",
    notVerifyDuplicates: true,
  });
}

/**
 * 巡检查专题-各管理处涵洞经常检查情况
 * @returns 
 */

export function getHoleOftenCountByManager() {
  return request({
    url: "/patrol/patrolCount/deptCulvertOftenCountList",
    method: "get",
    notVerifyDuplicates: true,
  });
}

/**
 * 巡检查专题-巡查病害趋势
 * @returns 
 */
export function getTrendData(type) {
  return request({
    url: `/manager/constructionStat/getRoadDiseaseCountByYear/${type}`,
    method: "get",
    notVerifyDuplicates: true,
  });
}

/**
 * 巡检查专题-各管理处巡查里程统计
 * @returns 
 */
export function getMileageData(type) {
  return request({
    url: `/manager/constructionStat/getDomainCountByYear/${type}`,
    method: "get",
    notVerifyDuplicates: true,
  });
}

/**
 * 巡检查专题-各管理处巡查里程统计
 * @returns 
 */
export function getPatrolMileageCount(type) {
  return request({
    url: `/patrol/patrolCount/deptPatrolMileageCount/${type}`,
    method: "get",
    notVerifyDuplicates: true,
  });
}

/**
 * 巡检查专题-各管理处在线巡查车数量
 * @returns 
 */
export function getOnlinePatrolCount() {
  return request({
    url: `/patrol/patrolCount/getDeptRealCoordinateCat`,
    method: "get",
    notVerifyDuplicates: true,
  });
}

/**
 * 巡检查专题-在线巡查车列表
 * @returns 
 */
export function getOnlinePatrolList(params) {
  return request({
    url: `/patrol/patrolCount/getRealCoordinateCatList`,
    method: "get",
    params,
    notVerifyDuplicates: true,
  });
}