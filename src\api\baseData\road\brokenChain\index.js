import request from '@/utils/request'

// 查询列表
export function getListPage(data) {
  return request({
    url: '/baseData/brokenChain/getListPage',
    method: 'post',
    data
  })
}

// 增
export function addBrokenChain(data) {
  return request({
    url: "/baseData/brokenChain/add",
    method: "post",
    data,
  });
}

// 删
export function delBrokenChain(data) {
  return request({
    url: '/baseData/brokenChain/delete/'+data,
    method: 'delete',
  })
}

// 改
export function updateBrokenChain(data) {
  return request({
    url: '/baseData/brokenChain/edit',
    method: 'put',
    data: data
  })
}

// 查
export function getBrokenChain(id) {

  return request({
    url: `/baseData/brokenChain/get/${id}`,
    method: "get",
  });
}

// 暂存
export function tempBrokenChain(data) {
  return request({
    url: "/baseData/brokenChain/tempAdd",
    method: "post",
    data,
  });
}