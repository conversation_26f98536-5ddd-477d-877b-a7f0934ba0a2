import request from '@/utils/request'

// 养护工程监理计量单列表
export function listSettlecalc(data) {
  return request({
    url: `manager/project/supercalc/list`,
    method: 'post',
    data
  })
}

// 新增养护工程监理计量单
export function addSettlecalc(data) {
  return request({
    url: `manager/project/supercalc/add`,
    method: 'post',
    data
  })
}

// 编辑养护工程监理计量单
export function editSettlecalc(data) {
  return request({
    url: `manager/project/supercalc/edit`,
    method: 'put',
    data
  })
}

// 养护工程监理计量单审核节点信息
export function getNodeInfo(data) {
  return request({
    url: `manager/project/supercalc/node/info`,
    method: 'post',
    data
  })
}

// 删除养护工程监理计量单
export function deleteSettlecalc(id) {
  return request({
    url: `manager/project/supercalc/delete/${id}`,
    method: 'delete',
  })
}

// 养护工程监理计量单审核节点信息
export function processSettle(data) {
  return request({
    url: `manager/project/supercalc/process`,
    method: 'post',
    data
  })
}

// 根据路段id查询待进行监理计量的结算计量单
export function listBySid(data) {
  return request({
    url: `manager/project/supercalc/detail/list/unsup`,
    method: 'post',
    data
  })
}

// 新增养护工程监理计量单明细
export function addMiddle(data) {
  return request({
    url: `manager/project/supercalc/detail/add`,
    method: 'post',
    data
  })
}

// 查询养护工程监理计量单明细列表
export function middleListBySid(data) {
  return request({
    url: `manager/project/supercalc/detail/list`,
    method: 'post',
    data
  })
}

// 删除养护工程监理计量单明细
export function deleteMiddle(data) {
  return request({
    url: `manager/project/supercalc/detail/delete`,
    method: 'post',
    data
  })
}

// 查询养护工程监理计量单扣款清单列表
export function fetchDeductionList(data) {
  return request({
    url: `manager/project/supercalc/deduction/list`,
    method: 'post',
    data
  })
}

// 新增养护工程监理计量单扣款清单
export function addDeduction(data) {
  return request({
    url: `manager/project/supercalc/deduction/add`,
    method: 'post',
    data
  })
}

// 编辑养护工程监理计量单扣款清单
export function editDeduction(data) {
  return request({
    url: `manager/project/supercalc/deduction/edit`,
    method: 'put',
    data
  })
}

// 删除养护工程监理计量单扣款清单
export function deleteDeduction(id) {
  return request({
    url: `manager/project/supercalc/deduction/delete/${id}`,
    method: 'delete',
  })
}


// 查询养护工程监理计量单费用调整列表
export function fetchFundAdjustList(data) {
  return request({
    url: `manager/project/supercalc/fundadjust/list`,
    method: 'post',
    data
  })
}

// 新增养护工程监理计量单费用调整
export function addFundAdjust(data) {
  return request({
    url: `manager/project/supercalc/fundadjust/add`,
    method: 'post',
    data
  })
}

// 修改养护工程监理计量单费用调整
export function editFundAdjust(data) {
  return request({
    url: `manager/project/supercalc/fundadjust/edit`,
    method: 'put',
    data
  })
}

// 删除养护工程监理计量单扣款清单
export function deleteFundAdjust(id) {
  return request({
    url: `manager/project/supercalc/fundadjust/delete/${id}`,
    method: 'delete',
  })
}

// 新增费用调整时查询结算计量单列表(分页)
export function getFundadjustList(data) {
  return request({
    url: `manager/project/supercalc/fundadjust/list/data`,
    method: "post",
    data
  })
}

// 通过结算计量单id查询项目列表(分页)
export function getFundadjustProjectList(data) {
  return request({
    url: `manager/project/supercalc/fundadjust/list/project`,
    method: "post",
    data
  })
}

// 查询上期计量单列表
export function getPreCalc(data) {
  return request({
    url: `manager/project/supercalc/last/list`,
    method: 'post',
    data
  })
}
export function updateFund(data) {
  return request({
    url: `manager/project/supercalc/update/fund`,
    method: 'post',
    data
  });
}

// 计量单预览
export function previewReport(id) {
  return request({
    url: `manager/project/supercalc/preview?id=${id}`,
    method: 'get'
  });
}

// 计量单预览
export function downloadReport(id) {
  return request({
    url: `manager/project/supercalc/download?id=${id}`,
    method: 'get'
  });
}



export function getVisaCheck(id) {
  return request({
    url: `manager/project/supercalc/get/visacheck?id=${id}`,
    method: 'get'
  });
}


// 编辑签证人信息
export function updateVisaCheck(data) {
  return request({
    url: `manager/project/supercalc/update/visacheck`,
    method: 'put',
    data
  })
}
