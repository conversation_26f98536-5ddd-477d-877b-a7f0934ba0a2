import request from '@/utils/request'

// 查询养护工程施工单列表(任务单编制查询接口)
export function getConstructionList(data) {
    return request({
        url: '/manager/proj/construction/list',
        method: 'post',
        data,
    });
}

// 查询养护工程施工单列表(分页)[任务单审核，任务单签发模块共用的查询接口
export function getPendingConstructionList(data) {
    return request({
        url: '/manager/proj/construction/pending/list',
        method: 'post',
        data,
    });
}


// 任务单完工登记列表接口
export function getCompletedConstructionList(data) {
    return request({
        url: '/manager/proj/construction/completed/list',
        method: 'post',
        data,
    });
}

// 查询养护工程施工单附件信息(分页)
export function getConstructionFileList(data) {
    return request({
        url: '/manager/proj/construction/file/list',
        method: 'get',
        params: data,
    });
}

// 查询养护工程施工单列表(不分页)
export function getAllConstructionList() {
    return request({
        url: '/manager/proj/construction/listAll',
        method: 'get',
    });
}

// 根据id查询养护工程施工单数据
export function getConstructionById(id) {
    return request({
        url: `/manager/proj/construction/get/${id}`,
        method: 'get',
    });
}

// 新增养护工程施工单
export function addConstruction(data) {
    return request({
        url: '/manager/proj/construction/add',
        method: 'post',
        data,
    });
}

// 修改养护工程施工单
export function editConstruction(data) {
    return request({
        url: '/manager/proj/construction/edit',
        method: 'put',
        data,
    });
}


// 养护工程施工单提交与审核
export function process(data) {
    return request({
        url: '/manager/proj/construction/process',
        method: 'post',
        data,
    });
}

// 新增养护工程施工单附件
export function addConstructionFile(data) {
    return request({
        url: '/manager/proj/construction/add/file',
        method: 'post',
        data,
    });
}

// 删除养护工程施工单附件
export function deleteConstructionFile(id) {
    return request({
        url: `/manager/proj/construction/delete/file/${id}`,
        method: 'put',
    });
}

// 删除养护工程施工单
export function deleteConstruction(id) {
    return request({
        url: `/manager/proj/construction/delete/${id}`,
        method: 'delete',
    });
}

// 施工单查看列表(分页)
export function viewConstructionList(data) {
    return request({
        url: '/manager/proj/construction/view',
        method: 'post',
        data,
    });
}

// 检测任务单预览
export function previewChecks(id) {
    return request({
        url: `/manager/proj/construction/checks/preview?id=${id}`,
        method: 'get',
        responseType: 'blob', // 为了处理文件下载
    });
}



// 开工登记
export function addRegister(data) {
    return request({
        url: '/manager/proj/construction/register/add',
        method: 'post',
        data,
    });
}


// 养护工程施工单审核节点信息
export function getNodeInfo(data) {
    return request({
        url: '/manager/proj/construction/node/info',
        method: 'post',
        data,
    });
}

// 养护工程施工单审核节点信息
export function getNodeInfoNew(data) {
  return request({
    url: '/manager/proj/construction/new/node/info',
    method: 'post',
    data,
  });
}

// 更新养护工程施工单是否完结(isEnd传0更新为未完结,传1更新为已完结)
export function isend(data) {
    return request({
        url: '/manager/proj/construction/isend',
        method: 'post',
        data,
    });
}


// 监理单位接收列表
export function supPendingList(data) {
    return request({
        url: '/manager/proj/construction/sup/pending/list',
        method: 'post',
        data,
    });
}


// 设计单位接收列表
export function designPendingList(data) {
    return request({
        url: '/manager/proj/construction/design/pending/list',
        method: 'post',
        data,
    });
}



//  养护工程施工单设计单位接收
export function designProcess(data) {
    return request({
        url: '/manager/proj/construction/design/process',
        method: 'post',
        data,
    });
}


// 养护工程施工单监理单位接收
export function supProcess(data) {
    return request({
        url: '/manager/proj/construction/sup/process',
        method: 'post',
        data,
    });
}


// 设计任务单查看(分页)
export function viewDesignConstructionList(data) {
    return request({
        url: '/manager/proj/construction/design/view',
        method: 'post',
        data,
    });
}

// 施工单查看列表(分页)
export function viewSupConstructionList(data) {
    return request({
        url: '/manager/proj/construction/sup/view',
        method: 'post',
        data,
    });
}



/**
 * 施工单预览
 * @param {string} id - 施工单ID
 * @returns {Promise} - 返回 Promise 对象
 */
export function previewConstruction(id) {
    return request({
        url: `/manager/proj/construction/con/preview?id=${id}`,
        method: 'get'
    });
}

/**
 * 检测任务单预览
 * @param {string} id - 检测任务单ID
 * @returns {Promise} - 返回 Promise 对象
 */
export function previewCheck(id) {
    return request({
        url: `/manager/proj/construction/checks/preview?id=${id}`,
        method: 'get'
    });
}

/**
 * 设计任务单预览
 * @param {string} id - 设计任务单ID
 * @returns {Promise} - 返回 Promise 对象
 */
export function previewDesign(id) {
    return request({
        url: `/manager/proj/construction/design/preview?id=${id}`,
        method: 'get'
    });
}

/**
 * 监理任务单预览
 * @param {string} id - 监理任务单ID
 * @returns {Promise} - 返回 Promise 对象
 */
export function previewSup(id) {
    return request({
        url: `/manager/proj/construction/sup/preview?id=${id}`,
        method: 'get'
    });
}

// 根据工程id,合同id查询养护工程费用明细列表(不分页)
export function getProDetail(data) {
    return request({
        url: '/manager/project/detail/list/data',
        method: 'get',
        params: data
    });
}


// 根据id查询养护工程施工单操作记录
export function getTaskRecord(id) {
    return request({
        url: `/manager/proj/construction/get/record/${id}`,
        method: 'get',
    });
}

export function updateIssueDate(data) {
    return request({
        url: '/manager/proj/construction/update/issueDate',
        method: 'post',
        data: data
    })
}

export function withdraw(data) {
    return request({
        url: '/manager/proj/construction/withdraw',
        method: 'post',
        data: data
    })
}
