import request from '@/utils/request'
// 中交国通

// 获取分页数据
export function getZjgtConfigPage(data) {
  return request({
    url: '/jgjcadmin/api/flash/zjgt-config/page',
    method: 'post',
    data
  });
}

// 保存数据
export function saveZjgtConfig(data) {
  return request({
    url: '/jgjcadmin/api/flash/zjgt-config/save',
    method: 'post',
    data
  });
}

// 更新数据
export function updateZjgtConfig(data) {
  return request({
    url: '/jgjcadmin/api/flash/zjgt-config/update',
    method: 'post',
    data
  });
}

// 删除数据
export function deleteZjgtConfig(data) {
  return request({
    url: '/jgjcadmin/api/flash/zjgt-config/delete',
    method: 'post',
    data
  });
}


// 维的美配置分页查询
export function getWdmConfigPage(data) {
  return request({
    url: '/jgjcadmin/api/flash/wdm-config/page',
    method: 'post',
    data
  })
}

// 维的美配置新增
export function saveWdmConfig(data) {
  return request({
    url: '/jgjcadmin/api/flash/wdm-config/save',
    method: 'post',
    data
  })
}

// 维的美配置编辑
export function updateWdmConfig(data) {
  return request({
    url: '/jgjcadmin/api/flash/wdm-config/update',
    method: 'post',
    data
  })
}

// 维的美配置删除
export function deleteWdmConfig(data) {
  return request({
    url: '/jgjcadmin/api/flash/wdm-config/delete',
    method: 'post',
    data
  })
}



// 维的美配置分页查询
export function getSsConfigPage(data) {
  return request({
    url: '/jgjcadmin/api/flash/sansi-config/page',
    method: 'post',
    data
  })
}

// 维的美配置新增
export function saveSsConfig(data) {
  return request({
    url: '/jgjcadmin/api/flash/sansi-config/save',
    method: 'post',
    data
  })
}

// 维的美配置删除
export function deleteSsConfig(data) {
  return request({
    url: '/jgjcadmin/api/flash/sansi-config/delete',
    method: 'post',
    data
  })
}




// 维的美配置分页查询
export function getCommonConfigPage(data) {
  return request({
    url: '/jgjcadmin/api/flash/common-config/page',
    method: 'post',
    data
  })
}

// 维的美配置新增
export function saveCommonConfig(data) {
  return request({
    url: '/jgjcadmin/api/flash/common-config/save',
    method: 'post',
    data
  })
}

// 维的美配置编辑
export function updateCommonConfig(data) {
  return request({
    url: '/jgjcadmin/api/flash/common-config/update',
    method: 'post',
    data
  })
}

// 维的美配置删除
export function deleteCommonConfig(data) {
  return request({
    url: '/jgjcadmin/api/flash/common-config/delete',
    method: 'post',
    data
  })
}
