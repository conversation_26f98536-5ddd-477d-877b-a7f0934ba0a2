import request from "@/utils/request";

// 查询图层目录数据配置列表
export function getListPage(query) {
  return request({
    url: "/oneMap/menuSub/getListPage",
    method: "get",
    params: query,
  });
}

/**
 * 获取父级菜单列表
 * @param {Object} query - 查询参数对象
 * @returns {Promise} 返回请求的Promise对象
 * @description 通过GET请求获取oneMap子菜单的父级列表数据
 */
export function getParentList(query) {
  return request({
    url: "/oneMap/menuSub/getList",
    method: "get",
    params: query,
  });
}

// 查询图层目录数据配置详细
export function getMenuSub(id) {
  return request({
    url: "/oneMap/menuSub/getInfoById",
    method: "get",
    params: { id },
  });
}

// 新增图层目录数据配置
export function addMenuSub(data) {
  return request({
    url: "/oneMap/menuSub/add",
    method: "post",
    data,
  });
}

// 修改图层目录数据配置
export function updateMenuSub(data) {
  return request({
    url: "/oneMap/menuSub/edit",
    method: "post",
    data,
  });
}

// 删除图层目录数据配置
export function delMenuSub(ids) {
  return request({
    url: "/oneMap/menuSub/delete",
    method: "delete",
    data: ids,
  });
}

/**
 * 查询图层目录数据配置树结构
 * @param {*} params 
 * @returns 
 */
export function getListTree(params) {
  return request({
    url: "/oneMap/menuSub/getListTree",
    method: "get",
    params,
  });
}
// 清除缓存
export function clearTileCache(data) {
  return request({
    url: "/oneMap/menuSub/clearTileCache",
    method: "post",
    data,
  });
}