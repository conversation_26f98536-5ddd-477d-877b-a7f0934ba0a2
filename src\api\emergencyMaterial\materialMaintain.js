import request from '@/utils/request'

// 维修保养节点信息
export function getNodeInfo(data) {
  return request({
    url: '/manager/material/repair/node/info',
    method: 'post',
    data
  })
}

// 获取维修保养列表
export function getMaintainList(data) {
  return request({
    url: '/manager/material/repair/list',
    method: 'post',
    data
  })
}

// 新增维修保养
export function addMaintain(data) {
  return request({
    url: '/manager/material/repair/add',
    method: 'post',
    data
  })
}

// 根据id查询维修保养数据
export function getDetailMaintainList(id) {
  return request({
    url: `/manager/material/repair/get/${id}`,
    method: 'get'
  })
}

// 编辑维修保养
export function editMaintain(data) {
  return request({
    url: '/manager/material/repair/edit',
    method: 'put',
    data
  })
}

// 删除维修保养
export function delMaintain(id) {
  return request({
    url: `/manager/material/repair/delete/${id}`,
    method: 'delete',
  })
}

// 根据出库单id与物资库id查询应急盘点单明细列表
export function getMaintainDetailList(data) {
  return request({
    url: '/manager/material/repair/detail/list',
    method: 'post',
    data
  })
}

//维修保养审核
export function submitMaintain(data) {
  return request({
    url: '/manager/material/repair/process',
    method: 'post',
    data
  })
}


// 查询保养审核结果列表
export function getMaintainResultList(data) {
  return request({
    url: '/manager/material/repair/view',
    method: 'post',
    data
  })
}


// 报表
export function previewReport(id) {
  return request({
    url: `/manager/material/repair/report/preview?id=${id}`,
    method: 'get',
  })
}

