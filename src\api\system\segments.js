import request from '@/utils/request'

// 查询部门子段关联列表
export function listSegments(query) {
  return request({
    url: '/system/segments/list',
    method: 'get',
    params: query
  })
}

// 根据部门ID查询部门子段ID列表
export function findDeptSegmentsIdList(deptId) {
  return request({
    url: '/system/segments/findDeptSegmentsIdList/' + deptId,
    method: 'get'
  })
}

// 查询部门子段关联详细
export function getSegments(deptId) {
  return request({
    url: '/system/segments/' + deptId,
    method: 'get'
  })
}

// 新增部门子段关联
export function addSegments(data) {
  return request({
    url: '/system/segments',
    method: 'post',
    data: data
  })
}

// 批量新增
export function addDeptSegments(data) {
  return request({
    url: '/system/segments/addDeptSegments',
    method: 'post',
    data: data
  })
}

// 修改部门子段关联
export function updateSegments(data) {
  return request({
    url: '/system/segments',
    method: 'put',
    data: data
  })
}

// 删除部门子段关联
export function delSegments(deptId) {
  return request({
    url: '/system/segments/' + deptId,
    method: 'delete'
  })
}
