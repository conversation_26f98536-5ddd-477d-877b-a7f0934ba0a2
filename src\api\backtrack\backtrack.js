import request from '@/utils/request'

// 查询设计回溯列表
export function listBacktrack(query) {
  return request({
    url: '/disaster/backtrack/list',
    method: 'get',
    params: query
  })
}

// 查询设计回溯详细
export function getBacktrack(id) {
  return request({
    url: '/disaster/backtrack/get/' + id,
    method: 'get'
  })
}

// 新增设计回溯
export function addBacktrack(data) {
  return request({
    url: '/disaster/backtrack/add',
    method: 'post',
    data: data
  })
}

// 修改设计回溯
export function updateBacktrack(data) {
  return request({
    url: '/disaster/backtrack/edit',
    method: 'put',
    data: data
  })
}

// 删除设计回溯
export function delBacktrack(id) {
  return request({
    url: '/disaster/backtrack/delete/' + id,
    method: 'delete'
  })
}
