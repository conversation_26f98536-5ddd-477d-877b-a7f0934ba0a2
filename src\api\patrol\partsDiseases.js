import request from '@/utils/request'

// 查询部件-病害关系列表
export function listPartsDiseases(query) {
  return request({
    url: '/patrol/partsDiseases/list',
    method: 'get',
    params: query
  })
}

export function listAllPartsDiseases(query) {
  return request({
    url: '/patrol/partsDiseases/listAll',
    method: 'get',
    params: query
  })
}

// 查询部件-病害关系详细
export function getPartsDiseases(id) {
  return request({
    url: '/patrol/partsDiseases/get/' + id,
    method: 'get'
  })
}

// 新增部件-病害关系
export function addPartsDiseases(data) {
  return request({
    url: '/patrol/partsDiseases/add',
    method: 'post',
    data: data
  })
}
// 新增部件-病害关系
export function addBatchPartsDiseases(data) {
  return request({
    url: '/patrol/partsDiseases/addBatch',
    method: 'post',
    data: data
  })
}
// 修改部件-病害关系
export function updatePartsDiseases(data) {
  return request({
    url: '/patrol/partsDiseases/edit',
    method: 'put',
    data: data
  })
}

// 删除部件-病害关系
export function delPartsDiseases(id) {
  return request({
    url: '/patrol/partsDiseases/delete/' + id,
    method: 'delete'
  })
}
// 删除部件-病害关系
export function delPartsDiseasesByPartsId(partsId) {
  return request({
    url: '/patrol/partsDiseases/deleteByPartsId/' + partsId,
    method: 'delete'
  })
}
