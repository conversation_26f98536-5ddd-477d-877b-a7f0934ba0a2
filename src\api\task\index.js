import request from '@/utils/request'



//审核开始
export function audit(data) {
  return request({
    url: '/baseData/audit/startBaseDataProcess',
    method: 'post',
    data
  })
}

//审核开始
export function auditProcessBaseDataTasks(data) {
  return request({
    url: '/baseData/audit/processBaseDataTasks',
    method: 'post',
    data
  })
}

/**
 * 根据资产ID获取资产信息
 * 
 * 此函数发起一个GET请求，以获取特定资产的详细信息
 * 它主要用于与后端API进行交互，获取资产数据
 * 
 * @param {string} assetId - 资产的唯一标识符
 * @returns {Promise} 返回一个Promise对象，它在请求成功时解析为资产信息，在请求失败时解析为错误对象
 */
export function getAssetInfo(assetId) {
  return request({
    url: `/baseData/assetInfo/getAssetInfo/${assetId}`,
    method: 'get',
  })
}


