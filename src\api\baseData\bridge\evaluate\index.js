import request from '@/utils/request'

// 查询桥梁特殊检查列表
export function listBridgeEvaluateRecords(data) {
  return request({
    url: '/baseData/bridge/evaluate/getListPage',
    method: 'post',
    data
  })
}

// 查询桥梁特殊检查详细
export function evaluateExport(data) {
  return request({
    url: '/baseData/bridge/evaluate/export',
    method: 'get',
    data: data
  })
}

//桥梁部件评定数据
export function evaluatePosition(params) {
  return request({
    url: '/baseData/bridge/evaluate/position/getListPage',
    method: 'get',
    params: params
  })
}

//桥梁构件评定数据

export function evaluateComponent(params) {
  return request({
    url: '/baseData/bridge/evaluate/component/getListPage',
    method: 'get',
    params: params
  })
}

