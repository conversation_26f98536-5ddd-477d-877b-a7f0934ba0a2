import request from '@/utils/request'

// 查询隧道发电油费列表(分页)
export function listTunnelOilcostPage(data) {
    return request({
        url: 'manager/operate/tunnel/oilcost/list',
        method: 'post',
        data
    });
}

// 查询隧道发电油费列表(不分页)
export function listTunnelOilcostAll() {
    return request({
        url: 'manager/operate/tunnel/oilcost/listAll',
        method: 'get'
    });
}

// 根据id查询隧道发电油费数据
export function getTunnelOilcostById(id) {
    return request({
        url: `manager/operate/tunnel/oilcost/get/${id}`,
        method: 'get'
    });
}

// 新增隧道发电油费
export function addTunnelOilcost(data) {
    return request({
        url: 'manager/operate/tunnel/oilcost/add',
        method: 'post',
        data
    });
}

// 修改隧道发电油费
export function editTunnelOilcost(data) {
    return request({
        url: 'manager/operate/tunnel/oilcost/edit',
        method: 'put',
        data
    });
}

// 删除隧道发电油费
export function deleteTunnelOilcost(id) {
    return request({
        url: `manager/operate/tunnel/oilcost/delete/${id}`,
        method: 'delete'
    });
}
