import request from '@/utils/request'

// 查询废料入库记录列表
export function listWasteImportDetail(query) {
  return request({
    url: '/manager/waste/import/detail/list',
    method: 'post',
    data: query
  })
}

// 编辑废料入库记录
export function editWasteImportDetail(data) {
  return request({
    url: '/manager/waste/import/detail/edit',
    method: 'put',
    data: data
  })
}

// 导出废料入库记录
export function exportWasteImportDetail(query) {
  return request({
    url: '/manager/waste/import/detail/export',
    method: 'post',
    data: query,
    responseType: 'blob'
  })
}

// 下载导入模板
export function downloadTemplate(type) {
  return request({
    url: '/manager/waste/import/detail/download',
    method: 'get',
    params: {
      type
    },
    responseType: 'blob'
  })
}

// 导入废料入库记录
export function uploadStoreFile(file) {
  const formData = new FormData()
  formData.append('file', file)
  return request({
    url: '/manager/waste/import/detail/store/upload',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
