import request from '@/utils/request'

// 查询结构分组列表
export function listStructureGroup(query) {
  return request({
    url: '/jgjc-manager/api/console/jc/structureGroup/findPage ',
    method: 'post',
    data: query
  })
}

// 查询结构分组详细
export function getStructureGroup(id) {
  return request({
    url: '/jgjc-manager/api/console/jc/structureGroup/findById?id=' + id,
    method: 'get'
  })
}

// 新增结构分组
export function addStructureGroup(data) {
  return request({
    url: '/jgjc-manager/api/console/jc/structureGroup/create',
    method: 'post',
    data: data
  })
}
// 修改结构分组
export function updateStructureGroup(data) {
  return request({
    url: '/jgjc-manager/api/console/jc/structureGroup/modify',
    method: 'post',
    data: data
  })
}

// 删除结构分组
export function delStructureGroup(id) {
  return request({
    url: '/jgjc-manager/api/console/jc/structureGroup/removeById?id=' + id,
    method: 'delete'
  })
}
// 查询结构分组树型数据
export function getStructureGroupFindTree() {
  return request({
    url: '/jgjc-manager/api/console/jc/structureGroup/findTree',
    method: 'get'
  })
}
// 修改结构分组树型数据排序
export function updateStructureGroupModifySort(data) {
  return request({
    url: '/jgjc-manager/api/console/jc/structureGroup/modifySort',
    method: 'post',
    data: data
  })
}
