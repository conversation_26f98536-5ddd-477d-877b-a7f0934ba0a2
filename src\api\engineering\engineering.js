import request from '@/utils/request'

// 查询涉路工程列表
export function listEngineering(query) {
  return request({
    url: '/repote/engineering/list',
    method: 'get',
    params: query
  })
}

// 查询涉路工程详细
export function getEngineering(id) {
  return request({
    url: '/repote/engineering/get/' + id,
    method: 'get'
  })
}



// 新增涉路工程
export function addEngineering(data) {
  return request({
    url: '/repote/engineering/add',
    method: 'post',
    data: data
  })
}

// 修改涉路工程
export function updateEngineering(data) {
  return request({
    url: '/repote/engineering/edit',
    method: 'put',
    data: data
  })
}

// 删除涉路工程
export function delEngineering(id) {
  return request({
    url: '/repote/engineering/delete/' + id,
    method: 'delete'
  })
}

// 获取养护路段名称
export function selectSectionName(id) {
  return request({
    url: '/RemoteDeptAuthService/getById' + id,
    method: 'get'
  })
}


// 获取user的deptIDList
export function findUserTransportation(data) {
  return request({
    url: '/repote/engineering/getDeptIDs/',
    method: 'post',
    data:  data
  })


}
// 获取用户部门权限列表
export function findUserDept() {
  return request({
    url: '/system/dept/getDeptIdList/',
    method: 'get'
  })
}

// 获取用户路段权限列表
export function findUserMaintenanceIds() {
  return request({
    url: '/system/maintenanceSection/findUserMaintenanceIds/',
    method: 'post'
  })
}

// 传入用户权限对象给后端
export function getUserPermissions(query) {
  return request({
    url: '/repote/engineering/getUserPermissions',
    method: 'post',
    data: query
  })
}


// 根据部门ID获取部门信息
export function findDeptInfo(id) {
  return request({
    url: '/system/dept/getById/' + id,
    method: 'get'
  })
}

// 查询涉路工程详细
export function getEngineeringLane(id) {
  return request({
    url: '/repote/engineeringLane/get/' + id,
    method: 'get'
  })
}
