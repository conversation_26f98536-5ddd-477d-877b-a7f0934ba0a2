@use "sass:math";

$vw_base: 1920px;
$vh_base: 2160px;

@function vwpx($px) {
  @return ($px / $vh_base) * 100vmin;
}


$base-font-size: 16px; // 默认值

@media (min-width: 768px) {
  $base-font-size: 24px; // 调整为24px
}

@media (min-width: 1024px) {
  $base-font-size: 20px; // 调整为20px
}

@media (min-width: 1920px) {
  $base-font-size: 18px; // 调整为18px
}

@function px-to-rem($px-value) {
  @return ($px-value / $base-font-size) * 1rem;
}

$design_width: 1920;
$design_height: 2160;

// px 转为 vw 的函数
@function vw($px) {
  @return ($px / $design_width) * 100vw;
}

// px 转为 vh 的函数
@function vh($px) {
  @return ($px / $design_height) * 100vh;
}

// 获取屏幕宽度
@function screen-width() {
  @return 100vw;
}

// 获取屏幕高度 
@function screen-height() {
  @return 100vh;
}

// 获取较小的屏幕尺寸
@function screen-min() {
  @return min(100vw, 100vh);
}

// 获取较大的屏幕尺寸
@function screen-max() {
  @return max(100vw, 100vh);
}

// 响应式缩放函数
@function responsive-scale($size) {
  $scale: min(100vw / $design_width, 100vh / $design_height);
  @return calc(#{$size} * #{$scale});
}

// 响应式字体大小混入
@mixin responsive-font($size) {
  font-size: responsive-scale($size);
}

// 响应式宽度混入
@mixin responsive-width($width) {
  width: responsive-scale($width);
}

// 响应式高度混入
@mixin responsive-height($height) {
  height: responsive-scale($height);
}

// 响应式内边距混入
@mixin responsive-padding($top, $right: $top, $bottom: $top, $left: $right) {
  padding: responsive-scale($top) responsive-scale($right) responsive-scale($bottom) responsive-scale($left);
}

// 响应式外边距混入
@mixin responsive-margin($top, $right: $top, $bottom: $top, $left: $right) {
  margin: responsive-scale($top) responsive-scale($right) responsive-scale($bottom) responsive-scale($left);
}


// 根据屏幕像素计算字体大小
@function calc-font-size($pixels) {
  $viewport-width: 100vw;
  $min-font-size: 12px;  // 最小字体大小
  $max-font-size: $pixels * 5;  // 最大字体大小
  
  @return clamp(
    $min-font-size,
    calc(#{$pixels * 0.0625}vw),  // 1vw = 1/100 viewport width
    $max-font-size
  );
}

// 字体大小混入
@mixin dynamic-font-size($pixels) {
  font-size: calc-font-size($pixels);
}