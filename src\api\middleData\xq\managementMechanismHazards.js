import request from '@/utils/request'

// 查询管理机制隐患列表
export function listManagementMechanismHazards(query) {
  return request({
    url: '/middleData/managementMechanismHazards/list',
    method: 'post',
    params: {pageNum: query.pageNum, pageSize: query.pageSize},
    data: query
  })
}

// 查询管理机制隐患详细
export function getManagementMechanismHazards(id) {
  return request({
    url: '/middleData/managementMechanismHazards/get/' + id,
    method: 'get'
  })
}

// 新增管理机制隐患
export function addManagementMechanismHazards(data) {
  return request({
    url: '/middleData/managementMechanismHazards/add',
    method: 'post',
    data: data
  })
}

// 修改管理机制隐患
export function updateManagementMechanismHazards(data) {
  return request({
    url: '/middleData/managementMechanismHazards/edit',
    method: 'put',
    data: data
  })
}

// 删除
export function delData(data) {
  return request({
    url: `/middleData/managementMechanismHazards/delete`,
    method: 'delete',
    data: data.idList,
  });
}

// 批量审核管理机制隐患
export function batchAudit(data) {
  return request({
    url: '/middleData/managementMechanismHazards/batchAudit',
    method: 'post',
    data: data
  })
}


