import request from '@/utils/request'

// 查询日养结算计量单列表(分页)
export function listSettlecalc(queryParams) {
    return request({
        url: '/manager/settlecalc/list',
        method: 'get',
        params: queryParams,
    });
}

// 根据路段id查询上期日养结算计量单列表(不分页)
export function listAllSettlecalcByMaiSecId(maiSecId) {
    return request({
        url: `/manager/settlecalc/listAll?maiSecId=${maiSecId}`,
        method: 'get',
    });
}

// 根据id查询日养结算计量单数据
export function getSettlecalcById(id) {
    return request({
        url: `/manager/settlecalc/get/${id}`,
        method: 'get',
    });
}

// 新增日养结算计量单
export function addSettlecalc(data) {
    return request({
        url: '/manager/settlecalc/add',
        method: 'post',
        data,
    });
}

// 修改日养结算计量单
export function editSettlecalc(data) {
    return request({
        url: '/manager/settlecalc/edit',
        method: 'put',
        data,
    });
}

// 删除日养结算计量单
export function deleteSettlecalc(id) {
    return request({
        url: `/manager/settlecalc/delete/${id}`,
        method: 'delete',
    });
}

// 根据结算计量单id查询计量单明细列表
export function middleListBySid(params) {
    return request({
        url: `/manager/settlecalc/detail/list`,
        method: 'get',
        params
    })
}

// 新增计量单明细
export function addMiddle(data) {
  return request({
    url: `/manager/settlecalc/detail/add`,
    method: 'post',
    data
  })
}

// 删除计量单明细
export function deleteMiddle(ids) {
  return request({
    url: `/manager/settlecalc/detail/delete/${ids}`,
    method: 'delete'
  })
}


export function listBySid(params) {
  return request({
    url: `/manager/intermediate/list/data`,
    method: 'get',
    params
  })
}

export function listScheme(params) {
  return request({
    url: `/manager/method/list/scheme`,
    method: 'get',
    params
  })
}
// 查询结算计量单扣款清单列表(分页)
export function fetchDeductionList(params) {
    return request({
        url: '/manager/deduction/list',
        method: 'get',
        params: params
    });
}


// 新增结算计量单扣款清单
export function addDeduction(deduction) {
    return request({
        url: '/manager/deduction/add',
        method: 'post',
        data: deduction
    });
}

// 修改结算计量单扣款清单
export function editDeduction(deduction) {
    return request({
        url: '/manager/deduction/edit',
        method: 'put',
        data: deduction
    });
}

// 删除结算计量单扣款清单
export function deleteDeduction(id) {
    return request({
        url: `/manager/deduction/delete/${id}`,
        method: 'delete'
    });
}


// 查询费用调整列表(分页)
export function fetchFundAdjustList(params) {
    return request({
        url: '/manager/fundadjust/list',
        method: 'get',
        params: params
    });
}

// 新增费用调整
export function addFundAdjust(data) {
    return request({
        url: '/manager/fundadjust/add',
        method: 'post',
        data: data
    });
}

// 修改费用调整
export function editFundAdjust(data) {
    return request({
        url: '/manager/fundadjust/edit',
        method: 'put',
        data: data
    });
}

// 删除费用调整
export function deleteFundAdjust(id) {
    return request({
        url: `/manager/fundadjust/delete/${id}`,
        method: 'delete'
    });
}


// 查询材料调整列表(分页)
export function fetchMaterialAdjustList(params) {
    return request({
        url: '/manager/materialadjust/list',
        method: 'get',
        params: params
    });
}

// 新增材料调整
export function addMaterialAdjust(data) {
    return request({
        url: '/manager/materialadjust/add',
        method: 'post',
        data: data
    });
}

// 修改材料调整
export function editMaterialAdjust(data) {
    return request({
        url: '/manager/materialadjust/edit',
        method: 'put',
        data: data
    });
}

// 删除材料调整
export function deleteMaterialAdjust(id) {
    return request({
        url: `/manager/materialadjust/delete/${id}`,
        method: 'delete'
    });
}
// 根据结算计量单id查询所有子目数据
export function listAllScheme(params) {
    return request({
        url: '/manager/method/list/all/scheme',
        method: 'get',
        params: params
    });
}

// 日养中间计量单待审核列表(分页)
export function listPendingDaliyIntermediate(data) {
  return request({
    url: '/manager/settlecalc/pending/list',
    method: 'get',
    params: data,
    notVerifyDuplicates: true
  });
}

// 结算计量单提交
export function submit(params) {
  return request({
    url: '/manager/settlecalc/submit',
    method: 'post',
    data: params
  });
}



// 结算计量单审核
export function processSettle(params) {
  return request({
    url: '/manager/settlecalc/process',
    method: 'post',
    data: params
  });
}

// 结算计量单审核
export function getNodeInfo(params) {
  return request({
    url: '/manager/settlecalc/node/info',
    method: 'post',
    data: params
  });
}


// 查询事件信息
export function listEvent(data) {
  return request({
    url: '/manager/settlecalc/detail/list/event',
    method: 'get',
    params: data
  });
}


// 查询材料调差列表
export function listAdjust(data) {
    return request({
        url: '/manager/adjust/listAll',
        method: 'get',
        params: data
    });
}


// 查询期别列表
export function getNumbers(data) {
    return request({
        url: '/manager/number/listAll',
        method: 'get',
        params: data
    });
}

// 查询期别列表
export function getNumbersByYear(data) {
  return request({
    url: '/manager/number/list',
    method: 'get',
    params: data
  });
}

// 结算计量单预览
export function previewSettlecalcReport(id) {
  return request({
    url: `/manager/settlecalc/report/preview?id=${id}`,
    method: 'get'
  });
}

// 结算计量单下载
export function downloadSettlecalcReport(id) {
  return request({
    url: `/manager/settlecalc/report/download?id=${id}`,
    method: 'get'
  });
}

// 日养结算计量单待审核列表(分页)
export function listViewDaliyIntermediate(data) {
  return request({
    url: '/manager/settlecalc/view',
    method: 'get',
    params: data,
    notVerifyDuplicates: true
  });
}

// 查询上期计量单列表
export function getPreCalc(data) {
  return request({
    url: `manager/settlecalc/last/list`,
    method: 'get',
    params: data
  })
}

// 日养结算计量单结算支付列表(分页)
export function listPayment(data) {
  return request({
    url: '/manager/settlecalc/payment',
    method: 'get',
    params: data,
    notVerifyDuplicates: true
  });
}

// 预览
export function downloadSettlecalc(id) {
  return request({
    url: `manager/calc/project/settlecalc/report/download?id=${id}`,
    method: 'get',
  })
}

export function updateFund(id) {
  return request({
    url: `/manager/settlecalc/update/fund?id=${id}`,
    method: 'post'
  });
}


export function withdraw(data) {
  return request({
    url: '/manager/settlecalc/withdraw',
    method: 'post',
    data: data
  })
}

