import request from "@/utils/request";

// 获取统计数据
export function getStatistics() {
  return request({
    url: "/baseData/app/statistics/get",
    method: "get",
    notVerifyDuplicates: true,
  });
}

// 管理处桥梁分布情况
export function getBridgeList() {
  return request({
    url: "/baseData/bridge/statistics/getBridgeTypes",
    method: "get",
    notVerifyDuplicates: true,
  });
}

// 管理处隧道分布情况
export function getTunnelList() {
  return request({
    url: "/baseData/tunnel/statistics/getStatisticsByManagementMaintenanceId",
    method: "get",
    notVerifyDuplicates: true,
  });
}

// 桥梁健康监测、独柱墩、长大桥目录
export function getMaintenanceBridgeInfo() {
  return request({
    url: "/baseData/bridge/statistics/getMaintenanceBridgeInfo",
    method: "get",
    notVerifyDuplicates: true,
  });
}

// 白塔村隧道左幅
export function getTunnelDetailInfo(params) {
  return request({
    url: "/baseData/tunnel/statistics/getTunnelDetailInfo",
    method: "get",
    params,
    notVerifyDuplicates: true,
  });
}

// 龙江特大桥上行
export function getBridgeInfo(data) {
  return request({
    url: "/baseData/bridge/statistics/getBridgeInfo",
    method: "post",
    data,
    notVerifyDuplicates: true,
  });
}

// 获取矢量切片(geojson)
export function getVectorTile(data) {
  return request({
    url: "/oneMap/layerData/geojson",
    method: "post",
    data,
    notVerifyDuplicates: true
  });
}

/**
 * 查询部门合并范围线 - 权限
 * @param {*} params {sysDeptId:系统部门id}
 * @returns
 */
export function getDataCockpitGeoJson(data) {
  return request({
    url: '/oneMap/layerData/getDataCockpitGeoJson',
    method: 'post',
    data
  })
}

// 获取桥梁资金统计
export function getBridgeSumFundInfo(data = {}) {
  return request({
    url: "/manager/disease/getBridgeSumFundInfo",
    method: "post",
    data,
    notVerifyDuplicates: true
  });
}

// 获取桥梁资金统计
export function getDomainCountInfo(data = {}) {
  return request({
    url: "/manager/disease/getDomainCountInfo",
    method: "post",
    data,
    notVerifyDuplicates: true
  });
}

//
export function getMonthCountInfo(data={}) {
  return request({
    url: "/manager/disease/getMonthCountInfo",
    method: "post",
    data,
    notVerifyDuplicates: true
  });
}

//
export function getDisFromCountInfo(data) {
  return request({
    url: "/manager/disease/getDisFromCountInfo",
    method: "post",
    data,
    notVerifyDuplicates: true
  });
}
// 日常养护
export function getDiseaseCountInfo(data = {}) {
  return request({
    url: "/manager/disease/getDiseaseCountInfo",
    method: "post",
    data,
    notVerifyDuplicates: true
  });
}

// 专项养护
export function getStatusCountList(data = {}) {
  return request({
    url: "/manager/project/getStatusCountList",
    method: "post",
    data,
    notVerifyDuplicates: true,
  });
}

// 养护工程
export function getProjCountInfo(data = {}) {
  return request({
    url: "/manager/project/getProjCountInfo",
    method: "post",
    data,
    notVerifyDuplicates: true
  });
}

// 年度养护经费预算统计信息
export function getProjTypeSumFundInfo(data = {}) {
  return request({
    url: "/manager/disease/getProjTypeSumFundInfo",
    method: "post",
    data,
    notVerifyDuplicates: true
  });
}

// 被损被盗
export function getTheftCountInfo(data = {}) {
  return request({
    url: "/manager/theft/project/getTheftCountInfo",
    method: "post",
    data,
    notVerifyDuplicates: true
  });
}

// 定期检查
export function getCheckCountInfo(data = {}) {
  return request({
    url: "/manager/check/project/getCheckCountInfo",
    method: "post",
    data,
    notVerifyDuplicates: true
  });
}

// 通车隧道趋势
export function getTunnelTrafficTrend() {
  return request({
    url: "/baseData/tunnel/statistics/getTunnelTrafficTrend",
    method: "get",
  });
}

// 主屏2-今日巡查
export function getInspectionLogsStatistic(params) {
  return request({
    url: "/patrol/inspectionLogs/statistics",
    method: "get",
    params,
    notVerifyDuplicates: true
  });
}
// 主屏2-今日检查
export function getPatrolDetail(params) {
  return request({
    url: "/patrol/inspectionLogs/patrolDetailByDepartments",
    method: "get",
    params,
    notVerifyDuplicates: true
  });
}

///system/count/baseCount
export function getBaseCount(params) {
  return request({
    url: "/system/count/baseCount",
    method: "get",
    params,
    notVerifyDuplicates: true
  });
}

// GET /patrol/inspectionLogs/patrolYearDetail/{year}
export function getPatrolYearDetail(params) {
  return request({
    url: "/patrol/inspectionLogs/patrolYearDetail/"+params.year,
    method: "get",
    notVerifyDuplicates: true
  });
}

// 隧道专题-基础信息
export function tunnelBaseInfo(assetId) {
  return request({
    url: `/patrol/inspectionLogs/tunnelBaseInfo?assetId=${assetId}`,
    method: "get",
    notVerifyDuplicates: true
  });
}

// 隧道专题-巡查情况
export function selectAll(data) {
  return request({
    url: `/patrol/patrolTunnelCheck/selectAll`,
    method: "post",
    data,
    notVerifyDuplicates: true
  });
}

// 隧道专题-隧道土建
export function getByCheckId(params) {
  return request({
    url: `/patrol/detail/getByCheckId`,
    method: "get",
    params
  });
}

// 隧道专题-隧道机电
export function list(params) {
  return request({
    url: `/patrol/deviceCheck/list`,
    method: "get",
    params,
    notVerifyDuplicates: true
  });
}
// 隧道专题-隧道经常检查
export function findByDoubleTime(params) {
  return request({
    url: `/patrol/inspectionLogs/findByDoubleTime`,
    method: "get",
    params,
  });
}

// /patrol/assetCheck/selectAssetCheckData
export function selectAssetCheckData(params) {
  return request({
    url: "/patrol/assetCheck/selectAssetCheckData",
    method: "post",
    params,
    data:params,
    notVerifyDuplicates: true
  });
}

//GET /patrol/inspectionLogs/bridgeBaseInfo?assetId=1000000
export function getBridgeBaseInfo(params) {
  return request({
    url: "/patrol/inspectionLogs/bridgeBaseInfo",
    method: "get",
    params,
    notVerifyDuplicates: true
  });
}


//patrol/patrolBridgeCheck/selectWithResult"
export function selectWithResult(params) {
  return request({
    url: "/patrol/patrolBridgeCheck/selectWithResult",
    method: "get",
    params,
    notVerifyDuplicates: true
  });
}

//"/patrol/bridgeCheckDetail/getByCondition
export function getByCondition(params) {
  return request({
    url: "/patrol/bridgeCheckDetail/getByCondition",
    method: "get",
    params,
    notVerifyDuplicates: true
  });
}


//app/statistics/getWhetherHealth 
export function getWhetherHealth(params) {
  return request({
    url: "/baseData/app/statistics/getWhetherHealth",
    method: "get",
    params,
    notVerifyDuplicates: true
  });
}

///app/statistics/getMaintenanceQuantity
export function getMaintenanceQuantity(data) {
  return request({
    url: "/baseData/app/statistics/getMaintenanceQuantity",
    method: "post",
    data,
    notVerifyDuplicates: true
  });
}

///patrol/deviceCheck/list
export function getDeviceCheckList(params) {
  return request({
    url: "/patrol/deviceCheck/list",
    method: "get",
    params,
    notVerifyDuplicates: true
  });
}

///patrol/detail/getByCondition
export function getDetailByCondition(params) {
  return request({
    url: "/patrol/detail/getByCondition",
    method: "get",
    params,
    notVerifyDuplicates: true
  });
}

///xboot/displayScreen/getAllStructureInfoGroupByDomain
/**
 * 健康检测 - 分布情况
 * @param {*} params 
 * @returns 
 */
export function getAllStructureInfoGroupByDomain(params) {
  return request({
    url: "https://jkjc.yciccloud.com:8000/xboot/displayScreen/getAllStructureInfoGroupByDomain",
    method: "get",
    params,
    notVerifyDuplicates: true
  });
}

//https://jkjc.yciccloud.com:8000/xboot/displayScreen/default/getDataCount
/**
 * 健康检测 - 监测数据统计
 * @param {*} params 
 * @returns 
 */
export function getDataCount(params) {
  return request({
    url: "https://jkjc.yciccloud.com:8000/xboot/displayScreen/default/getDataCount",
    method: "get",
    params,
    notVerifyDuplicates: true
  });
}

//https://jkjc.yciccloud.com:8000/xboot/structureNormalDataManage/getStructureTypeNunber
/**
 * 健康检测 - 检测物结构统计
 * @param {*} params 
 * @returns 
 */
export function getStructureTypeNunber(params) {
  return request({
    url: "https://jkjc.yciccloud.com:8000/xboot/displayScreen/default/getStructureTypeNunber",
    method: "get",
    params,
    notVerifyDuplicates: true
  });
}

//https://jkjc.yciccloud.com:8000/xboot/displayScreen/default/getSensorCountKindByMonitorType
/**
 * 健康检测 - 检测物类别统计
 * @param {*} params 
 * @returns 
 */
export function getSensorCountKindByMonitorType(params) {
  return request({
    url: "https://jkjc.yciccloud.com:8000/xboot/displayScreen/default/getSensorCountKindByMonitorType",
    method: "get",
    params,
    notVerifyDuplicates: true
  });
}

//https://jkjc.yciccloud.com:8000/xboot/displayScreen/default/getNotDealRecordInformation?structureCodes=BS_BT_LJTDQ,BS_BL_DX3H_YF,BS_BL_DX3H_ZF,ZT_ZH_SPGJQ
/**
 * 健康检测 - 报警状态
 * @param {*} params 
 * @returns 
 */
export function getNotDealRecordInformation(params={structureCodes:"BS_BT_LJTDQ,BS_BL_DX3H_YF,BS_BL_DX3H_ZF,ZT_ZH_SPGJQ"}) {
  return request({
    url: "https://jkjc.yciccloud.com:8000/xboot/displayScreen/default/getNotDealRecordInformation",
    method: "get",
    params,
    notVerifyDuplicates: true
  });
}


