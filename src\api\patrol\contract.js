import request from '@/utils/request'

// 查询合同数据列表
export function listContract(query) {
  return request({
    url: '/patrol/contract/list',
    method: 'get',
    params: query
  })
}

// 查询合同数据详细
export function getContract(id) {
  return request({
    url: '/patrol/contract/get/' + id,
    method: 'get'
  })
}

// 新增合同数据
export function addContract(data) {
  return request({
    url: '/patrol/contract/add',
    method: 'post',
    data: data
  })
}

// 修改合同数据
export function updateContract(data) {
  return request({
    url: '/patrol/contract/edit',
    method: 'put',
    data: data
  })
}

// 删除合同数据
export function delContract(id) {
  return request({
    url: '/patrol/contract/delete/' + id,
    method: 'delete'
  })
}
