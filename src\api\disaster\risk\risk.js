import request from '@/utils/request'

// 查询灾害主表数据（分页）
export function queryPageRisk(query) {
  return request({
    url: '/disaster/disaster-risk/list',
    method: 'get',
    params: query
  })
}

// 查询灾害子表数据（分页）
export function queryPageRiskSub(id) {
  return request({
    url: `/disaster/disaster-risk/${id}`,
    method: 'get',
  })
}


// 查询灾害流程数据
export function queryPageRiskForm(id) {
  return request({
    url: `/disaster/disaster-risk/getDataByBusinessId/${id}`,
    method: 'get',
  })
}

// 删除灾害数据
export function deleteRisk(id) {
  return request({
    url: `/disaster/disaster-risk/${id}`,
    method: 'delete',
  })
}

export function dataSync() {
  return request({
    url: `/disaster/disaster-risk/update/info`,
    method: 'put',
  })
}

export function revokeProcess(params) {
  return request({
    url: `/disaster/disaster-process/revoke`,
    method: 'post',
    params:params
  })
}

// 批量提交风险点数据
export function batchStartProcess(id) {
  return request({
    url: `/disaster/disaster-risk/process/start/${id}`,
    method: 'post',
  })
}
