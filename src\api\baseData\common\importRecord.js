import request from '@/utils/request'

/**
 * 新增基础数据导入记录
 */
export function add(data) {
    return request({
        url: '/baseData/importRecord/add',
        method: 'post',
        data: data
    })
}

/**
 * 分页查询基础数据导入记录
 */
export function getListPage(params) {
    return request({
        url: '/baseData/importRecord/getListPage',
        method: 'get',
        params: params
    })
}

/**
 * 批量基础数据导入
 */
export function importExecute(data) {
    return request({
        url: '/baseData/importRecord/importExecute',
        method: 'post',
        data: data
    })
}

/**
 * 批量基础数据导入
 */
export function importExecuteWithCheckFlag(data) {
    return request({
        url: '/baseData/importRecord/importExecuteWithCheckFlag',
        method: 'post',
        data: data
    })
}

/**
 * 批量基础数据导入
 */
export function getInfoById(params) {
    return request({
        url: '/baseData/importRecord/getInfoById',
        method: 'get',
        params: params
    })
}

/**
 * 批量基础数据导入
 */
export function del(data) {
    return request({
        url: '/baseData/importRecord/del',
        method: 'delete',
        data: data
    })
}

/**
 * 批量基础数据导入
 */
export function downloadErrFile(params) {
  return request({
    url: '/baseData/importRecord/downloadErrFile',
    method: 'get',
    params: params
  })
}


/**
 * 模版下载
 * @param {*} data
 * @returns
 */
export function downloadExportTemplate(data) {
  return request({
      url: '/baseData/importRecord/downloadExcelTemplate',
      method: 'post',
      data
  })
}
