import request from '@/utils/request'

// 查询疑似风险路段清单列表
export function listRiskSections(query) {
  return request({
    url: '/middleData/riskSections/list',
    method: 'get',
    params: query
  })
}

// 查询疑似风险路段清单详细
export function getRiskSections(id) {
  return request({
    url: '/middleData/riskSections/get/' + id,
    method: 'get'
  })
}

// 新增疑似风险路段清单
export function addRiskSections(data) {
  return request({
    url: '/middleData/riskSections/add',
    method: 'post',
    data: data
  })
}

// 修改疑似风险路段清单
export function updateRiskSections(data) {
  return request({
    url: '/middleData/riskSections/edit',
    method: 'put',
    data: data
  })
}

// 删除
export function delData(data) {
  return request({
    url: `/middleData/riskSections/delete`,
    method: 'delete',
    data: data.idList,
  });
}

// 查询疑似风险路段清单列表(不分页)
export function listRiskSectionsAll(query) {
  return request({
    url: '/middleData/riskSections/listAll',
    method: 'get',
    params: query
  })
}
