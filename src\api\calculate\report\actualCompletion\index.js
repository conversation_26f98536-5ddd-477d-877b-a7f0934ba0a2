import request from '@/utils/request'

// S01报表
export function s01report(repositoryDTO) {
	return request({
		url: '/manager/calcreportdata/s01report',
		method: 'post',
		data: repositoryDTO
	});
}

// S02报表
export function s02report(repositoryDTO) {
	return request({
		url: '/manager/calcreportdata/s02report',
		method: 'post',
		data: repositoryDTO
	});
}

// S03报表
export function s03report(repositoryDTO) {
  return request({
    url: '/manager/calcreportdata/s03report',
    method: 'post',
    data: repositoryDTO
  });
}


// S04报表
export function s04report(repositoryDTO) {
	return request({
		url: '/manager/calcreportdata/s04report',
		method: 'post',
		data: repositoryDTO
	});
}
