import request from '@/utils/request'

// 查询填报记录列表
export function listRepoteRecord(query) {
  return request({
    url: '/repote/repoteRecord/list',
    method: 'get',
    params: query
  })
}

// 查询填报记录详细
export function getRepoteRecord(id) {
  return request({
    url: '/repote/repoteRecord/get/' + id,
    method: 'get'
  })
}

// 新增填报记录
export function addRepoteRecord(data) {
  return request({
    url: '/repote/repoteRecord/add',
    method: 'post',
    data: data
  })
}

// 修改填报记录
export function updateRepoteRecord(data) {
  return request({
    url: '/repote/repoteRecord/edit',
    method: 'put',
    data: data
  })
}

// 删除填报记录
export function delRepoteRecord(id) {
  return request({
    url: '/repote/repoteRecord/delete/' + id,
    method: 'delete'
  })
}
