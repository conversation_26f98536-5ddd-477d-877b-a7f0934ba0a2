import request from '@/utils/request'

// 管养处树
export function getDomainTree() {
  return request({
    url: '/jgjcadmin/api/flash/structure/getDomainTree',
    method: 'get'
  })
}

// 结构物管理管养处树
export function getManageDomainTree() {
  return request({
    url: '/jgjcadmin/api/flash/structure/getDomainTreeByMaint ',
    method: 'post'
  })
}

// 修改结构物
export function editStructure(data) {
  return request({
    url: '/jgjcadmin/api/flash/structure/update',
    method: 'post',
    data
  })
}

// 查询
export function listStructure(data) {
  return request({
    url: '/jgjcadmin/api/flash/structure/page',
    method: 'post',
    data
  })
}

// 新增结构物
export function addStructure(data) {
  return request({
    url: '/jgjcadmin/api/flash/structure/save',
    method: 'post',
    data
  })
}

// 删除结构物
export function delStructure(data) {
  return request({
    url: '/jgjcadmin/api/flash/structure/delete',
    method: 'post',
    data
  })
}


// 修改结构物
export function editFlashdevice(data) {
  return request({
    url: '/jgjcadmin/api/flash/flashdevice/update',
    method: 'post',
    data
  })
}

// 查询
export function listFlashdevice(data) {
  return request({
    url: '/jgjcadmin/api/flash/flashdevice/page',
    method: 'post',
    data
  })
}

// 新增结构物
export function addFlashdevice(data) {
  return request({
    url: '/jgjcadmin/api/flash/flashdevice/save',
    method: 'post',
    data
  })
}

// 删除结构物
export function delFlashdevice(data) {
  return request({
    url: '/jgjcadmin/api/flash/flashdevice/delete',
    method: 'post',
    data
  })
}


// 匹配
export function getMaintStructInfo(data) {
  return request({
    url: '/jgjcadmin/api/flash/structure/getMaintStructInfo',
    method: 'post',
    data
  })
}

// 批量新增
export function batchSave(data) {
  return request({
    url: '/jgjcadmin/api/flash/structure/batch-save',
    method: 'post',
    data
  })
}


// 更新结构物是否有监测数据
export function updateIsHasSensor(data) {
  return request({
    url: '/jgjcadmin/api/flash/structure/updateIsHasSensor',
    method: 'post',
    data
  })
}


// 结构物视频列表
export function getVideoList(data) {
  return request({
    url: '/jgjcadmin/api/basedata/structurevideo/page',
    method: 'post',
    data
  })
}

// 新增结构物视频
export function addVideo(data) {
  return request({
    url: '/jgjcadmin/api/basedata/structurevideo/save',
    method: 'post',
    data
  })
}

// 删除结构物视频
export function delVideo(data) {
  return request({
    url: '/jgjcadmin/api/basedata/structurevideo/delete',
    method: 'post',
    data
  })
}

// 编辑结构物视频
export function updateVideo(data) {
  return request({
    url: '/jgjcadmin/api/basedata/structurevideo/update',
    method: 'post',
    data
  })
}
