import request from '@/utils/request'

// 查询山洪淹没区路段清单列表
export function listHazardSections(query) {
  return request({
    url: '/middleData/hazardSections/list',
    method: 'get',
    params: query
  })
}

// 查询山洪淹没区路段清单详细
export function getHazardSections(id) {
  return request({
    url: '/middleData/hazardSections/get/' + id,
    method: 'get'
  })
}

// 新增山洪淹没区路段清单
export function addHazardSections(data) {
  return request({
    url: '/middleData/hazardSections/add',
    method: 'post',
    data: data
  })
}

// 修改山洪淹没区路段清单
export function updateHazardSections(data) {
  return request({
    url: '/middleData/hazardSections/edit',
    method: 'put',
    data: data
  })
}

// 删除
export function delData(data) {
  return request({
    url: `/middleData/hazardSections/delete`,
    method: 'delete',
    data: data.idList,
  });
}
