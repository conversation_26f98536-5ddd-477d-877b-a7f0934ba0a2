import request from '@/utils/request'

/**
 * 互通管理相关接口
 */

// 查询匝道列表
export function interflowList(data) {
  return request({
    url: '/baseData/routeInterFlow/list',
    method: 'post',
    data
  })
}

// 添加匝道信息
export function interflowAdd(data) {
  return request({
    url: '/baseData/routeInterFlow/add',
    method: 'post',
    data
  })
}

// 修改匝道信息
export function interflowEdit(data) {
  return request({
    url: '/baseData/routeInterFlow/edit',
    method: 'put',
    data
  })
}

// 查询匝道信息
export function interflowGetById(id) {
  return request({
    url: '/baseData/routeInterFlow/getInfoById',
    method: 'get',
    params: { id }
  })
}

// 根据id删除匝道信息
export function interflowDelete(data) {
  return request({
    url: `/baseData/routeInterFlow/delete`,
    method: 'delete',
    data: data
  })
}

// 导出匝道列表数据
export function interflowExport(data) {
  return request({
    url: `/baseData/routeInterflow/export`,
    method: 'post',
    data
  })
}

// 导出匝道列表数据
export function routeInterflowLock(data) {
  return request({
    url: `/baseData/routeInterFlow/setLock/${data.isLock}`,
    method: 'post',
    data: data.ids
  })
}

// 导入数据
export function importData(data) {
  return request({
    url: `/baseData/routeInterFlow/importData`,
    method: 'post',
    data
  })
}

// 暂存匝道信息
export function interflowTemp(data) {
  return request({
    url: '/baseData/routeInterFlow/tempAdd',
    method: 'post',
    data
  })
}