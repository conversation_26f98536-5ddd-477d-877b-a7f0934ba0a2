import request from '@/utils/request'

// 查询日养施工通知单签发列表(分页)
export function listNoticeIssue(data) {
    return request({
        url: '/manager/constructionIssuance/list',
        method: 'post',
        data: data
    });
}

// 查询日养施工通知单签发详情
export function getConstructionDetail(id) {
    return request({
        url: `/manager/constructionIssuance/getConstructionDetail/${id}`,
        method: 'get'
    });
}

// 签发
export function reviewNoticeIssue(data) {
    return request({
        url: '/manager/constructionIssuance/review',
        method: 'put',
        data: data
    });
}

// 批量签发
export function batchReviewNoticeIssue(data) {
    return request({
        url: '/manager/constructionIssuance/batchReview',
        method: 'put',
        data: data
    });
}

// 驳回
export function rejectNoticeIssue(data) {
    return request({
        url: '/manager/constructionIssuance/reject',
        method: 'put',
        data: data
    });
}

// 批量驳回
export function batchRejectNoticeIssue(data) {
    return request({
        url: '/manager/constructionIssuance/batchReject',
        method: 'put',
        data: data
    });
}

// 导出日养施工通知单签发列表
export function exportList() {
    return request({
        url: '/manager/constructionIssuance/export',
        method: 'post'
    });
}
