import request from '@/utils/request'

// 查询山洪淹没区风险路段列表
export function listMountainFloodRiskSections(query) {
  return request({
    url: '/middleData/mountainFloodRiskSections/list',
    method: 'post',
    params: {pageNum: query.pageNum, pageSize: query.pageSize},
    data: query
  })
}

// 查询山洪淹没区风险路段详细
export function getMountainFloodRiskSections(id) {
  return request({
    url: '/middleData/mountainFloodRiskSections/get/' + id,
    method: 'get'
  })
}

// 新增山洪淹没区风险路段
export function addMountainFloodRiskSections(data) {
  return request({
    url: '/middleData/mountainFloodRiskSections/add',
    method: 'post',
    data: data
  })
}

// 修改山洪淹没区风险路段
export function updateMountainFloodRiskSections(data) {
  return request({
    url: '/middleData/mountainFloodRiskSections/edit',
    method: 'put',
    data: data
  })
}

// 删除
export function delData(data) {
  return request({
    url: `/middleData/mountainFloodRiskSections/delete`,
    method: 'delete',
    data: data.idList,
  });
}

// 批量审核
export function batchAudit(data) {
  return request({
    url: '/middleData/mountainFloodRiskSections/batchAudit',
    method: 'post',
    data: data
  })
}
