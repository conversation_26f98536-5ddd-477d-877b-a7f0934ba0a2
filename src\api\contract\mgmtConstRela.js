import request from '@/utils/request'

// 新增
export function addMgmtCOnstRela(data) {
  return request({
    url: '/manager/maiconunitrelation/add',
    method: 'post',
    data: data
  })
}
// 查询
export function getList(data) {
  return request({
    url: '/manager/maiconunitrelation/list',
    method: 'post',
    data: data
  })
}
// 修改
export function editMgmtConstRela(data) {
  return request({
    url: '/manager/maiconunitrelation/edit',
    method: 'put',
    data: data
  })
}
// 删除
export function deleteMgmtConstRela(id) {
  return request({
    url: `/manager/maiconunitrelation/delete/${id}`,
    method: 'delete',
  })
}
