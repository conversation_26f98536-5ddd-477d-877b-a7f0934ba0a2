import request from '@/utils/request';

/**
 * 设备维修登记API
 */
export function getDeviceMaintenanceRecordPage(data) {
  return request({
    url: '/jgjcadmin/api/basedata/devicemaintenacerecord/page',
    method: 'post',
    data
  })
}

export function saveDeviceMaintenanceRecord(data) {
  return request({
    url: '/jgjcadmin/api/basedata/devicemaintenacerecord/save',
    method: 'post',
    data
  })
}

export function updateDeviceMaintenanceRecord(data) {
  return request({
    url: '/jgjcadmin/api/basedata/devicemaintenacerecord/update',
    method: 'post',
    data
  })
}

export function deleteDeviceMaintenanceRecord(data) {
  return request({
    url: '/jgjcadmin/api/basedata/devicemaintenacerecord/delete',
    method: 'post',
    data
  })
}

/**
 * 设备维修分析API
 */
export function getDeviceMaintenanceAnalyzePage(data) {
  return request({
    url: '/jgjcadmin/api/basedata/devicemaintenaceanalyze/page',
    method: 'post',
    data
  })
}

export function saveDeviceMaintenanceAnalyze(data) {
  return request({
    url: '/jgjcadmin/api/basedata/devicemaintenaceanalyze/save',
    method: 'post',
    data
  })
}

export function updateDeviceMaintenanceAnalyze(data) {
  return request({
    url: '/jgjcadmin/api/basedata/devicemaintenaceanalyze/update',
    method: 'post',
    data
  })
}

export function deleteDeviceMaintenanceAnalyze(data) {
  return request({
    url: '/jgjcadmin/api/basedata/devicemaintenaceanalyze/delete',
    method: 'post',
    data
  })
}

/**
 * 设备维修处理API
 */
export function getDeviceMaintenanceDealPage(data) {
  return request({
    url: '/jgjcadmin/api/basedata/devicemaintenacedeal/page',
    method: 'post',
    data
  })
}

export function saveDeviceMaintenanceDeal(data) {
  return request({
    url: '/jgjcadmin/api/basedata/devicemaintenacedeal/save',
    method: 'post',
    data
  })
}

export function updateDeviceMaintenanceDeal(data) {
  return request({
    url: '/jgjcadmin/api/basedata/devicemaintenacedeal/update',
    method: 'post',
    data
  })
}

export function deleteDeviceMaintenanceDeal(data) {
  return request({
    url: '/jgjcadmin/api/basedata/devicemaintenacedeal/delete',
    method: 'post',
    data
  })
}
