import request from '@/utils/request'

// 结算库列表(分页)
export function otherRepositoryList(repositoryDTO) {
  return request({
    url: '/manager/calc/project/settle/list',
    method: 'post',
    data: repositoryDTO
  });
}

// 根据id查询子目列表（分页）
export function fetchMethodList(data) {
  return request({
    url: `/manager/calc/project/settle/method/list`,
    method: 'post',
    data
  });
}

// 根据id查询子目列表（不分页）
export function fetchMethodListAll(data) {
  return request({
    url: `/manager/calc/project/settle/method/listAll`,
    method: 'post',
    data
  });
}


export function updateOtherMethodList(data) {
  return request({
    url: `/manager/calc/project/settle/method/update/number`,
    method: 'put',
    data
  });
}




// 签证单预览
export function previewVisa(id) {
  return request({
    url: `/manager/calc/project/settle/visa/preview?settleId=${id}`,
    method: 'get'
  });
}


// 维修档案预览
export function preViewMaintenance(id) {
  return request({
    url: `/manager/calc/project/settle/maintain/preview?settleId=${id}`,
    method: 'get'
  });
}


// 修复结果反馈预览
export function previewRepair(id) {
  return request({
    url: `/manager/calc/project/settle/repair/preview?settleId=${id}`,
    method: 'get'
  });
}


export function updateSafetyFee(data) {
  return request({
    url: `/manager/calc/project/settle/method/update/safetyFee`,
    method: 'put',
    data
  });
}


// 获取签证人员信息
export function getVisaCheck(constructionId) {
  return request({
    url: `/manager/calc/project/settle/get/visacheck/${constructionId}`,
    method: 'get',
  });
}

export function updateVisaCheck(data) {
  return request({
    url: `/manager/calc/project/settle/update/visacheck`,
    method: 'put',
    data
  });
}


export function withdraw(data) {
  return request({
    url: '/manager/proj/construction/finished/withdraw',
    method: 'post',
    data: data
  })
}
