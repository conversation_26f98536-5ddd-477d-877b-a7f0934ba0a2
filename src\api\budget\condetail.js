import request from '@/utils/request'

// 查询方案合同明细列表
export function listCondetail(query) {
  return request({
    url: '/budget/condetail/list',
    method: 'get',
    params: query
  })
}

// 查询方案合同明细详细
export function getCondetail(id) {
  return request({
    url: '/budget/condetail/get/' + id,
    method: 'get'
  })
}

// 新增方案合同明细
export function addCondetail(data) {
  return request({
    url: '/budget/condetail/add',
    method: 'post',
    data: data
  })
}

// 修改方案合同明细
export function updateCondetail(data) {
  return request({
    url: '/budget/condetail/edit',
    method: 'put',
    data: data
  })
}

// 删除方案合同明细
export function delCondetail(id) {
  return request({
    url: '/budget/condetail/delete/' + id,
    method: 'delete'
  })
}
