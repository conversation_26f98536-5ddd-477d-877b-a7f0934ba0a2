import request from '@/utils/request';
export function addSampleProject(data) {
	return request({
		url: '/jgjcadmin/api/basedata/sampleproject/save',
		method: 'post',
		data
	});
}

export function updateSampleProject(data) {
	return request({
		url: '/jgjcadmin/api/basedata/sampleproject/update',
		method: 'post',
		data
	});
}

export function deleteSampleProject(data) {
	return request({
		url: '/jgjcadmin/api/basedata/sampleproject/delete',
		method: 'post',
		data
	});
}

export function listSampleProjects(data) {
	return request({
		url: '/jgjcadmin/api/basedata/sampleproject/page',
		method: 'post',
		data
	});
}

export function batchAddSampleProjects(data) {
	return request({
		url: '/jgjcadmin/api/basedata/sampleproject/batchAdd',
		method: 'post',
		data
	});
}

export function getSampleProjectSelectData(data) {
	return request({
		url: '/jgjcadmin/api/basedata/sampleproject/selectData',
		method: 'post',
		data
	});
}
