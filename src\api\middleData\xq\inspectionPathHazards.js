import request from '@/utils/request'

// 查询涉灾隐患点—检查步道路列表
export function listInspectionPathHazards(query) {
  return request({
    url: '/middleData/inspectionPathHazards/list',
    method: 'post',
    params: {pageNum: query.pageNum, pageSize: query.pageSize},
    data: query
  })
}

// 查询涉灾隐患点—检查步道路详细
export function getInspectionPathHazards(id) {
  return request({
    url: '/middleData/inspectionPathHazards/get/' + id,
    method: 'get'
  })
}

// 新增涉灾隐患点—检查步道路
export function addInspectionPathHazards(data) {
  return request({
    url: '/middleData/inspectionPathHazards/add',
    method: 'post',
    data: data
  })
}

// 修改涉灾隐患点—检查步道路
export function updateInspectionPathHazards(data) {
  return request({
    url: '/middleData/inspectionPathHazards/edit',
    method: 'put',
    data: data
  })
}

// 删除
export function delData(data) {
  return request({
    url: `/middleData/inspectionPathHazards/delete`,
    method: 'delete',
    data: data.idList,
  });
}

// 批量审核
export function batchAudit(data) {
  return request({
    url: '/middleData/inspectionPathHazards/batchAudit',
    method: 'post',
    data: data
  })
}
