import request from '@/utils/request'

// 查询预算计划金额列表
export function listBudgetfund(query) {
  return request({
    url: '/budget/budgetfund/list',
    method: 'get',
    params: query
  })
}

// 查询预算计划金额详细
export function getBudgetfund(id) {
  return request({
    url: '/budget/budgetfund/get/' + id,
    method: 'get'
  })
}

// 新增预算计划金额
export function addBudgetfund(data) {
  return request({
    url: '/budget/budgetfund/add',
    method: 'post',
    data: data
  })
}

// 修改预算计划金额
export function updateBudgetfund(data) {
  return request({
    url: '/budget/budgetfund/edit',
    method: 'put',
    data: data
  })
}

// 删除预算计划金额
export function delBudgetfund(id) {
  return request({
    url: '/budget/budgetfund/delete/' + id,
    method: 'delete'
  })
}
