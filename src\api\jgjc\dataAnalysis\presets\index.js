import request from '@/utils/request'

// 查询结构物代码下的配置
export function getConfigUnderStructureCode(data) {
  return request({
    url: '/jgjcadmin/api/dataanalysis/custom-analysis/getConfigUnderStructureCode',
    method: 'post',
    data: data,
    notVerifyDuplicates: true
  })
}

// 添加一个配置
export function addOneConfig(data) {
  return request({
    url: '/jgjcadmin/api/dataanalysis/custom-analysis/addOne',
    method: 'post',
    data: data
  })
}

// 根据ID更新一个配置
export function updateOneConfigById(data) {
  return request({
    url: '/jgjcadmin/api/dataanalysis/custom-analysis/updateOneById',
    method: 'post',
    data: data
  })
}

// 根据ID删除一个配置
export function deleteConfigById(data) {
  return request({
    url: '/jgjcadmin/api/dataanalysis/custom-analysis/deleteById',
    method: 'post',
    data: data
  })
}

// 批量删除配置
export function deleteConfigsBatch(data) {
  return request({
    url: '/jgjcadmin/api/dataanalysis/custom-analysis/deleteBatch',
    method: 'post',
    data: data
  })
}


export function drawChart(data) {
  return request({
    url: '/jgjcadmin/api/dataanalysis/custom-analysis/draw',
    method: 'post',
    data: data,
    notVerifyDuplicates: true
  })
}


