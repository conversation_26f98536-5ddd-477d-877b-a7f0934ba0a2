import request from '@/utils/request'

// 查询厂家列表
export function listManufacturer(query) {
  return request({
    url: '/jgjc-manager/api/console/jc/manufacturer/findPage',
    method: 'post',
    data: query
  })
}

// 查询厂家详细
export function getManufacturer(id) {
  return request({
    url: '/jgjc-manager/api/console/jc/manufacturer/findById?id=' + id,
    method: 'get'
  })
}

// 新增厂家
export function addManufacturer(data) {
  return request({
    url: '/jgjc-manager/api/console/jc/manufacturer/create',
    method: 'post',
    data: data
  })
}

// 修改厂家
export function updateManufacturer(data) {
  return request({
    url: '/jgjc-manager/api/console/jc/manufacturer/modify',
    method: 'post',
    data: data
  })
}

// 删除厂家
export function delManufacturer(id) {
  return request({
    url: '/jgjc-manager/api/console/jc/manufacturer/removeById?id=' + id,
    method: 'delete'
  })
}
// 批量删除厂家
export function delManufacturerBatch(ids) {
  return request({
    url: '/jgjc-manager/api/console/jc/manufacturer/batchRemove',
    method: 'post',
    data: ids
  })
}
