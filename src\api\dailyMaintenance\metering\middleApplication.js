import request from '@/utils/request'

// 查询日养中间计量单列表(分页)
export function listDaliyIntermediate(data) {
    return request({
        url: '/manager/intermediate/list',
        method: 'get',
        params: data,
        notVerifyDuplicates: true
    });
}

// 查询路段id查询未关联结算计量单的中间计量单
export function listByMaiSecId(maiSecId) {
    return request({
        url: `/manager/intermediate/list/data?maiSecId=${maiSecId}`,
        method: 'get'
    });
}

// 查询日养中间计量单列表(不分页)
export function listAllDaliyIntermediate() {
    return request({
        url: '/manager/intermediate/listAll',
        method: 'get'
    });
}


// 根据id查询日养中间计量单数据
export function getDaliyIntermediateById(id) {
    return request({
        url: `/manager/intermediate/get/${id}`,
        method: 'get'
    });
}

// 新增日养中间计量单
export function addDaliyIntermediate(data) {
    return request({
        url: '/manager/intermediate/add',
        method: 'post',
        data
    });
}

// 修改日养中间计量单
export function editDaliyIntermediate(data) {
    return request({
        url: '/manager/intermediate/edit',
        method: 'put',
        data
    });
}

// 删除日养中间计量单
export function deleteDaliyIntermediate(id) {
    return request({
        url: `/manager/intermediate/delete/${id}`,
        method: 'delete'
    });
}

// 导出日养中间计量单列表
export function exportDaliyIntermediate() {
    return request({
        url: '/manager/intermediate/export',
        method: 'post'
    });
}

// 报表预览
export function previewReport(id) {
    return request({
        url: '/manager/intermediate/report/preview',
        method: 'post',
        data: { id }
    });
}

// 新增明细查询
export function listSettle(data) {
    return request({
        url: '/manager/settle/list',
        method: 'get',
        params: data,
        notVerifyDuplicates: true
    });
}

// 新增中间计量单明细
export function addDetail(params) {
    return request({
        url: '/manager/intermediate/detail/add',
        method: 'post',
        data: params
    });
}

// 查询事件信息
export function listEvent(data) {
    return request({
        url: '/manager/intermediate/detail/list/event',
        method: 'get',
        params: data
    });
}

// 删除日养中间计量单
export function deleteEvent(ids) {
    return request({
        url: `/manager/intermediate/detail/delete/${ids}`,
        method: 'delete'
    });
}


// 查询中间计量
export function listMethod(data) {
    return request({
        url: '/manager/method/list',
        method: 'get',
        params: data
    });
}

// 获取子目方法列表
export function listMethodBySettleId(data) {
    return request({
        url: '/manager/settle/list/method',
        method: 'get',
        params: data
    });
}


// 日养中间计量单待审核列表(分页)
export function listPendingDaliyIntermediate(data) {
  return request({
    url: '/manager/intermediate/pending/list',
    method: 'get',
    params: data,
    notVerifyDuplicates: true
  });
}


// 中间计量单提交
export function submit(params) {
  return request({
    url: '/manager/intermediate/submit',
    method: 'post',
    data: params
  });
}


// 中间计量单审核
export function process(params) {
  return request({
    url: '/manager/intermediate/process',
    method: 'post',
    data: params
  });
}


// 中间计量单审核
export function getNodeInfo(params) {
  return request({
    url: '/manager/intermediate/node/info',
    method: 'post',
    data: params
  });
}


// 根据中间计量单明细id查询事件下子目数据
export function listEventBySettleId(data) {
    return request({
        url: '/manager/intermediate/detail/list/method/',
        method: 'get',
        params: data
    });
}


// 中间计量单预览
export function previewIntermediateReport(id) {
  return request({
    url: `/manager/intermediate/report/preview?id=${id}`,
    method: 'get'
  });
}


// 中间计量单下载
export function downloadIntermediateReport(id) {
    return request({
        url: `/manager/intermediate/report/download?id=${id}`,
        method: 'get'
    });
}


// 日养中间计量单查看列表(分页)
export function listViewDaliyIntermediate(data) {
  return request({
    url: '/manager/intermediate/view',
    method: 'get',
    params: data,
    notVerifyDuplicates: true
  });
}


export function updateFund(id) {
  return request({
    url: `/manager/intermediate/update/fund?id=${id}`,
    method: 'post'
  });
}

export function withdraw(data) {
  return request({
    url: '/manager/intermediate/withdraw',
    method: 'post',
    data: data
  })
}

export function packDownload(id) {
  return request({
    url: `/manager/intermediate/pack/download?id=${id}`,
    method: 'get'
  })
}
