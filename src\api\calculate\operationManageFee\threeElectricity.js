import request from '@/utils/request'

// 查询三大系统电费列表(分页)
export function listElectricityPage(data) {
    return request({
        url: 'manager/operate/electricity/list',
        method: 'post',
        data
    });
}

// 查询三大系统电费列表(不分页)
export function listElectricityAll() {
    return request({
        url: 'manager/operate/electricity/listAll',
        method: 'get'
    });
}

// 根据id查询三大系统电费数据
export function getElectricityById(id) {
    return request({
        url: `manager/operate/electricity/get/${id}`,
        method: 'get'
    });
}

// 新增三大系统电费
export function addElectricity(data) {
    return request({
        url: 'manager/operate/electricity/add',
        method: 'post',
        data
    });
}

// 修改三大系统电费
export function editElectricity(data) {
    return request({
        url: 'manager/operate/electricity/edit',
        method: 'put',
        data
    });
}

// 删除三大系统电费
export function deleteElectricity(id) {
    return request({
        url: `manager/operate/electricity/delete/${id}`,
        method: 'delete'
    });
}
