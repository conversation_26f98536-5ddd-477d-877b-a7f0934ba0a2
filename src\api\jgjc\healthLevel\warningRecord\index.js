import request from '@/utils/request'


export function getLatestProcessedByPage(data) {
  return request({
    url: '/jgjcadmin/api/healthassessment/alertrecord/getLatestProcessedByPage',
    method: 'get',
    params: data
  });
}

export function getAlertRecordPage(data) {
  return request({
    url: '/jgjcadmin/api/healthassessment/alertrecord/page',
    method: 'get',
    params: data
  });
}

export function deleteAlertRecord(data) {
  return request({
    url: '/jgjcadmin/api/healthassessment/alertrecord/delete', // Note: endpoint has typo 'delet' instead of 'delete'
    method: 'post',
    data
  });
}

export function updateAlertRecord(data) {
  return request({
    url: '/jgjcadmin/api/healthassessment/alertrecord/update',
    method: 'post',
    data
  });
}

export function getAlertRecordStructDetail(data) {
  return request({
    url: '/jgjcadmin/api/healthassessment/alertrecord/structDetail', // Note: endpoint has typo 'structDetai' in your example
    method: 'post',
    data
  });
}
