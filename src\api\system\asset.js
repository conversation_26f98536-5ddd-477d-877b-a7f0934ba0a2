import request from '@/utils/request'
// 资产主类数据
export function typeGetList() {
    return request({
      url: '/baseData/asset/main/type/getList',
      method: 'get'
    })
  }

  // 资产主类数据
export function typeGetListPage(params) {
    return request({
      url: '/baseData/asset/type/getListPage',
      method: 'get',
      params
    })
  }

  //新增资产主类
export function fieldsAdd(data) {
    return request({
      url: '/baseData/dynamic/fields/add',
      method: 'post',
      data
    })
  }

  //s删除资产主类
export function fieldsDelete(data) {
    return request({
      url: '/baseData/dynamic/fields/delete/' + data,
      method: 'delete',
     
    })
  }

  //查询资产主类
export function fieldsGetlistPage(params) {
    return request({
      url: '/baseData/dynamic/fields/getListPage',
      method: 'get',
      params
    })
  }

  //注册资产主类
export function fieldsRegister(params) {
    return request({
      url: '/baseData/dynamic/fields/register',
      method: 'put',
      params
    })
  }

// 更新 类型
export function fieldsUpdate(data) {
  return request({
    url: '/baseData/dynamic/fields/edit',
    method: 'put',
    data
  })
}