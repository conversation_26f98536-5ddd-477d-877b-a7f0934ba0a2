import request from '@/utils/request'

// 查询设备信息列表
export function listLabordeviceinfo(query) {
  return request({
    url: '/manager/labordeviceinfo/listbyParam',
    method: 'post',
    data: query
  })
}

// 查询设备信息详细
export function getLabordeviceinfo(id) {
  return request({
    url: '/manager/labordeviceinfo/get/' + id,
    method: 'get'
  })
}

// 新增设备信息
export function addLabordeviceinfo(data) {
  return request({
    url: '/manager/labordeviceinfo/add',
    method: 'post',
    data: data
  })
}

// 修改设备信息
export function updateLabordeviceinfo(data) {
  return request({
    url: '/manager/labordeviceinfo/edit',
    method: 'put',
    data: data
  })
}

// 删除设备信息
export function delLabordeviceinfo(id) {
  return request({
    url: '/manager/labordeviceinfo/delete/' + id,
    method: 'delete'
  })
}
