import request from '@/utils/request'

// 应急物资出库单审核节点信息
export function getNodeInfo(data) {
  return request({
    url: '/manager/emergency/material/outbound/node/info',
    method: 'post',
    data
  })
}

// 获取出库申请单列表
export function getMaterialOutApplicitionList(data) {
  return request({
    url: '/manager/emergency/material/outbound/list',
    method: 'post',
    data
  })
}

// 新增出库申请
export function addMaterialOutApplicition(data) {
  return request({
    url: '/manager/emergency/material/outbound/add',
    method: 'post',
    data
  })
}

// 根据id查询应急物资出库单数据
export function getDetailOutList(id) {
  return request({
    url: `/manager/emergency/material/outbound/get/${id}`,
    method: 'get'
  })
}

// 编辑出库申请
export function editMaterialOutApplicition(data) {
  return request({
    url: '/manager/emergency/material/outbound/edit',
    method: 'put',
    data
  })
}

// 删除出库单
export function delMaterialOut(id) {
  return request({
    url: `/manager/emergency/material/outbound/delete/${id}`,
    method: 'delete',
  })
}

// 根据出库单id与物资库id查询应急物资出库单明细列表
export function getOutDetailList(data) {
  return request({
    url: '/manager/emergency/material/outbound/detail/list',
    method: 'post',
    data
  })
}

// 应急物资出库单审核
export function submitOutbound(data) {
  return request({
    url: '/manager/emergency/material/outbound/process',
    method: 'post',
    data
  })
}

// 查询应急物资出库单审核列表
export function getOutAuditList(data) {
  return request({
    url: '/manager/emergency/material/outbound/pending/list',
    method: 'post',
    data
  })
}

// 查询应急物资出库单结果列表
export function getOutResultList(data) {
  return request({
    url: '/manager/emergency/material/outbound/view',
    method: 'post',
    data
  })
}


// 报表
export function previewReport(id) {
  return request({
    url: `/manager/emergency/material/outbound/report/preview?id=${id}`,
    method: 'get',
  })
}

