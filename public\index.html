<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="renderer" content="webkit">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- <link rel="icon" href="<%= BASE_URL %>favicon.ico"> -->

    <!-- Luckysheet 资源 -->
    <!-- <link rel='stylesheet' href='<%= BASE_URL %>plugins/css/pluginsCss.css' />
    <link rel='stylesheet' href='<%= BASE_URL %>plugins/plugins.css' />
    <link rel='stylesheet' href='<%= BASE_URL %>css/luckysheet.css' />
    <link rel='stylesheet' href='<%= BASE_URL %>assets/iconfont/iconfont.css' />
    <script src="<%= BASE_URL %>plugins/js/plugin.js"></script>
    <script src="<%= BASE_URL %>luckysheet.umd.js"></script> -->
    <link rel='stylesheet' href='/plugins/css/pluginsCss.css' />
    <link rel='stylesheet' href='/plugins/plugins.css' />
    <link rel='stylesheet' href='/css/luckysheet.css' />
    <link rel='stylesheet' href='/assets/iconfont/iconfont.css' />
    <script src="/plugins/js/plugin.js"></script>
    <script src="/luckysheet.umd.js"></script>

    <title>
        <%= webpackConfig.name %>
    </title>
    <!--[if lt IE 11]><script>window.location.href='/html/ie.html';</script><![endif]-->
    <style>
        html,
        body,
        #app {
            height: 100%;
            margin: 0px;
            padding: 0px;
        }

        .chromeframe {
            margin: 0.2em 0;
            background: #ccc;
            color: #000;
            padding: 0.2em 0;
        }

        #loader-wrapper {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 999999;
        }

        .logo {
            display: block;
            position: relative;
            left: 48%;
            top: 36%;
            width: 60px;
            height: 60px;
            z-index: 1001;
        }

        #loader {
            display: block;
            position: relative;
            left: 50%;
            top: 30%;
            width: 150px;
            height: 150px;
            margin: -75px 0 0 -75px;
            border-radius: 50%;
            border: 3px solid transparent;
            border-top-color: #009944;
            -webkit-animation: spin 2s linear infinite;
            -ms-animation: spin 2s linear infinite;
            -moz-animation: spin 2s linear infinite;
            -o-animation: spin 2s linear infinite;
            animation: spin 2s linear infinite;
            z-index: 1001;
        }

        #loader:before {
            content: "";
            position: absolute;
            top: 5px;
            left: 5px;
            right: 5px;
            bottom: 5px;
            border-radius: 50%;
            border: 3px solid transparent;
            border-top-color: #34da7e;
            -webkit-animation: spin 3s linear infinite;
            -moz-animation: spin 3s linear infinite;
            -o-animation: spin 3s linear infinite;
            -ms-animation: spin 3s linear infinite;
            animation: spin 3s linear infinite;
        }

        #loader:after {
            content: "";
            position: absolute;
            top: 15px;
            left: 15px;
            right: 15px;
            bottom: 15px;
            border-radius: 50%;
            border: 3px solid transparent;
            border-top-color: #009944;
            -moz-animation: spin 1.5s linear infinite;
            -o-animation: spin 1.5s linear infinite;
            -ms-animation: spin 1.5s linear infinite;
            -webkit-animation: spin 1.5s linear infinite;
            animation: spin 1.5s linear infinite;
        }

        @-webkit-keyframes spin {
            0% {
                -webkit-transform: rotate(0deg);
                -ms-transform: rotate(0deg);
                transform: rotate(0deg);
            }

            100% {
                -webkit-transform: rotate(360deg);
                -ms-transform: rotate(360deg);
                transform: rotate(360deg);
            }
        }

        @keyframes spin {
            0% {
                -webkit-transform: rotate(0deg);
                -ms-transform: rotate(0deg);
                transform: rotate(0deg);
            }

            100% {
                -webkit-transform: rotate(360deg);
                -ms-transform: rotate(360deg);
                transform: rotate(360deg);
            }
        }

        #loader-wrapper .loader-section {
            position: fixed;
            top: 0;
            width: 51%;
            height: 100%;
            background: #fff;
            z-index: 1000;
            -webkit-transform: translateX(0);
            -ms-transform: translateX(0);
            transform: translateX(0);
        }

        #loader-wrapper .loader-section.section-left {
            left: 0;
        }

        #loader-wrapper .loader-section.section-right {
            right: 0;
        }

        .loaded #loader-wrapper .loader-section.section-left {
            -webkit-transform: translateX(-100%);
            -ms-transform: translateX(-100%);
            transform: translateX(-100%);
            -webkit-transition: all 0.7s 0.3s cubic-bezier(0.645, 0.045, 0.355, 1.000);
            transition: all 0.7s 0.3s cubic-bezier(0.645, 0.045, 0.355, 1.000);
        }

        .loaded #loader-wrapper .loader-section.section-right {
            -webkit-transform: translateX(100%);
            -ms-transform: translateX(100%);
            transform: translateX(100%);
            -webkit-transition: all 0.7s 0.3s cubic-bezier(0.645, 0.045, 0.355, 1.000);
            transition: all 0.7s 0.3s cubic-bezier(0.645, 0.045, 0.355, 1.000);
        }

        .loaded #loader {
            opacity: 0;
            -webkit-transition: all 0.3s ease-out;
            transition: all 0.3s ease-out;
        }

        .loaded #loader-wrapper {
            visibility: hidden;
            -webkit-transform: translateY(-100%);
            -ms-transform: translateY(-100%);
            transform: translateY(-100%);
            -webkit-transition: all 0.3s 1s ease-out;
            transition: all 0.3s 1s ease-out;
        }

        .no-js #loader-wrapper {
            display: none;
        }

        .no-js h1 {
            color: #222222;
        }

        #loader-wrapper .load_title {
            font-family: 'Open Sans';
            color: black;
            font-size: 19px;
            width: 100%;
            text-align: center;
            z-index: 9999999999999;
            position: absolute;
            top: 60%;
            opacity: 1;
            line-height: 30px;
        }

        #loader-wrapper .load_title span {
            font-weight: normal;
            font-style: italic;
            font-size: 13px;
            color: #FFF;
            opacity: 0.5;
        }
    </style>
</head>

<body>
    <div id="app">
        <div id="loader-wrapper">
            <!-- <img src="/static/img/logo.00b85b69.png" class="logo" /> -->
            <div id="loader"></div>
            <div class="loader-section section-left"></div>
            <div class="loader-section section-right"></div>
            <div class="load_title">正在加载系统资源，请耐心等待...</div>
        </div>
    </div>
</body>
<script>
    function changeFavicon(url) {
        let link = document.querySelector("link[rel*='icon']") || document.createElement('link');
        link.type = 'image/x-icon';
        link.rel = 'shortcut icon';
        link.href = url;
        document.getElementsByTagName('head')[0].appendChild(link);
    }
    const st1 = setInterval(() => {
        let logoInfo = localStorage.getItem('logoInfo')
        if (logoInfo) {
            console.log('已找到logo信息');
            logoInfo = JSON.parse(logoInfo)
            changeFavicon(logoInfo.topLogUrl)
            clearInterval(st1);
        } else {
            console.log('未找到logo信息，正在重试...');
        }
    }, 500)
    /* function addScript(usertoken) {
        // 创建一个新的script元素
        var script = document.createElement('script');
        script.type = 'text/javascript';
        // 设置script的src属性为你的JavaScript文件URL
        script.src = 'https://ai.glyhgl.com/api/application/embed?protocol=https&host=ai.glyhgl.com&token=a4e6281ecd47d0af&usertoken=' + usertoken;
        // 将script标签添加到document的head中
        document.head.appendChild(script);
    }
    document.addEventListener('DOMContentLoaded', function () {
        addScript('notoken')
        const st1 = setInterval(() => {
            let token = document.cookie.replace(/(?:(?:^|.*;\s*)Admin-Token\s*\=\s*([^;]*).*$)|^.*$/, "$1");
            const button = document.querySelector('.maxkb-chat-button');
            // 此处需判断ai DOM已经加载不然会导致加载两个ai
            if (token && button) {
                addScript(token);
                clearInterval(st1);
            }
        }, 2000)
        // 设置定时任务
        const intervalId = setInterval(() => {
            const button = document.querySelector('.maxkb-chat-button');
            if (button) {
                button.style.display = 'none';
                console.log('已获取到对象:', button);
                button.style.position = 'fixed';
                button.style.width = '78px';
                button.style.height = '90px';
                button.style.bottom = '100px';
                button.style.right = '30px';
                button.style.textAlign = 'center';
                const img = button.querySelector('img');
                console.log(img,'ggg');
                
                img.style.width = '60px';
                img.style.height = '60px';
                const spanElement = document.createElement('span');
                // 3. 设置 span 元素的内容
                spanElement.textContent = '养护小助手';
                spanElement.style.color = "white"
                spanElement.style.display = 'block';
                spanElement.style.textAlign = 'center';
                spanElement.style.backgroundColor = '#70C197';
                spanElement.style.borderRadius = '3px';
                spanElement.style.fontSize = '14px';
                spanElement.style.fontWeight = '600';
                spanElement.style.opacity = '0.9';
                spanElement.style.padding = '4px';
                // 4. 将 span 元素插入到 div 元素中
                button.appendChild(spanElement);
                // 终止定时任务
                clearInterval(intervalId);
                if (button) {
                    let isDragging = false;
                    let initialX;
                    let initialY;
                    let initialOffsetX;
                    let initialOffsetY;
                    // 鼠标按下事件
                    button.addEventListener('mousedown', function (e) {
                        isDragging = true;
                        initialX = e.clientX;
                        initialY = e.clientY;
                        initialOffsetX = button.offsetLeft;
                        initialOffsetY = button.offsetTop;
                        // 阻止默认事件，防止选中文字等操作
                        e.preventDefault();
                    });
                    // 鼠标移动事件
                    document.addEventListener('mousemove', function (e) {
                        if (isDragging) {
                            const currentX = e.clientX;
                            const currentY = e.clientY;
                            const deltaX = currentX - initialX;
                            const deltaY = currentY - initialY;
                            button.style.left = (initialOffsetX + deltaX) + 'px';
                            button.style.top = (initialOffsetY + deltaY) + 'px';
                        }
                    });
                    // 鼠标松开事件
                    document.addEventListener('mouseup', function (e) {
                        isDragging = false;
                        e.preventDefault();
                    });
                }
            } else {
                console.log('未获取到对象，继续尝试...');
            }
        }, 10); // 每隔 1 秒执行一次任务

    }); */
</script>

</html>