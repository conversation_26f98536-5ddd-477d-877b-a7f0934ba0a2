import request from '@/utils/request'

// 查询桥梁资产权限列表
export function listSysBridge(query) {
  return request({
    url: '/system/sysBridge/list',
    method: 'get',
    params: query
  })
}

// 根据条件查询已关联桥梁ID
export function getDefaultSelected(query) {
  return request({
    url: '/system/sysBridge/getDefaultSelected',
    method: 'get',
    params: query
  })
}

// 批量新增
export function addDeptBridge(data) {
  return request({
    url: '/system/sysBridge/addDeptBridge',
    method: 'post',
    data: data
  })
}

// 查询桥梁资产权限详细
export function getSysBridge(id) {
  return request({
    url: '/system/sysBridge/get/' + id,
    method: 'get'
  })
}

// 新增桥梁资产权限
export function addSysBridge(data) {
  return request({
    url: '/system/sysBridge/add',
    method: 'post',
    data: data
  })
}

// 修改桥梁资产权限
export function updateSysBridge(data) {
  return request({
    url: '/system/sysBridge/edit',
    method: 'put',
    data: data
  })
}

// 删除桥梁资产权限
export function delSysBridge(id) {
  return request({
    url: '/system/sysBridge/delete/' + id,
    method: 'delete'
  })
}
