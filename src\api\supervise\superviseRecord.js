import request from '@/utils/request'

// 查询督查记录列表
export function listSuperviseRecord(query) {
  return request({
    url: '/patrol/superviseRecord/list',
    method: 'get',
    params: query
  })
}

// 查询督查记录详细
export function getSuperviseRecord(id) {
  return request({
    url: '/patrol/superviseRecord/get/' + id,
    method: 'get'
  })
}

// 新增督查记录
export function addSuperviseRecord(data) {
  return request({
    url: '/patrol/superviseRecord/add',
    method: 'post',
    data: data
  })
}

// 修改督查记录
export function updateSuperviseRecord(data) {
  return request({
    url: '/patrol/superviseRecord/edit',
    method: 'put',
    data: data
  })
}

// 删除督查记录
export function delSuperviseRecord(id) {
  return request({
    url: '/patrol/superviseRecord/delete/' + id,
    method: 'delete'
  })
}
/*
import request from '@/utils/request'

// 查询督查记录列表
export function listSuperviseRecord(query) {
  return request({
    url: '/supervise/superviseRecord/list',
    method: 'get',
    params: query
  })
}

// 查询督查记录详细
export function getSuperviseRecord(id) {
  return request({
    url: '/supervise/superviseRecord/get/' + id,
    method: 'get'
  })
}

// 新增督查记录
export function addSuperviseRecord(data) {
  return request({
    url: '/supervise/superviseRecord/add',
    method: 'post',
    data: data
  })
}

// 修改督查记录
export function updateSuperviseRecord(data) {
  return request({
    url: '/supervise/superviseRecord/edit',
    method: 'put',
    data: data
  })
}

// 删除督查记录
export function delSuperviseRecord(id) {
  return request({
    url: '/supervise/superviseRecord/delete/' + id,
    method: 'delete'
  })
}
*/
