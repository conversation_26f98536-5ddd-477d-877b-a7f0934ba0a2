import request from '@/utils/request';

// 查询相关所有文件信息
export function findFiles(params) {
  return request({
    url: '/file/findFiles',
    method: 'get',
    params,
  });
}

// 查询单个文件信息
export function getFile(params) {
  if(!params.ownerId){
    return;
  }
  // if(isNaN(Number(params.ownerId))){
  //   return;
  // }
  return request({
    url: '/file/getFile',
    method: 'get',
    params,
  });
}

// 更新文件排序
export function updateFileSort(data) {
  return request({
    url: '/file/updateFileSort',
    method: 'post',
    data: data,
  });
}

// 保存备注
export function updateFileRemark(data) {
  return request({
    url: '/file/updateFileRemark',
    method: 'post',
    data: data,
  });
}

// 批量保存备注
export function updateFileRemarks(data) {
  return request({
    url: '/file/updateFileRemarks',
    method: 'post',
    data: data,
  });
}
