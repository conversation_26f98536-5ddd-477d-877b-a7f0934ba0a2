<svg 
 xmlns="http://www.w3.org/2000/svg"
 xmlns:xlink="http://www.w3.org/1999/xlink"
 width="281.5px" height="61.5px">
<defs>
<linearGradient id="PSgrad_0" x1="0%" x2="100%" y1="0%" y2="0%">
  <stop offset="0%" stop-color="rgb(255,255,0)" stop-opacity="1" />
  <stop offset="100%" stop-color="rgb(0,36,73)" stop-opacity="0" />
</linearGradient>
<linearGradient id="PSgrad_1" x1="0%" x2="100%" y1="0%" y2="0%">
  <stop offset="0%" stop-color="rgb(255,255,0)" stop-opacity="1" />
  <stop offset="100%" stop-color="rgb(0,71,143)" stop-opacity="0" />
</linearGradient>
</defs>
<path fill-rule="evenodd"  stroke="rgb(232, 225, 29)" stroke-width="1px" stroke-dasharray="0, 339" stroke-linecap="round" stroke-linejoin="miter" fill-opacity="0" opacity="0.012" fill="rgb(255, 255, 0)"
 d="M0.500,0.500 L280.500,0.500 L280.500,60.500 L0.500,60.500 L0.500,0.500 Z"/>
<path fill-rule="evenodd"  fill="url(#PSgrad_0)"
 d="M0.500,0.500 L280.500,0.500 L280.500,60.500 L0.500,60.500 L0.500,0.500 Z"/>
<path fill-rule="evenodd"  fill="url(#PSgrad_1)"
 d="M34.500,15.500 L280.500,15.500 L280.500,45.500 L0.500,45.500 L34.500,15.500 Z"/>
<path fill-rule="evenodd"  fill="rgb(255, 255, 0)"
 d="M18.500,16.500 L27.500,16.500 L9.500,31.500 L0.500,31.500 L18.500,16.500 Z"/>
</svg>