import request from '@/utils/request'

// 查询督办下发数据列表（分页）
export function findSupervisionIssuanceList(query) {
    return request({
        url: '/manager/noticeSupervision/list',
        method: 'post',
        data: query
    });
}

// 查询督办查看
export function findSupervisionList(query) {
    return request({
        url: '/manager/noticeSupervision/viewList',
        method: 'post',
        data: query
    });
}

// 下发
export function issuanceMethod(data) {
    return request({
        url: '/manager/noticeSupervision/review',
        method: 'put',
        data: data
    });
}

// 接收
export function acceptMethod(data) {
    return request({
        url: '/manager/noticeSupervision/accept',
        method: 'put',
        data: data
    });
}

// 根据id查询督办数据
export function getSupervisionById(id) {
    return request({
        url: `/manager/noticeSupervision/get/${id}`,
        method: 'get'
    });
}

// 新增督办数据
export function addSupervision(data) {
    return request({
        url: '/manager/noticeSupervision/add',
        method: 'post',
        data: data
    });
}

// 修改督办数据
export function editSupervision(data) {
    return request({
        url: '/manager/noticeSupervision/edit',
        method: 'put',
        data: data
    });
}

// 删除督办数据
export function removeSupervision(id) {
    return request({
        url: `/manager/noticeSupervision/delete/${id}`,
        method: 'delete'
    });
}



// 驳回督办数据
export function rejectSupervision(data) {
    return request({
        url: '/manager/noticeSupervision/reject',
        method: 'put',
        data: data
    });
}
