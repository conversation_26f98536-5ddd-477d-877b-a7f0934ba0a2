import request from '@/utils/request'

// 养护路段
export function maintenanceSectionList(query) {
  return request({
    url: '/system/maintenanceSection/listAll',
    method: 'get',
    params: query
  })
}
// 路线编号
export function routeListAll(query) {
  return request({
    url: `system/route/listAll`,
    method: 'get',
    params: query
  })
}
// 管理处、管养分处
export function deptTree(data) {
  return request({
    url: '/system/user/deptTree',
    method: 'get',
    params: data
  })
}
// 查询字典数据列表
export function listData(query) {
  return request({
    url: '/system/dict/data/list',
    method: 'get',
    params: query
  })
}
