import request from '@/utils/request'

// 查询考核配置类型树形结构
export function queryExamineTypeList() {
  return request({
    url: '/examine/examine-type/list',
    method: 'get'
  })
}

export function queryTypeTreeById(id) {
  return request({
    url: `/examine/examine-type/${id}`,
    method: 'get',
  })
}

export function queryExamineAssociationTree(query) {
  return request({
    url: '/examine/examine-type/association-tree',
    method: 'get',
    params: query
  })
}

// 新增考核配置类型
export function addExamineType(data) {
  return request({
    url: '/examine/examine-type/add',
    method: 'post',
    data: data
  })
}

// 编辑考核配置类型
export function editExamineType(data) {
  return request({
    url: '/examine/examine-type/edit',
    method: 'put',
    data: data
  })
}

// 删除考核配置类型
export function deleteType(id) {
  return request({
    url: `/examine/examine-type/${id}`,
    method: 'delete',
  })
}

// 查询考核配置明细
export function queryExamineDetailList(query) {
  return request({
    url: '/examine/examine-detail/list',
    method: 'get',
    params: query
  })
}

// 新增考核配置明细
export function addExamineDetail(data) {
  return request({
    url: '/examine/examine-detail/add',
    method: 'post',
    data: data
  })
}

// 编辑考核配置明细
export function editExamineDetail(data) {
  return request({
    url: '/examine/examine-detail/edit',
    method: 'put',
    data: data
  })
}

// 删除考核配置明细
export function deleteDetail(id) {
  return request({
    url: `/examine/examine-detail/${id}`,
    method: 'delete',
  })
}
