import request from '@/utils/request'

// 查询汛期巡查频率配置列表
export function listXqsetting(query) {
  return request({
    url: '/middleData/xqsetting/list',
    method: 'get',
    params: query
  })
}

// 查询汛期巡查频率配置详细
export function getXqsetting(id) {
  return request({
    url: '/middleData/xqsetting/get/' + id,
    method: 'get'
  })
}

// 新增汛期巡查频率配置
export function addXqsetting(data) {
  return request({
    url: '/middleData/xqsetting/add',
    method: 'post',
    data: data
  })
}

// 修改汛期巡查频率配置
export function updateXqsetting(data) {
  return request({
    url: '/middleData/xqsetting/edit',
    method: 'put',
    data: data
  })
}

// 删除汛期巡查频率配置
export function delXqsetting(id) {
  return request({
    url: '/middleData/xqsetting/delete/' + id,
    method: 'delete'
  })
}
