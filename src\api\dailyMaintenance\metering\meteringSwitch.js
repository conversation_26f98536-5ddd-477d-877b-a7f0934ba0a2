import request from '@/utils/request'

// 查询计量期数列表(分页)
export function queryCalcTCNumberList(data) {
    return request({
        url: '/manager/number/list',
        method: 'get',
        params: data
    });
}

// 查询计量期数列表(不分页)
export function queryCalcTCNumberListAll() {
    return request({
        url: '/manager/number/listAll',
        method: 'get'
    });
}

// 根据id查询计量期数数据
export function getCalcTCNumberById(id) {
    return request({
        url: `/manager/number/get/${id}`,
        method: 'get'
    });
}

// 新增计量期数
export function addCalcTCNumber(data) {
    return request({
        url: '/manager/number/add',
        method: 'post',
        data: data
    });
}

// 修改计量期数
export function updateCalcTCNumber(data) {
    return request({
        url: '/manager/number/edit',
        method: 'put',
        data: data
    });
}

// 开关接口
export function toggleCalcTCNumber(data) {
    return request({
        url: '/manager/number/edit/open',
        method: 'put',
        data: data
    });
}

// 删除计量期数
export function deleteCalcTCNumber(id) {
    return request({
        url: `/manager/number/delete/${id}`,
        method: 'delete'
    });
}
