import request from '@/utils/request'

export default {
  // 查询列表数据（分页）
  queryPageInspection(query) {
    return request({
      url: '/disaster/inspection/list',
      method: 'post',
      data: query,
      params: {
        pageNum: query.pageNum,
        pageSize: query.pageSize,
      },
    })
  },
  // 查询带有巡查人员的巡检记录列表
  queryListWithUsers(query) {
    return request({
      url: '/disaster/inspection/listWithUsers',
      method: 'post',
      data: query
    })
  },
  // 根据ID查询巡检记录
  inspectionQuery(id) {
    return request({
      url: `/disaster/inspection/getInfo/${id}`,
      method: 'get',
    })
  },
  // 新增巡检查记录
  inspectionAdd(query) {
    return request({
      url: '/disaster/inspection/add',
      method: 'post',
      data: query
    })
  },
  // 修改巡检查记录
  inspectionEdit(query) {
    return request({
      url: '/disaster/inspection/edit',
      method: 'post',
      data: query
    })
  },
  // 删除巡检查记录
  inspectionRemove(id) {
    return request({
      url: `/disaster/inspection/remove/${id}`,
      method: 'post',
    })
  },
  // 批量删除巡检查记录
  inspectionBatchRemove(query) {
    return request({
      url: '/disaster/inspection/batchRemove',
      method: 'post',
      data: query
    })
  },
  // 导出巡检查记录
  inspectionExport(query) {
    return request({
      url: '/disaster/inspection/export',
      method: 'post',
      params: query
    })
  },
  // 获取实体类
  inspectionGetToDoInspection(query) {
    return request({
      url: `/disaster/inspection/getToDoInspection`,
      method: 'post',
      data: query
    })
  },
  // 查询选取风险点列表
  listByDisasterRisk(query) {
    return request({
      url: '/disaster/inspection/listByDisasterRisk',
      method: 'post',
      data: query,
      params: {
        pageNum: query.pageNum,
        pageSize: query.pageSize,
      },
    })
  },
}











