import request from '@/utils/request'


// 获取结构树
export function getUserTreeStructure(data) {
  return request({
    url: '/manager/check/getDaliyTreeStructure',
    method: 'post',
    data: data
  });
}

// 查询验收登记列表(分页)
export function listConstructionChecksPaged(data) {
  return request({
    url: '/manager/check/list',
    method: 'post',
    data: data
  });
}

// 查询验收登记列表(不分页)
export function listAllConstructionChecks() {
  return request({
    url: '/manager/check/listAll',
    method: 'get'
  });
}

// 根据 ID 查询验收登记数据
export function getConstructionCheckById(id) {
  return request({
    url: `/manager/check/get/${id}`,
    method: 'get'
  });
}

// 修改验收登记
export function updateConstructionCheck(data) {
  return request({
    url: '/manager/check/edit',
    method: 'put',
    data: data
  });
}

// 提交验收
export function submitReview(data) {
  return request({
    url: '/manager/check/review',
    method: 'put',
    data: data
  });
}

// 撤回到待施工
export function rejectTocheck(data) {
  return request({
    url: '/manager/check/rejectTocheck',
    method: 'put',
    data: data
  });
}

// 驳回
export function reject(data) {
  return request({
    url: '/manager/check/reject',
    method: 'put',
    data,
  });
}


// 查询施工单列表
export function pageList(data) {
  return request({
    url: '/manager/check/pageList',
    method: 'post',
    data
  });
}

export function getDetil(id) {
  return request({
    url: `/manager/check/get/${id}`,
    method: 'get'
  });
}

// 保存验收
export function save(data) {
  return request({
    url: '/manager/check/save',
    method: 'put',
    data,
  });
}


