import request from '@/utils/request'

// let codePath = 'codeGen';//公共
let codePath = 'zhuoyueCodeGen';//武大卓越

// 查询测试数据列表
export function listTest2(query) {
  return request({
    url: `/${codePath}/test2/list`,
    method: 'get',
    params: query
  })
}

// 查询测试数据详细
export function getTest2(id) {
  return request({
    url: `/${codePath}/test2/get/` + id,
    method: 'get'
  })
}

// 新增测试数据
export function addTest2(data) {
  return request({
    url: `/${codePath}/test2/add`,
    method: 'post',
    data: data
  })
}

// 修改测试数据
export function updateTest2(data) {
  return request({
    url: `/${codePath}/test2/edit`,
    method: 'put',
    data: data
  })
}

// 删除测试数据
export function delTest2(id) {
  return request({
    url: `/${codePath}/test2/delete/` + id,
    method: 'delete'
  })
}
