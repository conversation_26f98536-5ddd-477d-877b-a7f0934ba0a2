import request from '@/utils/request'

// 查询汛期巡查详情列表
export function listXqitem(query) {
  return request({
    url: '/middleData/xqitem/list',
    method: 'get',
    params: query
  })
}

// 查询汛期巡查详情详细
export function getXqitem(id) {
  return request({
    url: '/middleData/xqitem/get/' + id,
    method: 'get'
  })
}

// 新增汛期巡查详情
export function addXqitem(data) {
  return request({
    url: '/middleData/xqitem/add',
    method: 'post',
    data: data
  })
}

// 修改汛期巡查详情
export function updateXqitem(data) {
  return request({
    url: '/middleData/xqitem/edit',
    method: 'put',
    data: data
  })
}

// 删除汛期巡查详情
export function delXqitem(id) {
  return request({
    url: '/middleData/xqitem/delete/' + id,
    method: 'delete'
  })
}
