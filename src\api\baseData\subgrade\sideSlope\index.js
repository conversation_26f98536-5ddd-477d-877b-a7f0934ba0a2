import request from "@/utils/request";

// 条件查询边坡基础数据列表(分页)
export function getListPage(data) {
  return request({
    url: "/baseData/sideslope/basic/getListPage",
    method: "post",
    data,
  });
}

// 新增边坡基础数据
export function addSideSlope(data) {
  return request({
    url: "/baseData/sideslope/basic/add",
    method: "post",
    data,
  });
}

// 查询边坡基础数据
export function getSideSlope(id) {
  return request({
    url: `/baseData/sideslope/basic/get/${id}`,
    method: "get",
  });
}

// 暂存边坡数据
export function tempSideSlope(data) {
  return request({
    url: "/baseData/sideslope/basic/temp/add",
    method: "post",
    data,
  });
}

// 修改边坡基础数据
export function updateSideSlope(data) {
  return request({
    url: '/baseData/sideslope/basic/edit',
    method: 'put',
    data: data
  })
}

// 删除边坡基础数据
export function delSideSlope(data) {
  return request({
    url: '/baseData/sideslope/basic/delete',
    method: 'delete',
    data
  })
}

// 根据id查询边坡防护形式数据明细列表
export function getProtection(data) {
  return request({
    url: '/baseData/sideslope/protection/getListPage',
    method: 'post',
    data
  })
}


// 一张图根据asseId获取信息
export function assetInfo(assetId) {
  return request({
    url: `/baseData/assetInfo/get/${assetId}`,
    method: 'get',
  })
}


