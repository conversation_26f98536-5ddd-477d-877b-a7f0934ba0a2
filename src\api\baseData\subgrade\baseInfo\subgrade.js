import request from '@/utils/request'

// 查询隧道静态数据列表
export function listStatic(query) {
  return request({
    url: '/baseData/tunnel/getListPage',
    method: 'get',
    params: query
  })
}

// 查询隧道静态数据详细
export function getStatic(id) {
  return request({
    url: '/baseData/tunnel/get/' + id,
    method: 'get'
  })
}

// 查询隧道管理数据详细
export function getStaticMgr(id) {
  return request({
    url: '/baseData/tunnel/mgr/get/' + id,
    method: 'get'
  })
}

// 查询隧道结构数据详细
export function getStaticStructure(id) {
  return request({
    url: '/baseData/tunnel/structure/get/' + id,
    method: 'get'
  })
}

// 新增隧道静态数据
export function addStatic(data) {
  return request({
    url: '/baseData/tunnel/add',
    method: 'post',
    data: data
  })
}

// 新增隧道管理数据
export function addMgrStatic(data) {
  return request({
    url: '/baseData/tunnel/mgr/add',
    method: 'post',
    data: data
  })
}

// 新增隧道结构数据
export function addStructureStatic(data) {
  return request({
    url: '/baseData/tunnel/structure/add',
    method: 'post',
    data: data
  })
}

// 暂存隧道静态数据
export function tempAddStatic(data) {
  return request({
    url: '/baseData/tunnel/temp/add',
    method: 'post',
    data: data
  })
}
// 暂存隧道管理数据
export function tempMgrAddStatic(data) {
  return request({
    url: '/baseData/tunnel/mgr/temp/add',
    method: 'post',
    data: data
  })
}

// 暂存隧道结构数据
export function tempStructureAddStatic(data) {
  return request({
    url: '/baseData/tunnel/structure/temp/add',
    method: 'post',
    data: data
  })
}


// 修改隧道静态数据
export function updateStatic(data) {
  return request({
    url: '/baseData/tunnel/edit',
    method: 'put',
    data: data
  })
}

// 修改隧道管理数据
export function updateMgrStatic(data) {
  return request({
    url: '/baseData/tunnel/mgr/edit',
    method: 'put',
    data: data
  })
}

// 修改隧道结构数据
export function updateStructureStatic(data) {
  return request({
    url: '/baseData/tunnel/structure/edit',
    method: 'put',
    data: data
  })
}

// 删除隧道静态数据
export function delStatic(data) {
  return request({
    url: '/baseData/tunnel/delete/'+data,
    method: 'delete',
  })
}

// 暂存隧道数据
export function tempAdd(data) {
  return request({
    url: '/baseData/tunnel/temp/add',
    method: 'post',
    data
  })
}


// 导出隧道数据
export function exportStatic(query) {
  return request({
    url: '/baseData/tunnel/export',
    method: 'get',
    params: query
  })
}


// 导入隧道数据
export function importStatic(data) {
  return request({
    url: '/baseData/tunnel/import',
    method: 'post',
    data: data
  })
}

// 锁定隧道数据
export function changeLockedStatus(params) {
  return request({
    url: '/baseData/tunnel/updateLocked',
    method: 'put',
    params
  })
}