import request from '@/utils/request'

// 查询应用更新信息列表
export function listUpdates(query) {
  return request({
    url: '/system/updates/list',
    method: 'get',
    params: query
  })
}

// 查询应用更新信息详细
export function getUpdates(id) {
  return request({
    url: '/system/updates/get/' + id,
    method: 'get'
  })
}

// 新增应用更新信息
export function addUpdates(data) {
  return request({
    url: '/system/updates/add',
    method: 'post',
    data: data
  })
}

// 修改应用更新信息
export function updateUpdates(data) {
  return request({
    url: '/system/updates/edit',
    method: 'put',
    data: data
  })
}

// 删除应用更新信息
export function delUpdates(id) {
  return request({
    url: '/system/updates/delete/' + id,
    method: 'delete'
  })
}
