import request from '@/utils/request'

// 查询手机验证码登录列表
export function listVerificationCode(query) {
  return request({
    url: '/system/verificationCode/list',
    method: 'get',
    params: query
  })
}

// 查询手机验证码登录详细
export function getVerificationCode(id) {
  return request({
    url: '/system/verificationCode/get/' + id,
    method: 'get'
  })
}

// 新增手机验证码登录
export function addVerificationCode(data) {
  return request({
    url: '/system/verificationCode/add',
    method: 'post',
    data: data
  })
}

// 修改手机验证码登录
export function updateVerificationCode(data) {
  return request({
    url: '/system/verificationCode/edit',
    method: 'put',
    data: data
  })
}

// 删除手机验证码登录
export function delVerificationCode(id) {
  return request({
    url: '/system/verificationCode/delete/' + id,
    method: 'delete'
  })
}
