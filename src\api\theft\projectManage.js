import request from '@/utils/request'

// 查询被损被盗工程列表(分页)
export function queryList(data) {
  return request({
    url: '/manager/theft/project/list',
    method: 'post',
    data,
  });
}

// 根据id查询被损被盗工程数据
export function getProjectById(id) {
  return request({
    url: `/manager/theft/project/get/${id}`,
    method: 'get',
  });
}

// 新增被损被盗工程
export function addProject(data) {
  return request({
    url: '/manager/theft/project/add',
    method: 'post',
    data,
  });
}

// 修改被损被盗工程
export function editProject(data) {
  return request({
    url: '/manager/theft/project/edit',
    method: 'put',
    data,
  });
}

// 更新项目状态
export function updateProjectStatus(data) {
  return request({
    url: '/manager/theft/project/update/status',
    method: 'put',
    data,
  });
}

// 删除被损被盗工程
export function deleteProject(id) {
  return request({
    url: `/manager/theft/project/delete/${id}`,
    method: 'delete',
  });
}


// 删除被损被盗工程
export function isAdd(id) {
  return request({
    url: `/manager/theft/project/is/add/${id}`,
    method: 'get',
  });
}
