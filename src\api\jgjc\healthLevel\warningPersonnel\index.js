import request from '@/utils/request'

export function saveWarningUser(data) {
  return request({
    url: '/jgjcadmin/api/healthassessment/warningusermanage/save',
    method: 'post',
    data
  });
}

export function updateWarningUser(data) {
  return request({
    url: '/jgjcadmin/api/healthassessment/warningusermanage/update',
    method: 'post',
    data
  });
}

export function deleteWarningUser(data) {
  return request({
    url: '/jgjcadmin/api/healthassessment/warningusermanage/delete',
    method: 'post',
    data
  });
}

export function getWarningUserPage(data) {
  return request({
    url: '/jgjcadmin/api/healthassessment/warningusermanage/page',
    method: 'get',
    params: data
  });
}
