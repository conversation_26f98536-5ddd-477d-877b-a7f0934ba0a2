import request from '@/utils/request'

// 查询机电故障上报列表
export function listDeviceFaultRecord(query) {
  return request({
    url: '/patrol/deviceFaultRecord/list',
    method: 'get',
    params: query
  })
}

// 查询机电故障上报详细
export function getDeviceFaultRecord(id) {
  return request({
    url: '/patrol/deviceFaultRecord/get/' + id,
    method: 'get'
  })
}

// 新增机电故障上报
export function addDeviceFaultRecord(data) {
  return request({
    url: '/patrol/deviceFaultRecord/add',
    method: 'post',
    data: data
  })
}

// 修改机电故障上报
export function updateDeviceFaultRecord(data) {
  return request({
    url: '/patrol/deviceFaultRecord/edit',
    method: 'put',
    data: data
  })
}

// 删除机电故障上报
export function delDeviceFaultRecord(data) {
  return request({
    url: `/patrol/deviceFaultRecord/delete`,
    method: 'delete',
    data: data.idList,
  });
}
