import request from '@/utils/request'
import axios from "axios";

// 查询审核数据列表（分页）
export function findAuditDataList(query) {
  return request({
    url: '/manager/roaddisease/list',
    method: 'post',
    data: query,
    notVerifyDuplicates: true
  })
}

// 查询审核数据列表（不分页）
export function listAllAuditData() {
  return request({
    url: '/manager/roaddisease/listAll',
    method: 'get'
  })
}

// 根据ID查询审核数据
export function getAuditDataById(id) {
  return request({
    url: `/manager/roaddisease/get/${id}`,
    method: 'get'
  })
}

// 新增审核数据
export function addAuditData(data) {
  return request({
    url: '/manager/roaddisease/add',
    method: 'post',
    data: data
  })
}

// 修改审核数据
export function editAuditData(data) {
  return request({
    url: '/manager/roaddisease/edit',
    method: 'put',
    data: data
  })
}

// 删除审核数据
export function deleteAuditData(id) {
  return request({
    url: `/manager/roaddisease/delete/${id}`,
    method: 'delete'
  })
}

// 批量删除事件审核数据
export function batchDeleteAuditData(ids) {
  return request({
    url: '/manager/roaddisease/removeBatchByIds',
    method: 'delete',
    data: ids // 这里假设data参数用于传递ids数组
  });
}
// 审核操作
export function reviewAuditData(data) {
  return request({
    url: '/manager/roaddisease/review',
    method: 'post',
    data: data
  });
}

// 批量审核操作
export function batchReviewAuditData(data) {
  return request({
    url: '/manager/roaddisease/reviewBatch',
    method: 'post',
    data: data
  });
}

// 审核无效操作
export function reviewInvalidAuditData(data) {
  return request({
    url: '/manager/roaddisease/reviewInvalid',
    method: 'post',
    data: data
  });
}

// 批量审核无效操作
export function batchReviewInvalidAuditData(data) {
  return request({
    url: '/manager/roaddisease/reviewInvalidBatch',
    method: 'post',
    data: data
  });
}

// 批量恢复
export function editBatch(data) {
  return request({
    url: '/manager/roaddisease/editBatch',
    method: 'post',
    data: data
  });
}

// 根据巡查记录ID查询事件列表
export function getEventListByRecordId(recordId) {
  return request({
    url: `/manager/roaddisease/getRoadDiseaseList/${recordId}`,
    method: 'get'
  })
}

// 事件解绑
export function unbindRecord(data) {
  return request({
    url: '/manager/roaddisease/updateRecordId',
    method: 'post',
    data: data
  });
}
// 事件关联
export function bindRecord(data) {
  return request({
    url: '/manager/roaddisease/BindRecordId',
    method: 'post',
    data: data
  });
}


export function findAuditDataListNoLoading(query) {
  return request({
    url: '/manager/roaddisease/list',
    method: 'post',
    data: query,
    notVerifyDuplicates: true,
    headers: {
      noLoading: true,
      repeatSubmit: false
    }
  })
}

export function findAuditDataList1(query) {
  return request({
    url: '/manager/roaddisease/getPatrolDiseaseList',
    method: 'post',
    data: query,
    notVerifyDuplicates: true
  })
}
