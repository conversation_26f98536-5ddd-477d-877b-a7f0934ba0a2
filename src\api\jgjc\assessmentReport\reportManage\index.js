import request from '@/utils/request'

// 报告管理查询
export function getReportManageForCustomerPage(query) {
  return request({
    url: '/jgjcadmin/api/evaluationReport/reportManageForCustomer/page',
    method: 'get',
    params: query
  })
}

// 报告管理新增
export function saveReportManageForCustomer(query) {
  return request({
    url: '/jgjcadmin/api/evaluationReport/reportManageForCustomer/save',
    method: 'post',
    data: query
  })
}

// 报告管理编辑
export function updateReportManageForCustomer(query) {
  return request({
    url: '/jgjcadmin/api/evaluationReport/reportManageForCustomer/update',
    method: 'post',
    data: query
  })
}

// 报告管理删除
export function deleteReportManageForCustomer(query) {
  return request({
    url: '/jgjcadmin/api/evaluationReport/reportManageForCustomer/delete',
    method: 'post',
    data: query
  })
}
