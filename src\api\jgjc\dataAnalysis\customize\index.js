import request from '@/utils/request'

// 查询传感器下拉框
export function getSensorTreeNodeByCode(data) {
  return request({
    url: '/jgjcadmin/api/dataanalysis/custom-analysis/getSensorTreeNodeByCode',
    method: 'post',
    data: data
  })
}

// 查询具体类型下拉框
export function getSensorByDataCode(data) {
  return request({
    url: '/jgjcadmin/api/dataanalysis/custom-analysis/getSensorByDataCode',
    method: 'post',
    data: data
  })
}

// 查询特征值分析
export function getDataForSpecificTypeAndAnalysisType(data) {
  return request({
    url: '/jgjcadmin/api/dataanalysis/custom-analysis/getDataForSpecificTypeAndAnalysisType',
    method: 'post',
    data: data
  })
}

// 数据序列相关性接口
export function getCorrelationAnalysis(data) {
  return request({
    url: '/jgjcadmin/api/dataanalysis/custom-analysis/getCorrelationAnalysis',
    method: 'post',
    data: data
  })
}

// 振动信号频谱分析接口
export function getDataProcess(data) {
  return request({
    url: '/jgjcadmin/api/dataanalysis/custom-analysis/getDataProcess',
    method: 'post',
    data: data
  })
}

// 风速风向玫瑰图接口
export function roseAnalysis(data) {
  return request({
    url: '/jgjcadmin/api/dataanalysis/custom-analysis/roseAnalysis',
    method: 'post',
    data: data
  })
}

// 数据趋势性分析
export function trendAnalysis(data) {
  return request({
    url: '/jgjcadmin/api/dataanalysis/custom-analysis/trendAnalysis',
    method: 'post',
    data: data
  })
}

// 车辆荷载分析
export function getVelData(data) {
  return request({
    url: '/jgjcadmin/api/dataanalysis/custom-analysis/getVelData',
    method: 'post',
    data: data
  })
}

// 裂缝日变化
export function dailyVariation(data) {
  return request({
    url: '/jgjcadmin/api/dataanalysis/custom-analysis/dailyVariation',
    method: 'post',
    data: data
  })
}

// 裂缝日变化
export function limitStatistic(data) {
  return request({
    url: '/jgjcadmin/api/dataanalysis/custom-analysis/dailyVariation',
    method: 'post',
    data: data
  })
}


export const getMin = (arr) => {
  if(arr.length === 0){
    return 0;
  }
  let tmpValue = arr[0];
  arr.forEach((value, index) => {
    if(tmpValue > value){
      tmpValue = value
    }
  })
  return tmpValue
}

export const getMax = (arr) => {
  if(arr.length === 0){
    return 0;
  }
  let tmpValue = arr[0];
  arr.forEach((value, index) => {
    if(tmpValue < value){
      tmpValue = value
    }
  })
  return tmpValue
}
