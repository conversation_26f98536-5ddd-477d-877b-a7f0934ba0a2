<!DOCTYPE html>
<!-- saved from url=(0090)chrome-extension://ekhagklcjbdpajgpjgmbionohlpdbjgc/translateSandbox/translateSandbox.html -->
<html lang="en"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	
	<title>Zotero - Translate Sandbox</title>
</head>
<body>

<script src="chrome-extension://ekhagklcjbdpajgpjgmbionohlpdbjgc/zotero_config.js"></script>
<script src="chrome-extension://ekhagklcjbdpajgpjgmbionohlpdbjgc/zotero.js"></script>
<script src="chrome-extension://ekhagklcjbdpajgpjgmbionohlpdbjgc/api.js"></script>
<script src="chrome-extension://ekhagklcjbdpajgpjgmbionohlpdbjgc/utilities/openurl.js"></script>
<script src="chrome-extension://ekhagklcjbdpajgpjgmbionohlpdbjgc/utilities/date.js"></script>
<script src="chrome-extension://ekhagklcjbdpajgpjgmbionohlpdbjgc/utilities/xregexp-all.js"></script>
<script src="chrome-extension://ekhagklcjbdpajgpjgmbionohlpdbjgc/utilities/xregexp-unicode-zotero.js"></script>
<script src="chrome-extension://ekhagklcjbdpajgpjgmbionohlpdbjgc/utilities/utilities.js"></script>
<script src="chrome-extension://ekhagklcjbdpajgpjgmbionohlpdbjgc/utilities/utilities_item.js"></script>
<script src="chrome-extension://ekhagklcjbdpajgpjgmbionohlpdbjgc/utilities/schema.js"></script>
<script src="chrome-extension://ekhagklcjbdpajgpjgmbionohlpdbjgc/utilities/resource/zoteroTypeSchemaData.js"></script>
<script src="chrome-extension://ekhagklcjbdpajgpjgmbionohlpdbjgc/utilities/cachedTypes.js"></script>
<script src="chrome-extension://ekhagklcjbdpajgpjgmbionohlpdbjgc/translate/promise.js"></script>
<script src="chrome-extension://ekhagklcjbdpajgpjgmbionohlpdbjgc/translate/utilities_translate.js"></script>
<script src="chrome-extension://ekhagklcjbdpajgpjgmbionohlpdbjgc/translate/debug.js"></script>
<script src="chrome-extension://ekhagklcjbdpajgpjgmbionohlpdbjgc/translate/http.js"></script>
<script src="chrome-extension://ekhagklcjbdpajgpjgmbionohlpdbjgc/translate/translator.js"></script>
<script src="chrome-extension://ekhagklcjbdpajgpjgmbionohlpdbjgc/translate/translators.js"></script>
<script src="chrome-extension://ekhagklcjbdpajgpjgmbionohlpdbjgc/translate/repo.js"></script>
<script src="chrome-extension://ekhagklcjbdpajgpjgmbionohlpdbjgc/translate/translation/translate.js"></script>
<script src="chrome-extension://ekhagklcjbdpajgpjgmbionohlpdbjgc/translate/translation/sandboxManager.js"></script>
<script src="chrome-extension://ekhagklcjbdpajgpjgmbionohlpdbjgc/translate_item.js"></script>
<script src="chrome-extension://ekhagklcjbdpajgpjgmbionohlpdbjgc/translate/tlds.js"></script>
<script src="chrome-extension://ekhagklcjbdpajgpjgmbionohlpdbjgc/translate/proxy.js"></script>
<script src="chrome-extension://ekhagklcjbdpajgpjgmbionohlpdbjgc/translate/rdf/init.js"></script>
<script src="chrome-extension://ekhagklcjbdpajgpjgmbionohlpdbjgc/translate/rdf/uri.js"></script>
<script src="chrome-extension://ekhagklcjbdpajgpjgmbionohlpdbjgc/translate/rdf/term.js"></script>
<script src="chrome-extension://ekhagklcjbdpajgpjgmbionohlpdbjgc/translate/rdf/identity.js"></script>
<script src="chrome-extension://ekhagklcjbdpajgpjgmbionohlpdbjgc/translate/rdf/n3parser.js"></script>
<script src="chrome-extension://ekhagklcjbdpajgpjgmbionohlpdbjgc/translate/rdf/rdfparser.js"></script>
<script src="chrome-extension://ekhagklcjbdpajgpjgmbionohlpdbjgc/translate/rdf/serialize.js"></script>
<script src="chrome-extension://ekhagklcjbdpajgpjgmbionohlpdbjgc/schema.js"></script>
<script src="chrome-extension://ekhagklcjbdpajgpjgmbionohlpdbjgc/utilities.js"></script>
<script src="chrome-extension://ekhagklcjbdpajgpjgmbionohlpdbjgc/http.js"></script>
<script src="chrome-extension://ekhagklcjbdpajgpjgmbionohlpdbjgc/inject/http.js"></script>
<script src="chrome-extension://ekhagklcjbdpajgpjgmbionohlpdbjgc/messagingGeneric.js"></script>
<script src="chrome-extension://ekhagklcjbdpajgpjgmbionohlpdbjgc/translateSandbox/translateSandboxFunctionOverrides.js"></script>
<script src="chrome-extension://ekhagklcjbdpajgpjgmbionohlpdbjgc/translateSandbox/translateSandbox.js"></script>

</body></html>