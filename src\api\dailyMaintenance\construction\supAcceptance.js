import request from '@/utils/request'

/**
 * 查询监理单位审核列表（分页）
 * @param {Object} data - 请求参数
 * @returns {Promise} - 返回 Promise 对象
 */
export function querySupervisionAuditList(data) {
  return request({
    url: '/manager/checkSupDomainAccept/pageList',
    method: 'post',
    data: data
  });
}

/**
 * 根据id查询监理单位审核数据
 * @param {string} id - 监理单位审核记录ID
 * @returns {Promise} - 返回 Promise 对象
 */
export function getSupervisionAuditById(id) {
  return request({
    url: `/manager/checkSupDomainAccept/get/${id}`,
    method: 'get'
  });
}

/**
 * 根据id查询监理单位审核事件明细
 * @param {string} id - 监理单位审核记录ID
 * @returns {Promise} - 返回 Promise 对象
 */
export function getSupervisionAuditDetailById(id) {
  return request({
    url: `/manager/checkSupDomainAccept/getDisDetail/${id}`,
    method: 'get'
  });
}

/**
 * 提交监理单位审核
 * @param {Object} data - 审核数据
 * @returns {Promise} - 返回 Promise 对象
 */
export function submitSupervisionAudit(data) {
  return request({
    url: '/manager/checkSupDomainAccept/review',
    method: 'put',
    data: data
  });
}

/**
 * 驳回监理单位审核
 * @param {Object} data - 审核数据
 * @returns {Promise} - 返回 Promise 对象
 */
export function rejectSupervisionAudit(data) {
  return request({
    url: '/manager/checkSupDomainAccept/reject',
    method: 'put',
    data: data
  });
}
