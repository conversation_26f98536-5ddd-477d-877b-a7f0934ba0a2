import request from '@/utils/request'

// 查询部门下拉树结构..
// export function deptTreeSelect() {
//   return request({
//     url: '/system/user/deptTree',
//     method: 'get'
//   })
// }


// 查询部门下拉树结构
export function deptTreeSelect(data) {
  return request({
    url: '/system/treeStruct/getTreeStruct',
    method: 'post',
    data: data,
    notVerifyDuplicates: true
  })
}

// 树形公共接口，包括下拉树
export function getTreeStruct(data) {
  return request({
    url: '/system/treeStruct/getTreeStruct',
    method: 'post',
    data: data,
    notVerifyDuplicates: true
  })
}

// 编辑节点信息
export function editNodeInfo(data) {
  return request({
    url: '/workFlow/history/update-activity-instance',
    method: 'put',
    data
  });
}
