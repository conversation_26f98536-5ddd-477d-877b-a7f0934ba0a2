import request from '@/utils/request'
// 修改
export function editConfig(data) {
  return request({
    url: '/jgjcadmin/api/config/update',
    method: 'post',
    data
  })
}

// 查询
export function listConfig(data) {
  return request({
    url: '/jgjcadmin/api/config/page',
    method: 'get',
    params: data
  })
}

// 新增
export function addConfig(data) {
  return request({
    url: '/jgjcadmin/api/config/save',
    method: 'post',
    data
  })
}

// 删除
export function delConfig(data) {
  return request({
    url: '/jgjcadmin/api/config/delete',
    method: 'post',
    data
  })
}
