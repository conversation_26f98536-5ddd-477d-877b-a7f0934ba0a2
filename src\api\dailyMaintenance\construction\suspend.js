import request from '@/utils/request'

// 结构树
export function getUserTreeStructure(data) {
    return request({
        url: '/manager/dispose/getUserTreeStructure',
        method: 'post',
        data: data
    });
}

// 查询暂不处理列表(分页)
export function listNotYetDisposePaged(data) {
    return request({
        url: '/manager/dispose/list',
        method: 'post',
        data: data
    });
}

// 查询暂不处理列表(不分页)
export function listNotYetDisposeAll() {
    return request({
        url: '/manager/dispose/listAll',
        method: 'get'
    });
}

// 根据 ID 查询暂不处理数据
export function getNotYetDisposeById(id) {
    return request({
        url: `/manager/dispose/getdispose/${id}`,
        method: 'get'
    });
}

// 根据 ID 查询暂不处理事件明细
export function getNotYetDisposeDetailById(id) {
    return request({
        url: `/manager/dispose/getDisDetail/${id}`,
        method: 'get'
    });
}

// 保存
export function saveNotYetDispose(data) {
    return request({
        url: '/manager/dispose/save',
        method: 'put',
        data: data
    });
}

// 通过
export function reviewNotYetDispose(data) {
    return request({
        url: '/manager/dispose/review',
        method: 'put',
        data: data
    });
}

// 暂不处理
export function notYetDispose(data) {
    return request({
        url: '/manager/dispose/reject',
        method: 'put',
        data: data
    });
}

// 修改施工登记
export function editConstructiondispose(data) {
    return request({
        url: '/manager/dispose/edit',
        method: 'put',
        data: data
    });
}


// 查询施工单列表
export function pageList(data) {
  return request({
    url: '/manager/dispose/pageList',
    method: 'post',
    data
  });
}


// 重新施工
export function againReview(data) {
    return request({
        url: '/manager/dispose/againReview',
        method: 'put',
        data: data
    });
}
