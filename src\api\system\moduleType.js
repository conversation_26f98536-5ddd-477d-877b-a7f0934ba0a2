import request from '@/utils/request'

export function getList() {
    return request({
        url: '/system/sysModuleType/getList',
        method: 'get',
    })
}

export function getListByEntity(entity) {
    return request({
        url: '/system/sysModuleType/getListByEntity',
        method: 'post',
        data: entity
    })
}

export function saveModule(entity) {
    return request({
        url: '/system/sysModuleType/saveModule',
        method: 'post',
        data: entity
    })
}

export function deleteModule(id) {
    return request({
        url: '/system/sysModuleType/deleteModule/' + id,
        method: 'post',
    })
}

// 
export function checkCodeUnique(code) {
    return request({
        url: '/system/SysModuleType/checkCodeUnique/' + code,
        method: 'get',
    })

}

// 查询文件类型列表
export function listSysModuleType(query) {
    return request({
        url: '/system/SysModuleType/list',
        method: 'get',
        params: query
    })
}

// 查询文件类型详细
export function getSysModuleType(id) {
    return request({
        url: '/system/SysModuleType/get/' + id,
        method: 'get'
    })
}

// 新增文件类型
export function addSysModuleType(data) {
    return request({
        url: '/system/SysModuleType/add',
        method: 'post',
        data: data
    })
}

// 修改文件类型
export function updateSysModuleType(data) {
    return request({
        url: '/system/SysModuleType/edit',
        method: 'put',
        data: data
    })
}

// 删除文件类型
export function delSysModuleType(id) {
    return request({
        url: '/system/SysModuleType/delete/' + id,
        method: 'delete'
    })
}

export function moduleFiles(code) {
    return request({
        url: '/system/sysModuleType/moduleFiles/' + code,
        method: 'get'
    })
}

export function getOneByCode(code) {
    return request({
        url: '/system/SysModuleType/getOneByCode/' + code,
        method: 'get'
    })
}
