import request from '@/utils/request'

// 查询日养中间计量单列表(分页)
export function listDaliyIntermediate(data) {
  return request({
      url: '/manager/calc/theft/intermediate/list',
      method: 'post',
      data
  });
}

// 新增定检中间计量单
export function addDaliyIntermediate(data) {
  return request({
      url: '/manager/calc/theft/intermediate/add',
      method: 'post',
      data
  });
}

// 修改定检中间计量单
export function editDaliyIntermediate(data) {
  return request({
      url: '/manager/calc/theft/intermediate/edit',
      method: 'put',
      data
  });
}

// 删除定检中间计量单
export function deleteDaliyIntermediate(id) {
  return request({
      url: `/manager/calc/theft/intermediate/delete/${id}`,
      method: 'delete',
  });
}

// 根据中间计量单id查询待计量明细列表(分页)
export function listSettle(data) {
  return request({
      url: '/manager/calc/theft/settle/select/unMeasured',
      method: 'post',
      data
  });
}

// 新增中间计量单明细
export function addDetail(data) {
  return request({
    url: '/manager/calc/theft/intermediate/detail/add',
    method: 'post',
    data
  });
}

// 根据事件settleId查询待计量的子目列表(分页)
export function listMethodBySettleId(data) {
  return request({
    url: `/manager/calc/theft/settle/method/list/uncalc`,
    method: 'post',
    data
  })
}

// 根据id查询中间计量单任务清单
export function listEvent(data) {
  return request({
      url: `/manager/calc/theft/intermediate/detail/list/event`,
      method: 'post',
      data
  });
}

// 删除中间计量单明细
export function deleteEvent(id) {
  return request({
    url: `/manager/calc/theft/intermediate/detail/delete/${id}`,
    method: 'delete',
  });
}

// 根据中间计量单id查询所有报价清单-子目(分页))
export function listMethod(data) {
  return request({
    url: `/manager/calc/theft/settle/method/list/data`,
    method: 'post',
    data
  });
}

// 查询任务单下报价清单列表(分页)
export function listEventBySettleId(data) {
  return request({
    url: `/manager/calc/theft/intermediate/detail/list/method`,
    method: 'post',
    data
  });
}

// 中间计量单提交与审核
export function middleProcess(data) {
  return request({
    url: `/manager/calc/theft/intermediate/process`,
    method: 'post',
    data
  });
}

// 获取中间计量审核列表
export function listPendingDaliyIntermediate(data) {
  return request({
    url: `/manager/calc/theft/intermediate/pending/list`,
    method: 'post',
    data
  });
}
// 获取中间计量审核列表
export function listViewDaliyIntermediate(data) {
    return request({
        url: `/manager/calc/theft/intermediate/view`,
        method: 'post',
        data
    });
}

// 中间计量单审核节点信息
export function getNodeInfo(data) {
  return request({
    url: `/manager/calc/theft/intermediate/node/info`,
    method: 'post',
    data
  });
}


// 中间计量单预览
export function previewIntermediateReport(id) {
    return request({
        url: `/manager/calc/theft/intermediate/report/preview?id=${id}`,
        method: 'get'
    });
}



// 中间计量单预览
export function downloadIntermediateReport(id) {
    return request({
        url: `/manager/calc/theft/intermediate/report/download?id=${id}`,
        method: 'get'
    });
}

export function updateFund(id) {
  return request({
    url: `/manager/calc/theft/intermediate/update/fund?id=${id}`,
    method: 'post'
  });
}


export function withdraw(data) {
  return request({
    url: '/manager/calc/theft/intermediate/withdraw',
    method: 'post',
    data
  });
}

export function packDownload(id) {
  return request({
    url: `/manager/calc/theft/intermediate/pack/download?id=${id}`,
    method: 'get'
  })
}
