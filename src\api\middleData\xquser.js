import request from '@/utils/request'

// 查询汛期巡查人员列表
export function listXquser(query) {
  return request({
    url: '/middleData/xquser/list',
    method: 'get',
    params: query
  })
}

// 查询汛期巡查人员详细
export function getXquser(id) {
  return request({
    url: '/middleData/xquser/get/' + id,
    method: 'get'
  })
}

// 新增汛期巡查人员
export function addXquser(data) {
  return request({
    url: '/middleData/xquser/add',
    method: 'post',
    data: data
  })
}

// 修改汛期巡查人员
export function updateXquser(data) {
  return request({
    url: '/middleData/xquser/edit',
    method: 'put',
    data: data
  })
}

// 删除汛期巡查人员
export function delXquser(id) {
  return request({
    url: '/middleData/xquser/delete/' + id,
    method: 'delete'
  })
}
