import request from '@/utils/request'
// 查询材料调查列表(分页)
export function queryMaterialAdjustList(params) {
    return request({
        url: '/manager/adjust/list',
        method: 'get',
        params: params
    });
}

// 查询材料调查列表(不分页)
export function queryMaterialAdjustListAll(params) {
    return request({
        url: '/manager/adjust/listAll',
        method: 'get',
        params
    });
}

// 根据ID查询材料调查数据
export function getMaterialAdjustById(id) {
    return request({
        url: `/manager/adjust/get/${id}`,
        method: 'get'
    });
}

// 新增材料调查
export function addMaterialAdjust(data) {
    return request({
        url: '/manager/adjust/add',
        method: 'post',
        data: data
    });
}

// 修改材料调查
export function updateMaterialAdjust(data) {
    return request({
        url: '/manager/adjust/edit',
        method: 'put',
        data: data
    });
}

// 删除材料调查
export function deleteMaterialAdjust(id) {
    return request({
        url: `/manager/adjust/delete/${id}`,
        method: 'delete'
    });
}

