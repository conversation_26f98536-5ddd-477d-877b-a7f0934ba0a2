import request from '@/utils/request'

// 查询考核项目List
export function queryExamineTaskList4Eval(query) {
  return request({
    url: '/examine/examine-task/list/eval',
    method: 'get',
    params: query
  })
}

export function queryExamineTaskList4Upload(query) {
  return request({
    url: '/examine/examine-task/list/upload',
    method: 'get',
    params: query
  })
}


// 新增考核项目
export function addExamineProject(data) {
  return request({
    url: '/examine/examine-project/add',
    method: 'post',
    data: data
  })
}

// 编辑考核项目
export function editExamineProject(data) {
  return request({
    url: '/examine/examine-project/edit',
    method: 'put',
    data: data
  })
}

// 删除考核项目
export function deleteExamineProject(ids) {
  return request({
    url: `/examine/examine-project/${ids}`,
    method: 'delete',
  })
}

// 考核项目下发
export function issuedProject(data) {
  return request({
    url: '/examine/examine-project/issued',
    method: 'post',
    data: data
  })
}

export function getExamineTaskResultByTaskId(projectId,taskId) {
  return request({
    url: '/examine/examine-task/result/get',
    method: 'get',
    params:{projectId,taskId}
  })
}

export function saveOrUpdateExamineResult(params) {
  return request({
    url: `/examine/examine-task/result/saveOrUpdate`,
    method: 'post',
    data:params
  })
}

export function submitExamineTaskCheck(taskId) {
  return request({
    url: `/examine/examine-task/result/check`,
    method: 'post',
    params:{taskId:taskId}
  })
}

export function submitExamineTask(taskId) {
  return request({
    url: `/examine/examine-task/result/submit`,
    method: 'post',
    params:{taskId:taskId}
  })
}

export function evaluate(params) {
  return request({
    url: `/examine/examine-task/result/evaluate`,
    method: 'post',
    data:params
  })
}

export function complete(taskId) {
  return request({
    url: `/examine/examine-task/result/complete`,
    method: 'post',
    params:{taskId:taskId}
  })
}

export  function exportXls(taskId) {
  return request({
    url: `/examine/examine-task/result/exportXls`,
    method: 'get',
    responseType:'blob',
    params:{taskId:taskId,t:new Date().getTime()}
  })
}
