import request from "@/utils/request";
import {encrypt} from '@/utils/aesutil'

// 登录方法..
export function login(username, password, code, uuid) {
  username = encrypt(username);
  password = encrypt(password);
  return request({
    url: "/auth/login",
    headers: {
      isToken: false,
      repeatSubmit: false,
    },
    method: "post",
    data: { username, password, code, uuid },
  });
}

export function loginByUser(username) {
  return request({
    url: "/auth/loginByUser",
    headers: {
      isToken: false,
      repeatSubmit: false,
    },
    method: "post",
    data: { username },
  });
}

// 验证码发送和存储
export function sendCode(phoneNumber) {
  return request({
    url: "/auth/sendCode",
    headers: {
      isToken: false,
    },
    method: "post",
    data: { phoneNumber },
  });
}

// 手机号验证码登录接口
export function phoneLogin(phoneNumber, code) {
  return request({
    url: "/auth/phoneLogin",
    headers: {
      isToken: false,
      repeatSubmit: false,
    },
    method: "post",
    data: { phoneNumber, code },
  });
}

// 手机验证码重置密码
export function phoneResetPwd(data) {
  return request({
    url: '/system/user/phoneResetPwd',
    method: 'put',
    data: data
  })
}

// 注册方法
export function register(data) {
  return request({
    url: "/auth/register",
    headers: {
      isToken: false,
    },
    method: "post",
    data: data,
  });
}

// 刷新方法
export function refreshToken() {
  return request({
    url: "/auth/refresh",
    method: "post",
  });
}

// 获取用户详细信息
export function getInfo() {
  return request({
    url: "/system/user/getInfo",
    method: "get",
    params: {
      isApp: 0,
    },
  });
}

// 退出方法
export function logout() {
  return request({
    url: "/auth/logout",
    method: "delete",
  });
}

// 获取验证码
export function getCodeImg() {
  return request({
    url: "/code",
    headers: {
      isToken: false,
    },
    method: "get",
    timeout: 20000,
  });
}
export function isDefaultPassword(params) {
  return request({
    url: "/system/user/isDefaultPassword",
    method: "get",
    params
  });
}
// 获取系统logo
export function getLoginLog() {
  return request({
    url: "/auth/getLoginLog",
    method: "post",
  });
}