import request from '@/utils/request'

// 查询路线管理列表
export function listRoute(query) {
  return request({
    url: '/system/route/list',
    method: 'get',
    params: query
  })
}
// 查询路线管理列表(全量)
export function listAllRoute(query) {
  return request({
    url: '/system/route/listAll',
    method: 'get',
    params: query
  })
}

// 查询路线管理详细
export function getRoute(routeId) {
  return request({
    url: '/system/route/' + routeId,
    method: 'get'
  })
}

// 新增路线管理
export function addRoute(data) {
  return request({
    url: '/system/route',
    method: 'post',
    data: data
  })
}

// 修改路线管理
export function updateRoute(data) {
  return request({
    url: '/system/route',
    method: 'put',
    data: data
  })
}

// 删除路线管理
export function delRoute(routeId) {
  return request({
    url: '/system/route/' + routeId,
    method: 'delete'
  })
}

// 清空路线缓存
export function routeCacheClear() {
  return request({
    url: '/baseData/common/cache/routeCacheClear',
    method: 'post'
  })
}