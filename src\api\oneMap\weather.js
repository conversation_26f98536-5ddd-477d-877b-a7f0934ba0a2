import request from "@/utils/request";

// 天气
export function getWeatherList(data) {
  return request({
    url: "/oneMap/weather/polygon/getList",
    method: "post",
    data,
  });
}

// 生成报
export function createReport(data) {
  return request({
    url: "/oneMap/weather/warning/letters/generate",
    method: "post",
    data,
  });
}

// 雨量情况
export function getRainfallStatistics(data) {
  return request({
    url: "/oneMap/weather/polygon/statistics",
    method: "post",
    data,
  });
}

// 详情
export function getDetail(data) {
  return request({
    url: "/oneMap/weather/warning/get",
    method: "post",
    data,
  });
}

// 获取降雨的预警影响的行政区域
export function getWarningLevelRegion(data) {
  return request({
    url: "/oneMap/weather/warning/level/region",
    method: "post",
    data,
  });
}

// 暴雨预警情况
export function getWarningLevel(data) {
  return request({
    url: "/oneMap/weather/warning/level",
    method: "post",
    data,
  });
}