import request from '@/utils/request'

// 运营管理费录入左侧组织机构树
export function domainTree() {
  return request({
    url: 'manager/operate/metering/domain/tree',
    method: 'post',
  });
}

// 查询运营管理费计量单列表(工程部位)
export function listMeteringTree(data) {
  return request({
    url: 'manager/operate/metering/list',
    method: 'post',
    data
  });
}

// 查询运营管理费计量单费用信息
export function listMetering(data) {
  return request({
    url: 'manager/operate/metering/list/fund',
    method: 'post',
    data
  });
}

// 新增计量单时获取期数列表
export function getNumber(year) {
  return request({
    url: `manager/operate/metering/get/number/${year}`,
    method: 'get'
  });
}

// 新增运营管理费计量单
export function addMetering(data) {
  return request({
    url: 'manager/operate/metering/add',
    method: 'post',
    data
  });
}

// 修改运营管理费计量单
export function editMetering(data) {
  return request({
    url: 'manager/operate/metering/edit',
    method: 'put',
    data
  });
}

// 删除运营管理费计量单
export function deleteMetering(id) {
  return request({
    url: `manager/operate/metering/delete/${id}`,
    method: 'delete'
  });
}
// 审核运营管理费计量单
export function process(data) {
  return request({
    url: 'manager/operate/metering/process',
    method: 'post',
    data
  });
}
// 审核运营管理费计量单
export function nodeInfo(data) {
  return request({
    url: 'manager/operate/metering/node/info',
    method: 'post',
    data
  });
}

// 查询运营管理费计量单明细列表(分页)
export function listDetail(data) {
  return request({
    url: 'manager/operate/metering/detail/list',
    method: 'post',
    data
  });
}
// 新增运营管理费计量单明细
export function addMeteringDetail(data) {
  return request({
    url: 'manager/operate/metering/detail/add',
    method: 'post',
    data
  });
}

// 修改运营管理费计量单明细
export function editMeteringDetail(data) {
  return request({
    url: 'manager/operate/metering/detail/edit',
    method: 'put',
    data
  });
}

// 删除运营管理费计量单明细
export function deleteMeteringDetail(id) {
  return request({
    url: `manager/operate/metering/detail/delete/${id}`,
    method: 'delete'
  });
}

// 查询运营管理费计量单明细金额列表(分页)
export function listMeteringDetailFund(data) {
  return request({
    url: 'manager/operate/metering/detail/fund/list',
    method: 'post',
    data
  });
}


// 電費統計
export function getElectricityFeeStatistics(data) {
  return request({
    url: 'manager/operate/metering/getElectricityFeeStatistics',
    method: 'post',
    data
  });
}


export function withdraw(data) {
  return request({
    url: 'manager/operate/metering/withdraw',
    method: 'post',
    data: data
  })
}
