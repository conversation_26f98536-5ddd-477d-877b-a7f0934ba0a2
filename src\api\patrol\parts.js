import request from '@/utils/request'

// 查询检查类型列表
export function listParts(query) {
  return request({
    url: '/patrol/parts/list',
    method: 'post',
    data: query,
    params: query
  })
}
// 查询检查类型列表
export function listAllParts(query) {
  return request({
    url: '/patrol/parts/listAll',
    method: 'post',
    data: query
  })
}
// 查询检查类型详细
export function getParts(id) {
  return request({
    url: '/patrol/parts/get/' + id,
    method: 'get'
  })
}

// 新增检查类型
export function addParts(data) {
  return request({
    url: '/patrol/parts/add',
    method: 'post',
    data: data
  })
}

// 修改检查类型
export function updateParts(data) {
  return request({
    url: '/patrol/parts/edit',
    method: 'put',
    data: data
  })
}

// 删除检查类型
export function delParts(id) {
  return request({
    url: '/patrol/parts/delete/' + id,
    method: 'delete'
  })
}
