import request from '@/utils/request'

// 查询施工登记方法列表(分页)
export function listMethods(data) {
    return request({
        url: '/manager/registerMethod/list',
        method: 'post',
        data: data
    });
}

// 查询施工登记方法列表(不分页)
export function listAllMethods() {
    return request({
        url: '/manager/registerMethod/listAll',
        method: 'get'
    });
}

// 根据id查询施工登记方法数据
export function getMethodById(id) {
    return request({
        url: `/manager/registerMethod/get/${id}`,
        method: 'get'
    });
}

// 新增施工登记方法
export function addMethod(data) {
    return request({
        url: '/manager/registerMethod/add',
        method: 'post',
        data: data
    });
}

// 修改施工登记方法
export function editMethod(data) {
    return request({
        url: '/manager/registerMethod/edit',
        method: 'put',
        data: data
    });
}

// 删除施工登记方法
export function deleteMethod(id) {
    return request({
        url: `/manager/registerMethod/delete/${id}`,
        method: 'delete'
    });
}

// 导出施工登记方法列表
export function exportMethods() {
    return request({
        url: '/manager/registerMethod/export',
        method: 'post'
    });
}
