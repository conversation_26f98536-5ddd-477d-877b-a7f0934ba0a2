import request from '@/utils/request'

// 查询大件运输列表
export function listTransportation(query) {
  return request({
    url: '/repote/transportation/list',
    method: 'get',
    params: query
  })
}

// 查询大件运输详细
export function getTransportation(id) {
  return request({
    url: '/repote/transportation/get/' + id,
    method: 'get'
  })
}

// 新增大件运输
export function addTransportation(data) {
  return request({
    url: '/repote/transportation/add',
    method: 'post',
    data: data
  })
}

// 修改大件运输
export function updateTransportation(data) {
  return request({
    url: '/repote/transportation/edit',
    method: 'put',
    data: data
  })
}

// 删除大件运输
export function delTransportation(id) {
  return request({
    url: '/repote/transportation/delete/' + id,
    method: 'delete'
  })
}



// 新增大件运输中的路段
export function addTransportationRoad(data) {
  return request({
    url: '/repote/transportationRoad/add',
    method: 'post',
    data: data
  })
}

// 新增大件运输中的、管理处
export function addTransportationDept(data) {
  return request({
    url: '/repote/transportationDept/add',
    method: 'post',
    data: data
  })
}


// 查询大件运输全部列表
export function listTransportationAll(query) {
  return request({
    url: '/repote/transportation/AllList',
    method: 'post',
    params: query
  })
}

// 查询大件运输详细、路段
export function getTransportationRoad(id) {
  return request({
    url: '/repote/transportationRoad/get/' + id,
    method: 'get'
  })
}

// 查询大件运输详细、管理处
export function getTransportationDept(id) {
  return request({
    url: '/repote/transportationDept/get/' + id,
    method: 'get'
  })
}

// 修改大件运输部门
export function updateTransportationDept(data) {
  return request({
    url: '/repote/transportationDept/edit',
    method: 'put',
    data: data
  })
}
// 修改大件运输路段
export function updateTransportationRoad(data) {
  return request({
    url: '/repote/transportationRoad/edit',
    method: 'put',
    data: data
  })
}

// 删除大件运输管理处
export function deleteTransportationDeptAll(data) {
  return request({
    url: '/repote/transportationDept/delete/',
    method: 'post',
    data: data
  })
}



// 删除大件运输管理处
export function deleteTransportationRoadAll(data) {
  return request({
    url: '/repote/transportationRoad/delete/',
    method: 'post',
    data: data
  })
}

// 获取用户权限列表
export function findUserDept() {
  return request({
    url: '/system/dept/getDeptIdList/',
    method: 'get'
  })
}


// 将获取到的部门列表进行判断
export function findUserTransportation(data) {
  return request({
    url: '/repote/transportationDept/getTransportationDeptByList/',
    method: 'post',
    data:  data
  })
}

// 通过获取到的部门去查询列表
export function findTransportationByDeptIDs(query) {
  return request({
    url: '/repote/transportation/AllUserList/',
    method: 'post',
    data: query
  })
}

