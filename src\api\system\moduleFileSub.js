import request from '@/utils/request'

export function getFileSubListByEntity(entity) {
    return request({
        url: '/system/sysModuleFileSub/getListByEntity',
        method: 'post',
        data: entity
    })
}

export function saveModule(entity) {
    return request({
        url: '/system/sysModuleFileSub/saveModule',
        method: 'post',
        data: entity
    })
}

export function deleteFileSubModule(id) {
    return request({
        url: '/system/sysModuleFileSub/deleteModule/' + id,
        method: 'post',
    })
}