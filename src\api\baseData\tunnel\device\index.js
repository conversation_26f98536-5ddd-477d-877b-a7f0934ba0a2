import request from '@/utils/request'

// 查询列表
export function getListPage(data) {
  return request({
    url: '/baseData/tunnel/device/getListPage',
    method: 'post',
    data
  })
}

// 删除
export function deleteByIds(data) {
  return request({
    url: '/baseData/tunnel/device/delete',
    method: 'delete',
    data: data
  })
}

// 新增
export function addTunnelDeviceTunnel(data) {
  return request({
    url: '/baseData/tunnel/device/add',
    method: 'post',
    data: data
  })
}



// 修改
export function updateTunnelDeviceTunnel(data) {
  return request({
    url: '/baseData/tunnel/device/edit',
    method: 'put',
    data: data
  })
}

///tunnel/device/get
export function getTunnelDevice(params) {
  return request({
    url: '/baseData/tunnel/device/get',
    method: 'get',
    params
  })
}

