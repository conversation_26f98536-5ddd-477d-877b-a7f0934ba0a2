import request from '@/utils/request'

// 查询预算版本配置列表
export function listVersion(query) {
  return request({
    url: '/budget/version/list',
    method: 'get',
    params: query
  })
}

// 查询预算版本配置详细
export function getVersion(id) {
  return request({
    url: '/budget/version/get/' + id,
    method: 'get'
  })
}

// 新增预算版本配置
export function addVersion(data) {
  return request({
    url: '/budget/version/add',
    method: 'post',
    data: data
  })
}

// 修改预算版本配置
export function updateVersion(data) {
  return request({
    url: '/budget/version/edit',
    method: 'put',
    data: data
  })
}

// 删除预算版本配置
export function delVersion(id) {
  return request({
    url: '/budget/version/delete/' + id,
    method: 'delete'
  })
}
