import request from '@/utils/request'

/**
 * 报告配置管理查询
 * @param {Object} query 查询参数
 * @returns {Promise} 请求Promise
 */
export function getReportStructurePage(query) {
  return request({
    url: '/jgjcadmin/api/evaluationReport/reportStructure/page',
    method: 'get',
    params: query
  })
}

/**
 * 报告配置管理新增
 * @param {Object} data 新增数据
 * @returns {Promise} 请求Promise
 */
export function saveReportStructure(data) {
  return request({
    url: '/jgjcadmin/api/evaluationReport/reportStructure/save',
    method: 'post',
    data
  })
}

/**
 * 报告配置管理编辑
 * @param {Object} data 更新数据
 * @returns {Promise} 请求Promise
 */
export function updateReportStructure(data) {
  return request({
    url: '/jgjcadmin/api/evaluationReport/reportStructure/update',
    method: 'post',
    data
  })
}

/**
 * 报告配置管理删除
 * @param {Object} data 删除参数(通常包含id)
 * @returns {Promise} 请求Promise
 */
export function deleteReportStructure(data) {
  return request({
    url: '/jgjcadmin/api/evaluationReport/reportStructure/delete',
    method: 'post',
    data
  })
}

/**
 * 报告配置树接口
 * @param {Object} data 查询参数
 * @returns {Promise} 请求Promise
 */
export function getTreeStructureUnderNodeCode(data) {
  return request({
    url: '/jgjcadmin/api/evaluationReport/reportStructure/getReportStructureTreeNodeData',
    method: 'post',
    data
  })
}

/**
 * 报告配置传感器详细数据接口
 * @param {Object} data 查询参数
 * @returns {Promise} 请求Promise
 */
export function getSensorListUnderNodeCode(data) {
  return request({
    url: '/jgjcadmin/api/evaluationReport/reportStructure/getReportStructureSensorData',
    method: 'post',
    data
  })
}
