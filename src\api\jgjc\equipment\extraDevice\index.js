/**
 * 其他设备管理API
 */

import request from '@/utils/request'; // 假设您有一个request工具

/**
 * 其他设备新增
 * @param {Object} data 设备数据
 * @returns {Promise} 请求Promise
 */
export function saveOtherDevice(data) {
  return request({
    url: '/jgjcadmin/api/basedata/other-device/save',
    method: 'post',
    data
  });
}

/**
 * 其他设备编辑
 * @param {Object} data 设备数据
 * @returns {Promise} 请求Promise
 */
export function updateOtherDevice(data) {
  return request({
    url: '/jgjcadmin/api/basedata/other-device/update',
    method: 'post',
    data
  });
}

/**
 * 其他设备删除
 * @param {Object} data 删除参数（通常包含id）
 * @returns {Promise} 请求Promise
 */
export function deleteOtherDevice(data) {
  return request({
    url: '/jgjcadmin/api/basedata/other-device/delete',
    method: 'post',
    data
  });
}

/**
 * 其他设备分页查询
 * @param {Object} data 查询参数
 * @returns {Promise} 请求Promise
 */
export function getOtherDevicePage(data) {
  return request({
    url: '/jgjcadmin/api/basedata/other-device/page',
    method: 'post',
    data
  });
}

/**
 * 其他设备批量新增
 * @param {Array} data 设备数据数组
 * @returns {Promise} 请求Promise
 */
export function batchAddOtherDevice(data) {
  return request({
    url: '/jgjcadmin/api/basedata/other-device/batchAdd',
    method: 'post',
    data
  });
}

/**
 * 获取管养处和路段tree(数据取自养护系统)
 * @param {Object} [params] 可选参数
 * @returns {Promise} 请求Promise
 */
export function getMaintenanceTree(params) {
  return request({
    url: '/jgjcadmin/api/flash/structure/getTree',
    method: 'post',
    data: params || {}
  });
}

/**
 * 设备类型分页查询
 * @param {Object} data 查询参数
 * @returns {Promise} 请求Promise
 */
export function getDeviceTypePage(data) {
  return request({
    url: '/jgjcadmin/api/basedata/devicetype/page',
    method: 'post',
    data
  });
}
