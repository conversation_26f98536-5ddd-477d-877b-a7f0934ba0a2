import request from '@/utils/request'

// 其他合同费用录入列表(分页)
export function listContractOtherFundCalc(param) {
    return request({
        url: `manager/contractOtherFundCalcDetail/list`,
        method: 'post',
        data: param
    })
}

// 申请单查看
export function viewContractOtherFundCalcList(param) {
    return request({
        url: `manager/contractOtherFundCalcDetail/viewList`,
        method: 'post',
        data: param
    })
}

// 根据id查询其他合同费用录入
export function getContractOtherFundCalcDetail(id) {
    return request({
        url: `manager/contractOtherFundCalcDetail/get/${id}`,
        method: 'get'
    })
}

// 新增其他合同费用录入
export function addContractOtherFundCalc(param) {
    return request({
        url: `manager/contractOtherFundCalcDetail/add`,
        method: 'post',
        data: param
    })
}

// 修改其他合同费用录入
export function editContractOtherFundCalc(param) {
    return request({
        url: `manager/contractOtherFundCalcDetail/edit`,
        method: 'put',
        data: param
    })
}


// 提交
export function submitContractOtherFundCalc(param) {
    return request({
        url: `manager/contractOtherFundCalcDetail/submit`,
        method: 'post',
        data: param
    })
}

// 撤回
export function rejectContractOtherFundCalc(param) {
    return request({
        url: `manager/contractOtherFundCalcDetail/reject`,
        method: 'post',
        data: param
    })
}

// 删除
export function deleteContractOtherFundCalc(id) {
    return request({
        url: `manager/contractOtherFundCalcDetail/delete/${id}`,
        method: 'delete',
    })
}

// 查询上期计量单列表
export function getPreCalc(data) {
    return request({
        url: `manager/contractOtherFundCalcDetail/last/list`,
        method: 'get',
        params: data
    })
}



// 查询上期计量单列表
export function getNodeInfo(data) {
  return request({
    url: `manager/contractOtherFundCalcDetail/getNodeInfo`,
    method: 'post',
    data
  })
}


// 查询其他合同费用录入详情列表
export function contractOtherFundCalcList(data) {
    return request({
        url: `manager/contractOtherFundCalc/list`,
        method: 'post',
        data
    })
}
