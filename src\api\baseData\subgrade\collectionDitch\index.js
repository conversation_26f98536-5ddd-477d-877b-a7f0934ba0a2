import request from '@/utils/request'

// 查询集水沟列表
export function listCollectionDitchRecords(data) {
  return request({
    url: '/baseData/collectionDitch/getListPage',
    method: 'post',
    data:data
  })
}

// 查询集水沟详细
export function getCollectionDitch(detectionRecordsId) {
  return request({
    url: '/baseData/collectionDitch/get/' + detectionRecordsId,
    method: 'get'
  })
}

// 新增集水沟
export function addCollectionDitch(data) {
  return request({
    url: '/baseData/collectionDitch/add',
    method: 'post',
    data: data
  })
}

// 暂存集水沟
export function tempaddCollectionDitch(data) {
  return request({
    url: '/baseData/collectionDitch/tempAdd',
    method: 'post',
    data: data
  })
}

// 修改集水沟
export function updateCollectionDitch(data) {
  return request({
    url: '/baseData/collectionDitch/edit',
    method: 'put',
    data: data
  })
}

// 删除集水沟
export function delCollectionDitch(detectionRecordsIds) {
  return request({
    url: '/baseData/collectionDitch/delete/'+detectionRecordsIds.join(','),
    method: 'delete',
  
  })
}






