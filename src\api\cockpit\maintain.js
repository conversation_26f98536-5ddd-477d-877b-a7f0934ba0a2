import request from "@/utils/request";

/**
 * 日常养护专题-日常养护事件
 * @returns 
 */
export function getDiseaseEventDealType(year) {
  return request({
    url: `/manager/constructionStat/getDiseaseEventDealType/${year}`,
    method: "get",
    notVerifyDuplicates: true,
  });
}

/**
 * 日常养护专题-事件类型占比
 * @returns 
 */

export function getDiseaseEventTypeRatio(year) {
  return request({
    url: `/manager/constructionStat/getDiseaseEventType/${year}`,
    method: "get",
    notVerifyDuplicates: true,
  });
}

/**
 * 日常养护专题-修复费用统计
 * @returns 
 */
export function getDiseaseEventCost(year) {
  return request({
    url: `/manager/constructionStat/getSumFundByAssetType/${year}`,
    method: "get",
    notVerifyDuplicates: true,
  });
}

/**
 * 日常养护专题-事件趋势
 * @returns 
 */
export function getDiseaseEventTrends(year) {
  return request({
    url: `/manager/constructionStat/getDiseaseCountByYear/${year}`,
    method: "get",
    notVerifyDuplicates: true,
  });
}

/**
 * 日常养护专题-各管养单位资金使用情况
 * @returns 
 */
export function getDiseaseEventFunds(year) {
  return request({
    url: `/manager/constructionStat/getDomainCountAndFund/${year}`,
    method: "get",
    notVerifyDuplicates: true,
  });
}

/**
 * 日常养护专题-预算与实际完成对比分析
 * @returns 
 */
export function getDiseaseEventCompare(year) {
  return request({
    url: `/manager/constructionStat/getPlanAndUseFund/${year}`,
    method: "get",
    notVerifyDuplicates: true,
  });
}

/**
 * 日常养护专题-事件数量对比
 * @returns 
 */
export function getDiseaseEventCount(year) {
  return request({
    url: `/manager/constructionStat/getDiseaseEventCompare/${year}`,
    method: "get",
    notVerifyDuplicates: true,
  });
}

/**
 * 专题养护-养护工程项目
 * @returns 
 */
export function getMaintenanceProject(year) {
  return request({
    url: `/manager/project/quantity/statistics/${year}`,
    method: "get",
    notVerifyDuplicates: true,
  });
}

/**
 * 专题养护-工程类别统计
 * @returns 
 */
export function getMaintenanceProjectType(year) {
  return request({
    url: `/manager/proj/construction/quantity/statistics/${year}`,
    method: "get",
    notVerifyDuplicates: true,
  });
}

/**
 * 专题养护-养护项目立项趋势
 * @returns 
 */
export function getProjectNumberStatistics(year) {
  return request({
    url: `/manager/project/number/statistics/${year}`,
    method: "get",
    notVerifyDuplicates: true,
  });
}

/**
 * 专题养护-施工情况
 * @returns 
 */
export function getConstructionStatistics(year) {
  return request({
    url: `/manager/proj/construction/situation/statistics/${year}`,
    method: "get",
    notVerifyDuplicates: true,
  });
}


/**
 * 专题养护-各管养单位资金使用情况
 * @returns 
 */
export function getDomainFundStatistics(year) {
  return request({
    url: `/manager/proj/construction/fund/statistics/${year}`,
    method: "get",
    notVerifyDuplicates: true,
  });
}

/**
 * 专题养护-专项养护资金使用情况
 * @returns 
 */
export function getSpecialFundStatistics(year) {
  return request({
    url: `/manager/project/budget/fund/statistics/${year}`,
    method: "get",
    notVerifyDuplicates: true,
  });
}

/**
 * 专题养护-工程类型费用占比
 * @returns 
 */
export function getProjectTypeFundStatistics(year) {
  return request({
    url: `/manager/project/settle/fund/statistics/${year}`,
    method: "get",
    notVerifyDuplicates: true,
  });
}

/**
 * 专题养护-养护工程预算趋势对比
 * @returns 
 */
export function getProjectBudgetStatistics(year) {
  return request({
    url: `/manager/project/budget/settle/fund/statistics/`,
    method: "get",
    notVerifyDuplicates: true,
  });
}