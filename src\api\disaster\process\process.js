// 获取公路高边坡信息调查详细信息
import request from "@/utils/request";

// 根据流程实例 ID 获取流程节点信息
export function queryAuditInfo(id) {
  return request({
    url: `/disaster/disaster-process/node-info/${id}`,
    method: 'get',
  })
}

  // 更新节点时间
  export function updateNodeTime(data) {
    return request({
      url: `/workFlow/history/update-activity-instance-time`,
      method: 'put',
      data,
    })
}

// 更新节点信息
export function updateNodeInfo(data) {
  return request({
    url: `/workFlow/history/update-activity-instance`,
    method: 'put',
    data,
  })
}

// 根据流程分类获取待办任务业务ID集合
export function queryTodoTaskByCategory(category) {
  return request({
    url: `/disaster/disaster-process/todo-task/by-category/${category}`,
    method: 'get',
  })
}

// 根据业务 ID 获取待办任务
export function queryTodoTaskByBusinessKey(businessKey) {
  return request({
    url: `/disaster/disaster-process/todo-task/by-business-key/${businessKey}`,
    method: 'get',
  })
}
