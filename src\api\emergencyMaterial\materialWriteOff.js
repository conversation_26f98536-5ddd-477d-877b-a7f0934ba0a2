import request from '@/utils/request'

// 应急物资核销审核节点信息
export function getNodeInfo(data) {
  return request({
    url: '/manager/emergency/material/verification/node/info',
    method: 'post',
    data
  })
}

// 应急物资核销单列表
export function getWriteOffApplicitionList(data) {
  return request({
    url: '/manager/emergency/material/verification/list',
    method: 'post',
    data
  })
}

// 删除申请入库单
export function delWriteOff(id) {
  return request({
    url: `/manager/emergency/material/verification/delete/${id}`,
    method: 'delete',
  })
}

//  核销单审核
export function submitWriteOff(data) {
  return request({
    url: '/manager/emergency/material/verification/process',
    method: 'post',
    data
  })
}

// 新增核销单
export function addWriteOffnApplicition(data) {
  return request({
    url: '/manager/emergency/material/verification/add',
    method: 'post',
    data
  })
}

// 修改核销单
export function editWriteOffApplicition(data) {
  return request({
    url: '/manager/emergency/material/verification/edit',
    method: 'put',
    data
  })
}

// 根据id查询应急物资核销单数据
export function getDetailWriteOffList(id) {
  return request({
    url: `/manager/emergency/material/verification/get/${id}`,
    method: 'get'
  })
}

// 查询应急物资核销单明细列表
export function getWriteOffDetailList(data) {
  return request({
    url: '/manager/emergency/material/outbound/detail/list',
    method: 'post',
    data
  })
}

// 查询应急物资核销单全部明细列表
export function getWriteOffDetailAllList(data) {
  return request({
    url: '/manager/emergency/material/outbound/detail/listAll',
    method: 'post',
    data
  })
}

// 获取核销单审核列表
export function getWriteOffAuditList (data) {
  return request({
    url: '/manager/emergency/material/verification/pending/list',
    method: 'post',
    data
  })
}

// 获取核销单结果列表
export function getWriteOffResultList(data) {
  return request({
    url: '/manager/emergency/material/verification/view',
    method: 'post',
    data
  })
}


// 报表
export function previewReport(id) {
  return request({
    url: `/manager/emergency/material/verification/report/preview?id=${id}`,
    method: 'get',
  })
}
