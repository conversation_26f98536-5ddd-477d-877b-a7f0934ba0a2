import request from '@/utils/request'

// 查询桥梁图纸列表
export function listBridgeDrawingRecords(params) {
  return request({
    url: '/baseData/bridge/drawing/getListPage',
    method: 'get',
    params
  })
}

// 导出
export function drawingAdd(data) {
  return request({
    url: '/baseData/bridge/drawing/add',
    method: 'post',
    data: data
  })
}

// 删除桥梁静态数据
export function delStatic(data) {
  return request({
    url: '/baseData/bridge/drawing/delete/'+data,
    method: 'delete',
    
  })
}



