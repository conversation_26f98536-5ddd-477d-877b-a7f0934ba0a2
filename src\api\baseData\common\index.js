import request from '@/utils/request'

/**
 * 获取唯一标识id
 */
export function createIdWorker() {
  return request({
    url: '/baseData/common/server/createIdWorker',
    method: 'get',
  })
}

/**
 * 运营状态变更
 * @param {\} data {baseDataType：基础数据类型，businessReason，businessState，remark，dataId：数据id}
 * @returns 
 */
export function businessStatusRecord(data) {
  return request({
    url: '/baseData/businessStatusRecord/add',
    method: 'post',
    data,
  });
}

/**
 * 条件分页查询运营状态明细列表
 * @param {\} data {baseDataType：基础数据类型，pageNum，pageSize，dataId：数据id}
 * @returns 
 */
export function getBusinessStatusRecord(data) {
  return request({
    url: '/baseData/businessStatusRecord/getListPage',
    method: 'post',
    data,
  });
}