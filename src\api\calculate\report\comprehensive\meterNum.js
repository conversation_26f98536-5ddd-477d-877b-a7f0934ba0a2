import request from '@/utils/request'

// 计量期数统计报表(集团)
export function getJTReport(paramDto) {
	return request({url: '/manager/meter/num/jt/report', method: 'post', data: paramDto});
}

// 计量期数统计报表(管理处)
export function getGLCReport(paramDto) {
	return request({url: '/manager/meter/num/glc/report', method: 'post', data: paramDto});
}

// 计量期数统计报表左侧树
export function getTree() {
	return request({url: '/manager/meter/num/tree', method: 'get'});
}

// 日常养护查看列表
export function dailyList(paramDto) {
	return request({url: '/manager/meter/num/daily/list', method: 'post', data: paramDto});
}

// 日常养护监理计量查看列表
export function dailySupList(paramDto) {
	return request({url: '/manager/meter/num/daily/sup/list', method: 'post', data: paramDto});
}

// 被损被盗查看列表
export function theftList(paramDto) {
	return request({url: '/manager/meter/num/theft/list', method: 'post', data: paramDto});
}

// 被损被盗监理计量查看列表
export function theftSupList(paramDto) {
	return request({url: '/manager/meter/num/theft/sup/list', method: 'post', data: paramDto});
}

// 养护工程查看列表
export function projList(paramDto) {
	return request({url: '/manager/meter/num/proj/list', method: 'post', data: paramDto});
}

// 养护工程监理计量查看列表
export function projSupList(paramDto) {
	return request({url: '/manager/meter/num/proj/sup/list', method: 'post', data: paramDto});
}

// 养护工程设计计量查看列表
export function projDesignList(paramDto) {
	return request({url: '/manager/meter/num/proj/design/list', method: 'post', data: paramDto});
}

// 定期检测查看列表
export function checkList(paramDto) {
	return request({url: '/manager/meter/num/check/list', method: 'post', data: paramDto});
}

// 运营费用查看列表
export function operateList(paramDto) {
	return request({url: '/manager/meter/num/operate/list', method: 'post', data: paramDto});
}

// 其它合同费用查看列表
export function otherConList(paramDto) {
	return request({url: '/manager/meter/num/other/con/list', method: 'post', data: paramDto});
}
