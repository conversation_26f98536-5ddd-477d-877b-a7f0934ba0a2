import request from "@/utils/request";
//  报警状态
export function getNotDealRecordInformationTibet() {
  return request({
    url: "http://1.95.126.5/xboot/displayScreen/default/getNotDealRecordInformation",
    method: "get",
    notVerifyDuplicates: true
  });
}
// 监测类别统计
export function getSensorCountKindByMonitorTypeTibet() {
  return request({
    url: "http://1.95.126.5/xboot/displayScreen/getSensorCountKindByMonitorType",
    method: "get",
    notVerifyDuplicates: true
  });
}
// 传感器运营状态
export function getWarningListTibet(params) {
  return request({
    url: "http://1.95.126.5/xboot/data/getDataInfo",
    method: "get",
    params,
    notVerifyDuplicates: true
  });
}


//启动上行，下行，全部
export function startWarnPlan(data) {
  return request({
    url: '/jgjcadmin/api/flash/flash-generic-plan/startWarnPlan',
    method: 'post',
    data
  })
}

//结束预案
export function endWarnPlan(data) {
  return request({
    url: '/jgjcadmin/api/flash/flash-generic-plan/endWarnPlan',
    method: 'post',
    data
  })
}

//读取结构物在线状态信息
export function getWdmStructFlashDeviceStatus(data) {
  return request({
    url: '/jgjcadmin/api/flash/flash-generic-plan/getWdmStructFlashDeviceStatus',
    method: 'post',
    data
  })
}

//解除现场预警
export function offSiteFlashWarning(data) {
  return request({
    url: '/jgjcadmin/api/flash/flash-generic-plan/offSiteFlashWarning',
    method: 'post',
    data
  })
}

// 结构物视频列表
export function getVideoList(data) {
  return request({
    url: '/jgjcadmin/api/basedata/structurevideo/page',
    method: 'post',
    data
  })
}

//获取萤石云视频token
export function getTokenData() {
  return request({
    url: '/jgjcadmin/api/videoToken/getTokenData',
    method: 'post'
  })
}

// 道闸开启关闭
export function xzSwitchCtl(data) {
  return request({
    url: '/jgjcadmin/api/xz/gateDevice/switchCtl',
    method: 'post',
    data
  })
}

// 绑定设备
export function deviceToRisk(data) {
  return request({
    url: '/jgjcadmin/api/xz/gateDevice/deviceToRisk',
    method: 'post',
    data
  })
}

// 移除已绑定设备
export function removeDevice(data) {
  return request({
    url: '/jgjcadmin/api/xz/gateDevice/removeDevice?deviceId=' + data,
    method: 'delete',
  })
}

// 获取已绑定的设备
export function getBoundDevices(params) {
  return request({
    url: '/jgjcadmin/api/xz/gateDevice/boundDevices',
    method: 'get',
    params
  })
}

// 获取未绑定的设备
export function getUnboundDevices(params) {
  return request({
    url: '/jgjcadmin/api/xz/gateDevice/unboundDevices',
    method: 'get',
    params
  })
}

//设置单个设备状态（开启、关闭、重启）
export function setSingleFlashWarnDev(data) {
  return request({
    url: '/jgjcadmin/api/flash/flash-generic-plan/setSingleFlashWarnDev',
    method: 'post',
    data
  })
}