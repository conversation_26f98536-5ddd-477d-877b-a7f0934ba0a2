import request from '@/utils/request'

// 阈值管理主页查询
export function getAlertManagePage(data) {
  return request({
    url: '/jgjcadmin/api/healthassessment/alertmanage/page',
    method: 'get',
    params: data
  });
}

// 阈值管理编辑
export function addThresholdInfo(data) {
  return request({
    url: '/jgjcadmin/api/healthassessment/alertmanage/addThresholdInfo',
    method: 'post',
    data: data
  });
}

// 阈值管理删除
export function deleteAlertManage(data) {
  return request({
    url: '/jgjcadmin/api/healthassessment/alertmanage/delete',
    method: 'post',
    data: data
  });
}

// 预警管理编辑时查看预警规则明细
export function getAlertManageRuleParamsDetail(data) {
  return request({
    url: '/jgjcadmin/api/healthassessment/alertruleparameter/alertManageRuleParamsDetail',
    method: 'get',
    params: data
  });
}

// 预警管理规则单位获取
export function getValueTypePage(data) {
  return request({
    url: '/jgjcadmin/api/basedata/valuetype/page',
    method: 'post',
    data
  });
}

// 预警规则管理查询
export function getAlertRuleTypePage(data) {
  return request({
    url: '/jgjcadmin/api/healthassessment/alertruletype/page',
    method: 'get',
    params: data
  });
}
