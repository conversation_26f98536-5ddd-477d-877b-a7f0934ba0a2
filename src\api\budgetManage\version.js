import request from '@/utils/request'

// 查询预算版本配置列表(分页)
export function listBudgetVersions(data) {
    return request({
        url: '/manager/version/list',
        method: 'post',
        data: data
    });
}

// 查询预算版本配置列表(不分页)
export function listAllBudgetVersions() {
    return request({
        url: '/manager/version/listAll',
        method: 'get'
    });
}

// 根据id查询预算版本配置数据
export function getBudgetVersionById(id) {
    return request({
        url: `/manager/version/get/${id}`,
        method: 'get'
    });
}

// 新增预算版本配置
export function addBudgetVersion(data) {
    return request({
        url: '/manager/version/add',
        method: 'post',
        data: data
    });
}

// 修改预算版本配置
export function editBudgetVersion(data) {
    return request({
        url: '/manager/version/edit',
        method: 'put',
        data: data
    });
}

// 删除预算版本配置
export function deleteBudgetVersion(id) {
    return request({
        url: `/manager/version/delete/${id}`,
        method: 'delete'
    });
}
