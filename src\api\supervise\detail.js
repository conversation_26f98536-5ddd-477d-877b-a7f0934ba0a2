import request from '@/utils/request'

// 查询督查详情列表
export function listDetail(query) {
  return request({
    url: '/patrol/superviseDetail/list',
    method: 'get',
    params: query
  })
}

// 查询督查详情详细
export function getDetail(id) {
  return request({
    url: '/patrol/superviseDetail/get/' + id,
    method: 'get'
  })
}

// 新增督查详情
export function addDetail(data) {
  return request({
    url: '/patrol/superviseDetail/add',
    method: 'post',
    data: data
  })
}

// 修改督查详情
export function updateDetail(data) {
  return request({
    url: '/patrol/superviseDetail/edit',
    method: 'put',
    data: data
  })
}

// 删除督查详情
export function delDetail(id) {
  return request({
    url: '/patrol/superviseDetail/delete/' + id,
    method: 'delete'
  })
}

/*
import request from '@/utils/request'

// 查询督查详情列表
export function listDetail(query) {
  return request({
    url: '/supervise/superviseDetail/list',
    method: 'get',
    params: query
  })
}

// 查询督查详情详细
export function getDetail(id) {
  return request({
    url: '/supervise/superviseDetail/get/' + id,
    method: 'get'
  })
}

// 新增督查详情
export function addDetail(data) {
  return request({
    url: '/supervise/superviseDetail/add',
    method: 'post',
    data: data
  })
}

// 修改督查详情
export function updateDetail(data) {
  return request({
    url: '/supervise/superviseDetail/edit',
    method: 'put',
    data: data
  })
}

// 删除督查详情
export function delDetail(id) {
  return request({
    url: '/supervise/superviseDetail/delete/' + id,
    method: 'delete'
  })
}
*/
