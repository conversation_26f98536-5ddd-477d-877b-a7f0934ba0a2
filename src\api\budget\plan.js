import request from '@/utils/request'

// 查询预算计划列表
export function listPlan(query) {
  return request({
    url: '/budget/plan/list',
    method: 'get',
    params: query
  })
}

// 查询预算计划详细
export function getPlan(id) {
  return request({
    url: '/budget/plan/get/' + id,
    method: 'get'
  })
}

// 新增预算计划
export function addPlan(data) {
  return request({
    url: '/budget/plan/add',
    method: 'post',
    data: data
  })
}

// 修改预算计划
export function updatePlan(data) {
  return request({
    url: '/budget/plan/edit',
    method: 'put',
    data: data
  })
}

// 删除预算计划
export function delPlan(id) {
  return request({
    url: '/budget/plan/delete/' + id,
    method: 'delete'
  })
}
