import request from '@/utils/request'

// 查询养护工程施工登记列表
export function queryList(data) {
    return request({
        url: '/manager/project/list',
        method: 'post',
        data,
    });
}


// 新增养护工程
export function addProject(data) {
    return request({
        url: '/manager/project/add',
        method: 'post',
        data,
    });
}


// 修改养护工程
export function editProject(data) {
    return request({
        url: '/manager/project/edit',
        method: 'put',
        data,
    });
}

// 删除养护工程
export function deleteProject(id) {
    return request({
        url: `/manager/project/delete/${id}`,
        method: 'delete',
    });
}



// 更新项目状态
export function updateProjectStatus(data) {
    return request({
        url: '/manager/project/update/status',
        method: 'post',
        data,
    });
}

// 根据项目id判断项目是否可以生成任务单
export function isAdd(id) {
    return request({
        url: `/manager/project/is/add/${id}`,
        method: 'get',
    });
}



// 根据路段信息查询专项明细数据
export function getListBySectionParam(data) {
    return request({
        url: '/manager/plandetail/getlistbysectionparam',
        method: 'post',
        data,
    });
}

// 根据id查询养护工程数据
export function getProject(id) {
    return request({
        url: `/manager/project/get/${id}`,
        method: 'get',
    });
}

