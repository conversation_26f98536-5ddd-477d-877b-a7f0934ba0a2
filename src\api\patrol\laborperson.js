import request from '@/utils/request'

// 查询劳务单位人员列表
export function listLaborperson(query) {
  return request({
    url: '/manager/laborperson/list',
    method: 'get',
    params: query
  })
}

// 查询劳务单位人员列表
export function listbyParam(query) {
  return request({
    url: '/manager/laborperson/listbyParam',
    method: 'post',
    data: query
  })
}

// 获取劳务人员用户
export function getLaborPersonList(data) {
  return request({
    url: '/manager/laborperson/getLaborPersonList',
    method: 'post',
    data: data
  })
}

// 查询劳务单位人员详细
export function getLaborperson(id) {
  return request({
    url: '/manager/laborperson/get/' + id,
    method: 'get'
  })
}

// 新增劳务单位人员
export function addLaborperson(data) {
  return request({
    url: '/manager/laborperson/add',
    method: 'post',
    data: data
  })
}

// 修改劳务单位人员
export function updateLaborperson(data) {
  return request({
    url: '/manager/laborperson/edit',
    method: 'put',
    data: data
  })
}

// 删除劳务单位人员
export function delLaborperson(id) {
  return request({
    url: '/manager/laborperson/delete/' + id,
    method: 'delete'
  })
}

// 删除劳务单位人员
export function batchDelLaborperson(ids) {
  return request({
    url: '/manager/laborperson/deleteIds',
    method: 'post',
    data: ids
  })
}
