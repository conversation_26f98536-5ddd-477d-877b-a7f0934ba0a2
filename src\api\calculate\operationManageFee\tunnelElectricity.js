import request from '@/utils/request'

// 查询隧道电费列表(分页)
export function listTunnelElectricityPage(data) {
    return request({
        url: 'manager/operate/tunnel/electricity/list',
        method: 'post',
        data
    });
}

// 查询隧道电费列表(不分页)
export function listTunnelElectricityAll() {
    return request({
        url: 'manager/operate/tunnel/electricity/listAll',
        method: 'get'
    });
}

// 根据id查询隧道电费数据
export function getTunnelElectricityById(id) {
    return request({
        url: `manager/operate/tunnel/electricity/get/${id}`,
        method: 'get'
    });
}

// 新增隧道电费
export function addTunnelElectricity(data) {
    return request({
        url: 'manager/operate/tunnel/electricity/add',
        method: 'post',
        data
    });
}

// 修改隧道电费
export function editTunnelElectricity(data) {
    return request({
        url: 'manager/operate/tunnel/electricity/edit',
        method: 'put',
        data
    });
}

// 删除隧道电费
export function deleteTunnelElectricity(id) {
    return request({
        url: `manager/operate/tunnel/electricity/delete/${id}`,
        method: 'delete'
    });
}

