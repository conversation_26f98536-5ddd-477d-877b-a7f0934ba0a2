import request from '@/utils/request'

// 新增灾害数据（其它）
export function createRiskOther(query) {
  return request({
    url: '/disaster/disaster-risk/other/add',
    method: 'post',
    data: query
  })
}

// 更新灾害数据（其它）
export function updateRiskOther(query) {
  return request({
    url: '/disaster/disaster-risk/other/edit',
    method: 'put',
    data: query
  })
}

export function dataSync() {
  return request({
    url: '/disaster/disaster-risk/other/edit',
    method: 'put',
    data: query
  })
}