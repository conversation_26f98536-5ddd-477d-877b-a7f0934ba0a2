import request from '@/utils/request'

// 查询列表
export function getListPage(data) {
  return request({
    url: '/baseData/route/iceCovered/getListPage',
    method: 'post',
    data
  })
}

// 增
export function addIceCovered(data) {
  return request({
    url: "/baseData/route/iceCovered/add",
    method: "post",
    data,
  });
}

// 暂存
export function tempIceCovered(data) {
  return request({
    url: "/baseData/route/iceCovered/temp/add",
    method: "post",
    data,
  });
}

// 删
export function delIceCovered(data) {
  return request({
    url: '/baseData/route/iceCovered/delete',
    method: 'delete',
    data
  })
}

// 改
export function updateIceCovered(data) {
  return request({
    url: '/baseData/route/iceCovered/edit',
    method: 'post',
    data: data
  })
}

// 查
export function getIceCovered(id) {

  return request({
    url: `/baseData/route/iceCovered/get/${id}`,
    method: "get",
  });
}

