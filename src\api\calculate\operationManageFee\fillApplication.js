import request from '@/utils/request'

// 查询业务申请单列表(分页)
export function listApplications(param) {
    return request({
        url: 'manager/operate/application/list',
        method: 'post',
        data: param
    });
}


// 查询业务申请单列表(分页)
export function viewListApplications(param) {
    return request({
        url: 'manager/operate/application/viewList',
        method: 'post',
        data: param
    });
}

// 查询业务申请单列表(不分页)
export function listAllApplications() {
    return request({
        url: 'manager/operate/application/listAll',
        method: 'get'
    });
}

// 根据id查询业务申请单数据
export function getApplicationDetail(id) {
    return request({
        url: `manager/operate/application/get/${id}`,
        method: 'get'
    });
}

// 新增业务申请单
export function addApplication(operateTCApplication) {
    return request({
        url: 'manager/operate/application/add',
        method: 'post',
        data: operateTCApplication
    });
}

// 提交业务申请单
export function submitApplication(operateTCApplication) {
    return request({
        url: 'manager/operate/application/submit',
        method: 'post',
        data: operateTCApplication
    });
}

// 审核业务申请单
export function reviewApplication(operateTCApplication) {
    return request({
        url: 'manager/operate/application/review',
        method: 'post',
        data: operateTCApplication
    });
}

// 修改业务申请单
export function updateApplication(operateTCApplication) {
    return request({
        url: 'manager/operate/application/edit',
        method: 'put',
        data: operateTCApplication
    });
}

// 删除业务申请单
export function deleteApplication(id) {
    return request({
        url: `manager/operate/application/delete/${id}`,
        method: 'delete'
    });
}


// 撤回
export function rejectApplication(operateTCApplication) {
    return request({
        url: 'manager/operate/application/reject',
        method: 'post',
        data: operateTCApplication
    });
}


// 撤回
export function nodeInfo(id) {
    return request({
        url: `manager/operate/application/nodeInfo/${id}`,
        method: 'get'
    });
}
