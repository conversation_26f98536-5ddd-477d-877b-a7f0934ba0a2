import request from '@/utils/request'

// 查询预算版本配置列表(分页)
export function GetYearconfigList(data) {
    return request({
        url: '/manager/yearconfig/list',
        method: 'post',
        data: data
    });
}

// 查询预算版本配置列表(分页)
export function AddYearconfigList(data) {
    return request({
        url: '/manager/yearconfig/add',
        method: 'post',
        data: data
    });
}

// 修改年度配置
export function EditYearconfigList(data) {
    return request({
        url: '/manager/yearconfig/edit',
        method: 'put',
        data: data
    });
}
// 删除年度配置
export function DeleteYearconfig(id) {
    return request({
        url: `/manager/yearconfig/delete/${id}`,
        method: 'delete'
    });
}