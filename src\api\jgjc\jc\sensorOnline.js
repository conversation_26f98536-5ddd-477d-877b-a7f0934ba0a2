import request from '@/utils/request'

// 查询传感器在线状态列表
export function listOnline(query) {
  return request({
    url: '/jgjc-manager/api/console/jc/online/list',
    method: 'get',
    params: query
  })
}

// 查询传感器在线状态详细
export function getOnline(id) {
  return request({
    url: '/jgjc-manager/api/console/jc/online/get/' + id,
    method: 'get'
  })
}

// 新增传感器在线状态
export function addOnline(data) {
  return request({
    url: '/jgjc-manager/api/console/jc/online/add',
    method: 'post',
    data: data
  })
}

// 修改传感器在线状态
export function updateOnline(data) {
  return request({
    url: '/jgjc-manager/api/console/jc/online/edit',
    method: 'put',
    data: data
  })
}

// 删除传感器在线状态
export function delOnline(id) {
  return request({
    url: '/jgjc-manager/api/console/jc/online/delete/' + id,
    method: 'delete'
  })
}
