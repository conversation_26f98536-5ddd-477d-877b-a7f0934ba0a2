import request from '@/utils/request'





// 修改涉路工程车道
export function updateEngineeringLane(data) {
  return request({
    url: '/repote/engineering/edit',
    method: 'put',
    data: data
  })
}

// 删除涉路工程车道
export function delEngineeringLane(id) {
  return request({
    url: '/repote/engineeringRoad/delete/' + id,
    method: 'delete'
  })
}

// 查询涉路工程车道
export function getEngineeringLane(id) {
  return request({
    url: '/repote/engineeringLane/get/' + id,
    method: 'get'
  })
}

// 新增涉路工程车道
export function addEngineeringLane(data) {
  return request({
    url: '/repote/engineeringLane/add',
    method: 'post',
    data: data
  })
}
