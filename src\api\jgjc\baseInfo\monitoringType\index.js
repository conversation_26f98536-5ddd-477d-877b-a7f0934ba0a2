import request from '@/utils/request';

// 监测内容相关接口
export function addMonitoringContent(data) {
  return request({
    url: '/jgjcadmin/api/basedata/monitoringcontent/save',
    method: 'post',
    data
  });
}

export function updateMonitoringContent(data) {
  return request({
    url: '/jgjcadmin/api/basedata/monitoringcontent/update',
    method: 'post',
    data
  });
}

export function deleteMonitoringContent(data) {
  return request({
    url: '/jgjcadmin/api/basedata/monitoringcontent/delete',
    method: 'post',
    data
  });
}

export function listMonitoringContents(data) {
  return request({
    url: '/jgjcadmin/api/basedata/monitoringcontent/page',
    method: 'post',
    data
  });
}

export function batchAddMonitoringContents(data) {
  return request({
    url: '/jgjcadmin/api/basedata/monitoringcontent/batchAdd',
    method: 'post',
    data
  });
}

// 监测类型相关接口
export function addMonitoringType(data) {
  return request({
    url: '/jgjcadmin/api/basedata/monitoringtype/save',
    method: 'post',
    data
  });
}

export function updateMonitoringType(data) {
  return request({
    url: '/jgjcadmin/api/basedata/monitoringtype/update',
    method: 'post',
    data
  });
}

export function deleteMonitoringType(data) {
  return request({
    url: '/jgjcadmin/api/basedata/monitoringtype/delete',
    method: 'post',
    data
  });
}

export function listMonitoringTypes(data) {
  return request({
    url: '/jgjcadmin/api/basedata/monitoringtype/page',
    method: 'post',
    data
  });
}

export function batchAddMonitoringTypes(data) {
  return request({
    url: '/jgjcadmin/api/basedata/monitoringtype/batchAdd',
    method: 'post',
    data
  });
}
