import request from '@/utils/request'

// 查询传感器值类型列表
export function listSensorValueType(query) {
  return request({
    url: '/jgjc-manager/api/console/jc/sensorValueType/findPage',
    method: 'post',
    data: query
  })
}

// 查询传感器值类型详细
export function getSensorValueType(id) {
  return request({
    url: '/jgjc-manager/api/console/jc/sensorValueType/findById?id=' + id,
    method: 'get'
  })
}

// 新增传感器值类型
export function addSensorValueType(data) {
  return request({
    url: '/jgjc-manager/api/console/jc/sensorValueType/create',
    method: 'post',
    data: data
  })
}

// 修改传感器值类型
export function updateSensorValueType(data) {
  return request({
    url: '/jgjc-manager/api/console/jc/sensorValueType/modify',
    method: 'post',
    data: data
  })
}

// 删除传感器值类型
export function delSensorValueType(id) {
  return request({
    url: '/jgjc-manager/api/console/jc/sensorValueType/removeById?id=' + id,
    method: 'delete'
  })
}
// 批量删除传感器值类型
export function delSensorValueTypeBatch(ids) {
  return request({
    url: '/jgjc-manager/api/console/jc/sensorValueType/batchRemove',
    method: 'post',
    data: ids
  })
}
//根据传感器id获取传感器值类型集合
export function getSensorValueTypeFindBySensorId(sensorId) {
  return request({
    url: '/jgjc-manager/api/console/jc/sensorValueType/findBySensorId?sensorId=' + sensorId,
    method: 'get'
  })
}
