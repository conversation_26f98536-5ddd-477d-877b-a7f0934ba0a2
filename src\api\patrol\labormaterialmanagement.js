import request from '@/utils/request'

// 查询物资管理列表
export function listLabormaterialmanagement(query) {
  return request({
    url: '/manager/labormaterialmanagement/listbyParam',
    method: 'post',
    data: query
  })
}

// 查询物资管理详细
export function getLabormaterialmanagement(id) {
  return request({
    url: '/manager/labormaterialmanagement/get/' + id,
    method: 'get'
  })
}

// 新增物资管理
export function addLabormaterialmanagement(data) {
  return request({
    url: '/manager/labormaterialmanagement/add',
    method: 'post',
    data: data
  })
}

// 修改物资管理
export function updateLabormaterialmanagement(data) {
  return request({
    url: '/manager/labormaterialmanagement/edit',
    method: 'put',
    data: data
  })
}

// 删除物资管理
export function delLabormaterialmanagement(id) {
  return request({
    url: '/manager/labormaterialmanagement/delete/' + id,
    method: 'delete'
  })
}
