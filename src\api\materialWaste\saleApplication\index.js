import request from '@/utils/request';
// 查询物资报废申请列表(分页)
export function querySellListPage(data) {
  return request({
    url: '/manager/sell/list',
    method: 'post',
    data,
  });
}

// 查询物资报废审核列表(分页)
export function queryPendingSellList(data) {
  return request({
    url: '/manager/sell/pending/list',
    method: 'post',
    data,
  });
}

// 查询物资报废查看列表(分页)
export function querySellViewList(data) {
  return request({
    url: '/manager/sell/view',
    method: 'post',
    data,
  });
}

// 查询物资报废申请列表(不分页)
export function queryAllSellList() {
  return request({
    url: '/manager/sell/listAll',
    method: 'get',
  });
}

// 根据id查询物资报废申请数据
export function getSellById(id) {
  return request({
    url: `/manager/sell/get/${id}`,
    method: 'get',
  });
}

// 新增物资报废申请
export function addSell(data) {
  return request({
    url: '/manager/sell/add',
    method: 'post',
    data,
  });
}

// 修改物资报废申请
export function updateSell(data) {
  return request({
    url: '/manager/sell/edit',
    method: 'put',
    data,
  });
}

// 删除物资报废申请
export function deleteSell(id) {
  return request({
    url: `/manager/sell/delete/${id}`,
    method: 'delete',
  });
}


// 物资报废申请单审核
export function processSell(data) {
  return request({
    url: '/manager/sell/process',
    method: 'post',
    data,
  });
}

// 物资报废申请单审核节点信息
export function getNodeInfo(data) {
  return request({
    url: '/manager/sell/node/info',
    method: 'post',
    data,
  });
}


// 物资报废申请单审核节点信息
export function visaPreview(data) {
  return request({
    url: '/manager/sell/preview',
    method: 'get',
    params: data,
  });
}
