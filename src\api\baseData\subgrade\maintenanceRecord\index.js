import request from '@/utils/request'

// 查询桥梁养护处治记录列表
export function listBridgeMaintenanceRecords(params) {
  return request({
    url: '/baseData/bridge/maintenanceRecords/list',
    method: 'get',
    params
  })
}

// 查询桥梁养护处治记录详细
export function getBridge(maintenanceRecordsId) {
  return request({
    url: '/baseData/manager/bridge/' + maintenanceRecordsId,
    method: 'get'
  })
}

// 新增桥梁养护处治记录
export function addBridge(data) {
  return request({
    url: '/baseData/manager/bridge',
    method: 'post',
    data: data
  })
}

// 修改桥梁养护处治记录
export function updateBridge(data) {
  return request({
    url: '/baseData/manager/bridge',
    method: 'put',
    data: data
  })
}

// 删除桥梁养护处治记录
export function delBridge(maintenanceRecordsId) {
  return request({
    url: '/workManager/manager/bridge/' + maintenanceRecordsId,
    method: 'delete'
  })
}
