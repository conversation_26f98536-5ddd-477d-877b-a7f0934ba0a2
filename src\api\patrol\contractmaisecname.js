import request from '@/utils/request'

// 查询合同关联的路段及路段编码列表
export function listContractmaisecname(query) {
  return request({
    url: '/patrol/contractmaisecname/list',
    method: 'get',
    params: query
  })
}

// 查询合同关联的路段及路段编码详细
export function getContractmaisecname(id) {
  return request({
    url: '/patrol/contractmaisecname/get/' + id,
    method: 'get'
  })
}

// 新增合同关联的路段及路段编码
export function addContractmaisecname(data) {
  return request({
    url: '/patrol/contractmaisecname/add',
    method: 'post',
    data: data
  })
}

// 修改合同关联的路段及路段编码
export function updateContractmaisecname(data) {
  return request({
    url: '/patrol/contractmaisecname/edit',
    method: 'put',
    data: data
  })
}

// 删除合同关联的路段及路段编码
export function delContractmaisecname(id) {
  return request({
    url: '/patrol/contractmaisecname/delete/' + id,
    method: 'delete'
  })
}
