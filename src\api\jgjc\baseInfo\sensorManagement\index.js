import request from '@/utils/request';

/**
 * 传感器管理API
 */

/**
 * 新增传感器
 * @param {Object} data 传感器数据
 * @returns {Promise} 请求Promise
 */
export function saveSensor(data) {
  return request({
    url: '/jgjcadmin/api/basedata/sensor-management/save',
    method: 'post',
    data
  });
}

/**
 * 批量新增传感器
 * @param {Array} data 传感器数据数组
 * @returns {Promise} 请求Promise
 */
export function batchSaveSensor(data) {
  return request({
    url: '/jgjcadmin/api/basedata/sensor-management/batch-save',
    method: 'post',
    data
  });
}

/**
 * 编辑传感器
 * @param {Object} data 传感器数据（需包含id）
 * @returns {Promise} 请求Promise
 */
export function updateSensor(data) {
  return request({
    url: '/jgjcadmin/api/basedata/sensor-management/update',
    method: 'post',
    data
  });
}

/**
 * 删除传感器
 * @param {Object} data 删除参数（通常包含id）
 * @returns {Promise} 请求Promise
 */
export function deleteSensor(data) {
  return request({
    url: '/jgjcadmin/api/basedata/sensor-management/delete',
    method: 'post',
    data
  });
}

/**
 * 传感器分页查询
 * @param {Object} data 查询参数
 * @returns {Promise} 请求Promise
 */
export function getSensorPage(data) {
  return request({
    url: '/jgjcadmin/api/basedata/sensor-management/page',
    method: 'post',
    data
  });
}

/**
 * 获取传感器树节点
 * @param {Object} [params] 查询参数
 * @returns {Promise} 请求Promise
 */
export function getSensorTreeNode(params) {
  return request({
    url: '/jgjcadmin/api/basedata/sensor-management/getSensorTreeNode',
    method: 'post',
    data: params || {}
  });
}

/**
 * 获取传感器列表
 * @param {Object} [params] 查询参数
 * @returns {Promise} 请求Promise
 */
export function getSensorList(params) {
  return request({
    url: '/jgjcadmin/api/basedata/sensor-management/list',
    method: 'post',
    data: params || {}
  });
}
