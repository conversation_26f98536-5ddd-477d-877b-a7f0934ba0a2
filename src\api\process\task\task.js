import request from '@/utils/request'

// 查询待办数据（分页）
export function queryPageTodoTask(query) {
  return request({
    url: '/workFlow/process/todo-tasks',
    method: 'post',
    data: query
  })
}

// 查询待办数据（分页）
export function queryPageTodoTaskList(query) {
  return request({
    url: '/workFlow/process/todo-task-list',
    method: 'post',
    data: query
  })
}

// 查询待办数据（分页）
export function queryPageTodoTaskGroup(query) {
  return request({
    url: '/workFlow/process/todo-tasks-group',
    method: 'post',
    data: query
  })
}

export function queryTodoTaskByBusinessKey(query) {
  return request({
    url: '/workFlow/process/todo-task/by-business-key',
    method: 'get',
    params: query
  })
}

export function processRevocation(query) {
  return request({
    url: '/workFlow/process/terminate-process-instance',
    method: 'get',
    params: query
  })
}

// 查询已办数据（分页）
export function queryPageCompletedTask(query) {
  return request({
    url: '/workFlow/process/completed-tasks',
    method: 'post',
    data: query
  })
}

// 查询流程流转意见
export function queryIdeaTask(query) {
  return request({
    url: '/workFlow/history/node-info',
    method: 'get',
    params: query
  })
}

// 查询流程图
export function queryBpmnTask(query) {
  return request({
    url: '/workFlow/history/searchApprovalBpmn',
    method: 'get',
    responseType: 'blob', // 重要：指定响应类型为 blob
    params: query
  })
}

// 提交流程（灾害）
export function submitTask(query) {
  return request({
    url: '/disaster/disaster-risk/process/tasks',
    method: 'post',
    data: query
  })
}

// 提交流程（高边坡）
export function submitTaskHighSlope(query) {
  return request({
    url: '/disaster/high-slope/process/tasks',
    method: 'post',
    data: query
  })
}

// 提交流程（灾毁上报）
export function submitTaskDamage(query) {
  return request({
    url: '/disaster/submitted/process/tasks',
    method: 'post',
    data: query
  })
}
