import request from '@/utils/request'

// 查询劳务人员持证情况列表
export function listLaborpersoncertificate(query) {
  return request({
    url: '/manager/laborpersoncertificate/list',
    method: 'get',
    params: query
  })
}

// 查询劳务人员持证情况列表
export function getListbyParam(query) {
  return request({
    url: '/manager/laborpersoncertificate/listbyParam',
    method: 'post',
    data: query
  })
}

// 查询劳务人员持证情况详细
export function getLaborpersoncertificate(id) {
  return request({
    url: '/manager/laborpersoncertificate/get/' + id,
    method: 'get'
  })
}

// 新增劳务人员持证情况
export function addLaborpersoncertificate(data) {
  return request({
    url: '/manager/laborpersoncertificate/add',
    method: 'post',
    data: data
  })
}

// 修改劳务人员持证情况
export function updateLaborpersoncertificate(data) {
  return request({
    url: '/manager/laborpersoncertificate/edit',
    method: 'put',
    data: data
  })
}

// 删除劳务人员持证情况
export function delLaborpersoncertificate(id) {
  return request({
    url: '/manager/laborpersoncertificate/delete/' + id,
    method: 'delete'
  })
}

// 删除劳务人员持证情况
export function delspersoncertificate(param) {
  return request({
    url: '/manager/laborpersoncertificate/deleteIds',
    method: 'post',
    data:param
  })
}
