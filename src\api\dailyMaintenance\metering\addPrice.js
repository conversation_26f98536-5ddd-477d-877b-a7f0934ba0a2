import request from '@/utils/request'

// 获取结算库-新增单价列表(分页)
export function fetchRepositoryList(repositoryDTO) {
  return request({
    url: '/manager/settlement/repository/list',
    method: 'get',
    params: repositoryDTO
  });
}

// 根据settleId获取结算库-新增单价下子目列表(不分页)
export function fetchMethodList(settleId, flag) {
  return request({
    url: `/manager/settlement/repository/list/method?settleId=${settleId}&flag=${flag}`,
    method: 'get'
  });
}

// 编辑结算库-新增单价下子目清单
export function updateMethodList(methodList) {
  return request({
    url: '/manager/settlement/repository/edit',
    method: 'put',
    data: methodList
  });
}

// 删除结算库-新增单价下子目清单
export function deleteMethodList(ids) {
  return request({
    url: `/manager/settlement/repository/delete/${ids}`,
    method: 'delete'
  });
}

// 结算库列表(分页)
export function otherRepositoryList(repositoryDTO) {
  return request({
    url: '/manager/settlement/repository/list/other',
    method: 'get',
    params: repositoryDTO
  });
}

// 编辑结算库数据(修改方法数量)
export function updateOtherMethodList(methodList) {
  return request({
    url: '/manager/settlement/repository/settle/edit',
    method: 'put',
    data: methodList
  });
}


// 签证单预览
export function previewVisa(id) {
  return request({
    url: `/manager/settlement/repository/visa/preview?settleId=${id}`,
    method: 'get'
  });
}


// 维修档案预览
export function preViewMaintenance(id) {
  return request({
    url: `/manager/settlement/repository/maintenance/file/preview?settleId=${id}`,
    method: 'get'
  });
}

// 编辑结算库数据(更改是否计量状态)
export function updateSettleCalcFlag(data) {
  return request({
    url: '/manager/settlement/repository/settle/calcFlag',
    method: 'put',
    data
  });
}


export function updateSafetyFee(data) {
  return request({
    url: `/manager/settlement/repository/update/safetyFee`,
    method: 'put',
    data
  });
}

// 施工档案下载
export function downloadConstruction(id) {
  return request({
    url: `/manager/settlement/repository/construction/file/download?settleId=${id}`,
    method: 'get'
  });
}



// 获取签证人员信息
export function getVisaCheck(params) {
  return request({
    url: `/manager/settlement/repository/get/visacheck`,
    method: 'get',
    params
  });
}

export function updateVisaCheck(data) {
  return request({
    url: `/manager/settlement/repository/update/visacheck`,
    method: 'put',
    data
  });
}


// 重新生成报表接口
export function regenerateReport(data) {
  return request({
    url: '/manager/settlement/repository/regenerate/report',
    method: 'post',
    data: data
  });
}

export function withdraw(data) {
  return request({
    url: '/manager/settlement/repository/withdraw',
    method: 'post',
    data: data
  })
}


// 编辑结算库-修改费用类型
export function updateCostType(data) {
  return request({
    url: '/manager/settlement/repository/update/costType',
    method: 'put',
    data: data
  });
}
