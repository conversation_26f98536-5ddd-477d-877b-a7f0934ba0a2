import request from '@/utils/request'

// 查询图层目录数据配置子分类列表
export function getListPage(params) {
  return request({
    url: '/oneMap/menuSubClassify/getListPage',
    method: 'get',
    params
  })
}

// 查询图层目录数据配置子分类详细
export function getMenuSubClassify(id) {
  return request({
    url: '/oneMap/menuSubClassify/getInfoById',
    method: 'get',
    params: { id },
  })
}

// 新增图层目录数据配置子分类
export function addMenuSubClassify(data) {
  return request({
    url: '/oneMap/menuSubClassify/add',
    method: 'post',
    data
  })
}

// 修改图层目录数据配置子分类
export function updateMenuSubClassify(data) {
  return request({
    url: '/oneMap/menuSubClassify/edit',
    method: 'post',
    data: data
  })
}

// 删除图层目录数据配置子分类
export function delMenuSubClassify(ids) {
  return request({
    url: '/oneMap/menuSubClassify/delete',
    method: 'delete',
    data: ids
  })
}

export function getMenuSubClassifyTree(params) {
  return request({
    url: '/oneMap/menuSubClassify/getListTree',
    method: 'get',
    params
  })
}
