import request from '@/utils/request'

// 查询涉灾隐患点—防洪排水设施列表
export function listDrainageFacilityHazards(query) {
  return request({
    url: '/middleData/drainageFacilityHazards/list',
    method: 'post',
    params: {pageNum: query.pageNum, pageSize: query.pageSize},
    data: query
  })
}

// 查询涉灾隐患点—防洪排水设施详细
export function getDrainageFacilityHazards(id) {
  return request({
    url: '/middleData/drainageFacilityHazards/get/' + id,
    method: 'get'
  })
}

// 新增涉灾隐患点—防洪排水设施
export function addDrainageFacilityHazards(data) {
  return request({
    url: '/middleData/drainageFacilityHazards/add',
    method: 'post',
    data: data
  })
}

// 修改涉灾隐患点—防洪排水设施
export function updateDrainageFacilityHazards(data) {
  return request({
    url: '/middleData/drainageFacilityHazards/edit',
    method: 'put',
    data: data
  })
}

// 删除
export function delData(data) {
  return request({
    url: `/middleData/drainageFacilityHazards/delete`,
    method: 'delete',
    data: data.idList,
  });
}

// 批量审核
export function batchAudit(data) {
  return request({
    url: '/middleData/drainageFacilityHazards/batchAudit',
    method: 'post',
    data: data
  })
}

