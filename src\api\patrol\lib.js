import request from '@/utils/request'

// 查询报价体系基本信息列表
export function listLib(query) {
  return request({
    url: '/patrol/lib/list',
    method: 'get',
    params: query
  })
}

// 查询报价体系基本信息详细
export function getLib(id) {
  return request({
    url: '/patrol/lib/get/' + id,
    method: 'get'
  })
}

// 新增报价体系基本信息
export function addLib(data) {
  return request({
    url: '/patrol/lib/add',
    method: 'post',
    data: data
  })
}

// 修改报价体系基本信息
export function updateLib(data) {
  return request({
    url: '/patrol/lib/edit',
    method: 'put',
    data: data
  })
}

// 删除报价体系基本信息
export function delLib(id) {
  return request({
    url: '/patrol/lib/delete/' + id,
    method: 'delete'
  })
}
