import request from '@/utils/request';

// 查询物资废料列表(分页)
export function queryWasteListPage(data) {
  return request({
    url: '/manager/waste/list',
    method: 'post',
    data,
  });
}

// 根据id查询物资废料数据
export function getWasteById(id) {
  return request({
    url: `/manager/waste/get/${id}`,
    method: 'get',
  });
}

// 新增物资废料
export function addWaste(data) {
  return request({
    url: '/manager/waste/add',
    method: 'post',
    data,
  });
}

// 修改物资废料
export function editWaste(data) {
  return request({
    url: '/manager/waste/edit',
    method: 'put',
    data,
  });
}

// 删除物资废料
export function deleteWaste(id) {
  return request({
    url: `/manager/waste/delete/${id}`,
    method: 'delete',
  });
}
