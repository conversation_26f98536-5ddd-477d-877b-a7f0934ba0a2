import request from '@/utils/request'

// 查询站点管理列表(分页)
export function querySiteListPage(params) {
  return request({
    url: '/manager/waste/site/list',
    method: 'post',
    data: params
  })
}

// 查询站点管理列表(不分页)
export function querySiteListAll() {
  return request({
    url: '/manager/waste/site/listAll',
    method: 'get'
  })
}

// 根据id查询站点管理数据
export function getSiteById(id) {
  return request({
    url: `/manager/waste/site/get/${id}`,
    method: 'get'
  })
}

// 新增站点管理
export function addSite(data) {
  return request({
    url: '/manager/waste/site/add',
    method: 'post',
    data: data
  })
}

// 修改站点管理
export function editSite(data) {
  return request({
    url: '/manager/waste/site/edit',
    method: 'put',
    data: data
  })
}

// 删除站点管理
export function deleteSite(id) {
  return request({
    url: `/manager/waste/site/delete/${id}`,
    method: 'delete'
  })
}

// 导出站点管理列表
export function exportSite(params) {
  return request({
    url: '/manager/waste/site/export',
    method: 'post',
    data: params,
    responseType: 'blob'
  })
}
