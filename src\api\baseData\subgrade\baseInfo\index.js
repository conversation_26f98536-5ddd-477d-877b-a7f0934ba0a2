import request from "@/utils/request";

// 条件分页查询路基数据列表
export function getListPage(data) {
  return request({
    url: "/baseData/roadbed/getListPage",
    method: "post",
    data,
  });
}

/**
 * 新增路基数据
 * @param {*} data
 * @returns
 */
export function subgradeAdd(data) {
  return request({
    url: "/baseData/roadbed/add",
    method: "post",
    data,
  });
}

/**
 * 获取详情
 * @param {*} id
 * @returns
 */
export function getSubgradeById(id) {
  return request({
    url: `/baseData/roadbed/get/${id}`,
    method: "get",
  });
}

/**
 * 修改路基数据
 * @param {*} data
 * @returns
 */
export function subgradeEdit(data) {
  return request({
    url: `/baseData/roadbed/edit`,
    method: "put",
    data
  });
}

/**
 * 删除路基数据（逻辑删除）
 * @param {*} ids
 * @returns
 */
export function subgradeDelete(ids) {
  return request({
    url: `/baseData/roadbed/delete/${ids}`,
    method: "delete",
  });
}

/**
 * 暂存路基宽度数据
 * @param {*} data
 * @returns
 */
export function subgradeTempAdd(data) {
  return request({
    url: `/baseData/roadbed/tempAdd`,
    method: "post",
    data
  });
}
