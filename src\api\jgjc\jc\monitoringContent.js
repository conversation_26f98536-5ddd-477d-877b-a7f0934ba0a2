import request from '@/utils/request'

// 查询监测内容列表
export function listMonitoringContent(query) {
  return request({
    url: '/jgjc-manager/api/console/jc/monitoringContent/findPage',
    method: 'post',
    data: query
  })
}

// 查询监测内容详细
export function getMonitoringContent(id) {
  return request({
    url: '/jgjc-manager/api/console/jc/monitoringContent/findById?id=' + id,
    method: 'get'
  })
}

// 新增监测内容
export function addMonitoringContent(data) {
  return request({
    url: '/jgjc-manager/api/console/jc/monitoringContent/create',
    method: 'post',
    data: data
  })
}

// 修改监测内容
export function updateMonitoringContent(data) {
  return request({
    url: '/jgjc-manager/api/console/jc/monitoringContent/modify',
    method: 'post',
    data: data
  })
}

// 删除监测内容
export function delMonitoringContent(id) {
  return request({
    url: '/jgjc-manager/api/console/jc/monitoringContent/removeById?id=' + id,
    method: 'delete'
  })
}
// 批量删除监测内容
export function delMonitoringContentBatch(ids) {
  return request({
    url: '/jgjc-manager/api/console/jc/monitoringContent/batchRemove',
    method: 'post',
    data: ids
  })
}
// 查询监测内容树型数据
export function getMonitoringContentFindTree() {
  return request({
    url: '/jgjc-manager/api/console/jc/monitoringContent/findTree',
    method: 'get'
  })
}
