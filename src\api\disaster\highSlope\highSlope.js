import request from '@/utils/request'

// 查询高边坡主表数据（分页）
export function queryPageHighSlope(query) {
  return request({
    url: '/disaster/high-slope/list',
    method: 'get',
    params: query
  })
}

// 获取公路高边坡信息调查详细信息
export function queryHighSlopeForm(id) {
  return request({
    url: `/disaster/high-slope/${id}`,
    method: 'get',
  })
}

// 新增高边坡数据
export function createHighSlope(query) {
  return request({
    url: '/disaster/high-slope/add',
    method: 'post',
    data: query
  })
}

// 更新高边坡数据
export function updateHighSlope(query) {
  return request({
    url: '/disaster/high-slope/edit',
    method: 'put',
    data: query
  })
}


// 删除高边坡数据
export function deleteHighSlope(id) {
  return request({
    url: `/disaster/high-slope/${id}`,
    method: 'delete',
  })
}

export function dataSync() {
  return request({
    url: `/disaster/high-slope/update/info`,
    method: 'put',
  })
}

export function revokeProcess(params) {
  return request({
    url: `/disaster/disaster-process/revoke`,
    method: 'post',
    params:params
  })
}

// 批量提交高边坡数据
export function batchStartProcess(id) {
  return request({
    url: `/disaster/high-slope/process/start/${id}`,
    method: 'post',
  })
}
