import request from '@/utils/request'

// 查询养护路段管理列表
export function listMaintenanceSection(query) {
  return request({
    url: '/system/maintenanceSection/list',
    method: 'get',
    params: query
  })
}

// 查询总养护里程
export function getTotalMileage(query) {
  return request({
    url: '/system/maintenanceSection/getTotalMileage',
    method: 'get',
    params: query
  })
}

// 查询养护路段管理列表
export function queryDeptMaintenanceList(query) {
  return request({
    url: '/system/maintenanceSection/queryDeptMaintenanceList',
    method: 'get',
    params: query
  })
}

// 查询养护路段管理列表（不分页）  //2024/10/12修改
export function listMaintenanceSectionAll(query) {
  return request({
    url: '/system/maintenanceSection/listAll',
    method: 'get',
    params: query
  })
}

// 查询养护路段管理详细
export function getMaintenanceSection(maintenanceSectionId) {
  return request({
    url: '/system/maintenanceSection/' + maintenanceSectionId,
    method: 'get'
  })
}

// 新增养护路段管理
export function addMaintenanceSection(data) {
  return request({
    url: '/system/maintenanceSection',
    method: 'post',
    data: data
  })
}

// 修改养护路段管理
export function updateMaintenanceSection(data) {
  return request({
    url: '/system/maintenanceSection',
    method: 'put',
    data: data
  })
}

// 删除养护路段管理
export function delMaintenanceSection(maintenanceSectionId) {
  return request({
    url: '/system/maintenanceSection/' + maintenanceSectionId,
    method: 'delete'
  })
}

// 获取当前账户权限下的所有管理处以及管理处下的路段信息
export function findUserDeptMaintenanceList(data) {
  return request({
    url: '/system/maintenanceSection/findUserDeptMaintenanceList',
    method: 'post',
    data: data
  })
}

// 获取当前账户权限下的所有管理处以及管理处下的路段信息
export function findUserDeptMaintenanceList2(data) {
  return request({
    url: '/system/maintenanceSection/findUserDeptMaintenanceList2',
    method: 'post',
    data: data
  })
}

export function syncMileage(query) {
  return request({
    url: '/system/maintenanceSection/syncMileage',
    method: 'get',
    params: query
  })
}
