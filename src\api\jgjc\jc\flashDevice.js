import request from '@/utils/request'

// 查询爆闪设备列表
export function listFlashDevice(query) {
  return request({
    url: '/jgjc-manager/api/console/jc/flashDevice/findPage',
    method: 'post',
    data: query
  })
}

// 查询爆闪设备详细
export function getFlashDevice(id) {
  return request({
    url: '/jgjc-manager/api/console/jc/flashDevice/findById?id=' + id,
    method: 'get'
  })
}

// 新增爆闪设备
export function addFlashDevice(data) {
  return request({
    url: '/jgjc-manager/api/console/jc/flashDevice/create',
    method: 'post',
    data: data
  })
}

// 修改爆闪设备
export function updateFlashDevice(data) {
  return request({
    url: '/jgjc-manager/api/console/jc/flashDevice/modify',
    method: 'post',
    data: data
  })
}

// 删除爆闪设备
export function delFlashDevice(id) {
  return request({
    url: '/jgjc-manager/api/console/jc/flashDevice/removeById?id=' + id,
    method: 'delete'
  })
}
// 批量删除
export function delDeviceTypeBatch(ids) {
  return request({
    url: '/jgjc-manager/api/console/jc/flashDevice/batchRemove',
    method: 'post',
    data: ids
  })
}
