import request from '@/utils/request'

// 查询隧道机电日常巡查列表
export function listDeviceCheck(query) {
  return request({
    url: '/patrol/deviceCheck/list',
    method: 'get',
    params: query
  })
}

// 查询资产寻检查主列表
export function getAssetTotalCount(query) {
  return request({
    url: `/patrol/deviceCheck/getAssetTotalCount`,
    method: 'get',
    params: query,
  });
}

// 故障月报查询
export function listMonthlyReport(query) {
  return request({
    url: '/patrol/deviceCheck/listMonthlyReport',
    method: 'get',
    params: query
  })
}

// 查询隧道机电日常巡查详细
export function getDeviceCheck(id) {
  return request({
    url: '/patrol/deviceCheck/get/' + id,
    method: 'get'
  })
}

// 新增隧道机电日常巡查
export function addDeviceCheck(data) {
  return request({
    url: '/patrol/deviceCheck/add',
    method: 'post',
    data: data
  })
}

// 批量新增
export function addAllDeviceCheck(data) {
  return request({
    url: '/patrol/deviceCheck/addAll',
    method: 'post',
    data: data
  })
}

// 修改隧道机电日常巡查
export function updateDeviceCheck(data) {
  return request({
    url: '/patrol/deviceCheck/edit',
    method: 'put',
    data: data
  })
}

// 删除隧道机电日常巡查
export function delDeviceCheck(data) {
  return request({
    url: `/patrol/deviceCheck/delete`,
    method: 'delete',
    data: data.idList,
  });
}
