import request from '@/utils/request'

// 查询爆闪预警记录列表
export function listFlashWarningRecord(query) {
  return request({
    url: '/jgjc-manager/api/console/jc/flashWarningRecord/findPage',
    method: 'post',
    data: query
  })
}

// 查询爆闪预警记录详细
export function getFlashWarningRecord(id) {
  return request({
    url: '/jgjc-manager/api/console/jc/flashWarningRecord/findById?id=' + id,
    method: 'get'
  })
}

// 新增爆闪预警记录
export function addFlashWarningRecord(data) {
  return request({
    url: '/jgjc-manager/api/console/jc/flashWarningRecord/create',
    method: 'post',
    data: data
  })
}

// 修改爆闪预警记录
export function updateFlashWarningRecord(data) {
  return request({
    url: '/jgjc-manager/api/console/jc/flashWarningRecord/modify',
    method: 'post',
    data: data
  })
}

// 删除爆闪预警记录
export function delFlashWarningRecord(id) {
  return request({
    url: '/jgjc-manager/api/console/jc/flashWarningRecord/removeById?id=' + id,
    method: 'delete'
  })
}
// 批量删除
export function delDeviceTypeBatch(ids) {
  return request({
    url: '/jgjc-manager/api/console/jc/flashWarningRecord/batchRemove',
    method: 'post',
    data: ids
  })
}
