import request from '@/utils/request'

// 查询巡查项配置列表
export function listXqItemsetting(query) {
  return request({
    url: '/middleData/xqItemsetting/list',
    method: 'get',
    params: query
  })
}

export function listAllXqItemsetting(query) {
  return request({
    url: '/middleData/xqItemsetting/listAll',
    method: 'get',
    params: query
  })
}

// 查询巡查项配置详细
export function getXqItemsetting(id) {
  return request({
    url: '/middleData/xqItemsetting/get/' + id,
    method: 'get'
  })
}

// 新增巡查项配置
export function addXqItemsetting(data) {
  return request({
    url: '/middleData/xqItemsetting/add',
    method: 'post',
    data: data
  })
}

// 修改巡查项配置
export function updateXqItemsetting(data) {
  return request({
    url: '/middleData/xqItemsetting/edit',
    method: 'put',
    data: data
  })
}

// 删除巡查项配置
export function delXqItemsetting(id) {
  return request({
    url: '/middleData/xqItemsetting/delete/' + id,
    method: 'delete'
  })
}
