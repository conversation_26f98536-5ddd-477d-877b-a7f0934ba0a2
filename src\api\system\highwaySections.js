import request from '@/utils/request'

// 查询公路路段管理列表
export function listHighwaySections(query) {
  return request({
    url: '/system/highwaySections/list',
    method: 'get',
    params: query
  })
}

// 查询公路路段管理列表
export function listAllHighwaySections(query) {
  return request({
    url: '/system/highwaySections/listAll',
    method: 'get',
    params: query
  })
}
// 查询公路路段管理详细
export function getHighwaySections(roadSectionId) {
  return request({
    url: '/system/highwaySections/' + roadSectionId,
    method: 'get'
  })
}
// 查询总养护里程
export function getTotalMileage(query) {
  return request({
    url: '/system/highwaySections/getTotalMileage',
    method: 'get',
    params: query
  })
}

// 新增公路路段管理
export function addHighwaySections(data) {
  return request({
    url: '/system/highwaySections',
    method: 'post',
    data: data
  })
}

// 修改公路路段管理
export function updateHighwaySections(data) {
  return request({
    url: '/system/highwaySections',
    method: 'put',
    data: data
  })
}

// 删除公路路段管理
export function delHighwaySections(roadSectionId) {
  return request({
    url: '/system/highwaySections/' + roadSectionId,
    method: 'delete'
  })
}
