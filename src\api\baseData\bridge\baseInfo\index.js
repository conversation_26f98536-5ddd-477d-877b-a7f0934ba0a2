import request from '@/utils/request'

// 查询桥梁静态数据列表
export function listStatic(data) {
  return request({
    url: '/baseData/bridge/getListPage',
    method: 'post',
    data: data
  })
}

// 查询桥梁静态数据详细
export function getStatic(id) {
  return request({
    url: '/baseData/bridge/get/' + id,
    method: 'get'
  })
}

// 新增桥梁静态数据
export function addStatic(data) {
  return request({
    url: '/baseData/bridge/add',
    method: 'post',
    data: data
  })
}

// 暂存桥梁静态数据
export function tempStatic(data) {
  return request({
    url: '/baseData/bridge/temp/add',
    method: 'post',
    data: data
  })
}

// 修改桥梁静态数据
export function updateStatic(data) {
  return request({
    url: '/baseData/bridge/edit',
    method: 'put',
    data: data
  })
}

// 删除桥梁静态数据
export function delStatic(data) {
  return request({
    url: '/baseData/bridge/delete',
    method: 'delete',
    data
  })
}

// 是否锁定
export function changeLockedStatus(data) {
  return request({
    url: '/baseData/bridge/updateLocked',
    method: 'post',
    data
  })
}

// 运营状态管理变更-添加
export function businessAdd(data) {
  return request({
    url: '/baseData/bridge/business/add',
    method: 'post',
    data
  })
}

// 运营状态管理变更-获取列表
export function businessGetList(data) {
  return request({
    url: '/baseData/bridge/business/getListPage',
    method: 'post',
    data: data
  })
}

// 导入新增
export function importData(formData, isUpdate = true) {
  return request({
    url: `/baseData/bridge/importData?isUpdate=${isUpdate}`,
    method: 'post',
    data: formData
  })
}

//构件数据
export function getMemberPosition(data) {
  return request({
    url: '/baseData/bridge/memberPosition/getList/'+data,
    method: 'get',
  })
}

//部件数据
export function getMemberComponent(data) {
  return request({
    url: '/baseData/bridge/memberComponent/getListPage',
    method: 'post',
    data: data
  })
}

//部件删除
export function delMember(data) {
  return request({
    url: '/baseData/bridge/memberComponent/delete',
    method: 'delete',
    data
  })
}

//审核开始
export function audit(data) {
  return request({
    url: '/baseData/audit/startBaseDataProcess',
    method: 'post',
    data
  })
}

// 桥梁卡片
export function getCard(data) {
  return request({
    url: '/baseData/bridge/getCard',
    method: 'post',
    data
  })
}

// 桥梁日常养护
export function getgetBridgeAssetDiseaseInfoCard(data) {
  return request({
    url: '/manager/disease/getBridgeAssetDiseaseInfo',
    method: 'post',
    data
  })
}

//养护工程
//disease/getBridgeAssetDiseaseProjInfo
export function getBridgeAssetDiseaseProjInfo(data) {
  return request({
    url: '/manager/disease/getBridgeAssetDiseaseProjInfo',
    method: 'post',
    data
  })
}
