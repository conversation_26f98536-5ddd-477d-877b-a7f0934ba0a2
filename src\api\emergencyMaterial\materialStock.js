import request from '@/utils/request'

// 应急物资盘点节点信息
export function getNodeInfo(data) {
  return request({
    url: '/manager/material/stocktaking/node/info',
    method: 'post',
    data
  })
}

// 获取出库申请单列表
export function getMaterialStockList(data) {
  return request({
    url: '/manager/material/stocktaking/list',
    method: 'post',
    data
  })
}

// 新增物资盘点
export function addStock(data) {
  return request({
    url: '/manager/material/stocktaking/add',
    method: 'post',
    data
  })
}

// 根据id查询应急物资出库单数据
export function getDetailStockList(id) {
  return request({
    url: `/manager/material/stocktaking/get/${id}`,
    method: 'get'
  })
}

// 编辑物资盘点
export function editStock(data) {
  return request({
    url: '/manager/material/stocktaking/edit',
    method: 'put',
    data
  })
}

// 删除出库单
export function delMaterialStock(id) {
  return request({
    url: `/manager/material/stocktaking/delete/${id}`,
    method: 'delete',
  })
}

// 根据出库单id与物资库id查询应急盘点单明细列表
export function getStockDetailList(data) {
  return request({
    url: '/manager/material/stocktaking/detail/list',
    method: 'post',
    data
  })
}

// 应急物资出库单审核
export function submitStock(data) {
  return request({
    url: '/manager/material/stocktaking/process',
    method: 'post',
    data
  })
}


// 查询应急物资出库单结果列表
export function getStockResultList(data) {
  return request({
    url: '/manager/material/stocktaking/view',
    method: 'post',
    data
  })
}


// 报表
export function previewReport(id) {
  return request({
    url: `/manager/material/stocktaking/report/preview?id=${id}`,
    method: 'get',
  })
}
