import request from '@/utils/request'

// 查询预算版本配置列表(分页)
export function GetTypeconfigList(data) {
    return request({
        url: '/manager/typeconfig/list',
        method: 'post',
        data: data
    });
}

// 查询预算版本配置列表(分页)
export function AddTypeconfigList(data) {
    return request({
        url: '/manager/typeconfig/add',
        method: 'post',
        data: data
    });
}

// 修改年度配置
export function EditTypeconfigList(data) {
    return request({
        url: '/manager/typeconfig/edit',
        method: 'put',
        data: data
    });
}
// 删除年度配置
export function DeleteTypeconfig(id) {
    return request({
        url: `/manager/typeconfig/delete/${id}`,
        method: 'delete'
    });
}


// 删除年度配置
export function getCostType(type) {
    return request({
        url: `/manager/typeconfig/costtype/${type}`,
        method: 'get'
    });
}


// 删除年度配置
export function getCostType2(type) {
    return request({
        url: `/manager/typeconfig/costtypebyparent/${type}`,
        method: 'get'
    });
}
