import request from '@/utils/request'


// 查询上传附件
export function listTransportationAttachment(query) {
  return request({
    url: '/repote/transportationAttachment/list',
    method: 'get',
    params: query
  })
}

// 修改上传附件
export function updateTransportationAttachment(data) {
  return request({
    url: '/repote/transportationAttachment/edit',
    method: 'put',
    data: data
  })
}


// 新增上传附件
export function addTransportationAttachment(data) {
  return request({
    url: '/repote/transportationAttachment/add',
    method: 'post',
    data: data
  })
}

// 删除上传附件
export function deleteTransportationAttachment(id) {
  return request({
    url: '/repote/transportationAttachment/delete/' + id,
    method: 'delete'
  })
}


// 查询大件运输附件详细
export function getTransportationAttachment(id) {
  return request({
    url: '/repote/transportationAttachment/get/' + id,
    method: 'get'
  })
}


