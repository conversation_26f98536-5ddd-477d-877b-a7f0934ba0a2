import request from '@/utils/request'

// 查询爆闪预案执行列表
export function listFlashGenericPlan(query) {
  return request({
    url: '/jgjc-manager/api/console/jc/flashGenericPlan/findPage',
    method: 'post',
    data: query
  })
}

// 查询爆闪预案执行详细
export function getFlashGenericPlan(id) {
  return request({
    url: '/jgjc-manager/api/console/jc/flashGenericPlan/findById?id=' + id,
    method: 'get'
  })
}

// 新增爆闪预案执行
export function addFlashGenericPlan(data) {
  return request({
    url: '/jgjc-manager/api/console/jc/flashGenericPlan/create',
    method: 'post',
    data: data
  })
}

// 修改爆闪预案执行
export function updateFlashGenericPlan(data) {
  return request({
    url: '/jgjc-manager/api/console/jc/flashGenericPlan/modify',
    method: 'post',
    data: data
  })
}

// 删除爆闪预案执行
export function delFlashGenericPlan(id) {
  return request({
    url: '/jgjc-manager/api/console/jc/flashGenericPlan/removeById?id=' + id,
    method: 'delete'
  })
}
// 批量删除
export function delDeviceTypeBatch(ids) {
  return request({
    url: '/jgjc-manager/api/console/jc/flashGenericPlan/batchRemove',
    method: 'post',
    data: ids
  })
}
