import request from '@/utils/request'
import {parseStrEmpty} from "@/utils/ruoyi";

// 查询填报任务列表
export function listMission(query) {
  return request({
    url: '/repote/repoteMission/list',
    method: 'get',
    params: query
  })
}

// 查询填报任务详细
export function getMission(id) {
  return request({
    url: '/repote/repoteMission/get/' + id,
    method: 'get'
  })
}

// 新增填报任务
export function addMission(data) {
  return request({
    url: '/repote/repoteMission/add',
    method: 'post',
    data: data
  })
}

// 修改填报任务
export function updateMission(data) {
  return request({
    url: '/repote/repoteMission/edit',
    method: 'put',
    data: data
  })
}

// 删除填报任务
export function delMission(id) {
  return request({
    url: '/repote/repoteMission/delete/' + id,
    method: 'delete'
  })
}

// 新增人员至人员表单
export function addPerson(data) {
  return request({
    url: '/repote/person/delete/',
    method: 'post',
    data: data
  })
}







