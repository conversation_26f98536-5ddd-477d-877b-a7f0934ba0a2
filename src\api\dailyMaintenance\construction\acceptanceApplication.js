import request from '@/utils/request'

// 结构树
export function getUserTreeStructure(data) {
  return request({
    url: '/manager/checkApply/getDaliyTreeStructure',
    method: 'post',
    data: data
  });
}
// 查询验收申请列表(分页)
export function queryCheckListPage(data) {
  return request({
    url: '/manager/checkApply/list',
    method: 'post',
    data,
  });
}

// 查询验收申请列表(不分页)
export function queryCheckListAll() {
  return request({
    url: '/manager/checkApply/listAll',
    method: 'get',
  });
}

// 根据id查询验收申请数据
export function getCheckById(id) {
  return request({
    url: `/manager/checkApply/get/${id}`,
    method: 'get',
  });
}

// 修改验收申请
export function updateCheck(data) {
  return request({
    url: '/manager/checkApply/edit',
    method: 'put',
    data,
  });
}
// 根据id查询完工登记数据
export function getDetil(id) {
  return request({
    url: `/manager/checkApply/get/${id}`,
    method: 'get'
  });
}

// 保存验收
export function save(data) {
  return request({
    url: '/manager/checkApply/save',
    method: 'put',
    data,
  });
}

// 提交验收
export function submitReview(data) {
  return request({
    url: '/manager/checkApply/review',
    method: 'put',
    data,
  });
}

// 撤回到待施工
export function rejectToRegister(data) {
  return request({
    url: '/manager/checkApply/rejectToRegister',
    method: 'put',
    data,
  });
}

// 导出验收申请列表
export function exportCheckList() {
  return request({
    url: '/manager/checkApply/export',
    method: 'post',
  });
}


// 查询施工单列表
export function pageList(data) {
  return request({
    url: '/manager/checkApply/pageList',
    method: 'post',
    data
  });
}


// 签证单预览
export function visaPreview(id) {
  return request({
    url: `/manager/checkApply/visa/preview?disId=${id}`,
    method: 'get'
  });
}

// 维修档案预览
export function maintenanceFilePreview(id) {
  return request({
    url: `/manager/checkApply/maintain/preview?disId=${id}`,
    method: 'get'
  });
}


// 施工档案下载
export function archivesDownload(id) {
  return request({
    url: `/manager/checkApply/file/download?disId=${id}`,
    method: 'get'
  });
}
