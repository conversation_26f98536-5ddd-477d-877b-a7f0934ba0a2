import request from '@/utils/request'

// 结构物查询
export function listStructure(query) {
  return request({
    url: '/jgjcadmin/api/flash/structure/list',
    method: 'post',
    data: query,
    notVerifyDuplicates: true,

  })
}

// 开启预案
export function startPlan(query) {
  return request({
    url: '/jgjcadmin/api/flash/flash-generic-plan/startPlan',
    method: 'post',
    data: query
  })
}

// 结束预案
export function endPlan(query) {
  return request({
    url: '/jgjcadmin/api/flash/flash-generic-plan/endPlan',
    method: 'post',
    data: query
  })
}

// 预警记录
export function warningRecordPage(query) {
  return request({
    url: '/jgjcadmin/api/flash/flashWarningRecord/warningRecordPage',
    method: 'get',
    params: query,
    notVerifyDuplicates: true,
  })
}

// 预警记录2
export function warningRecordPage2(query) {
  return request({
    url: '/jgjcadmin/api/healthassessment/alertrecord/page2',
    method: 'post',
    data: query,
    notVerifyDuplicates: true,
  })
}


// 删除
export function deleteAlertrecord(query) {
  return request({
    url: '/jgjcadmin/api/healthassessment/alertrecord/delete',
    method: 'post',
    data: query
  })
}

// 预警记录查看所有
export function sensorWarningRecordPage(query) {
  return request({
    url: '/jgjcadmin/api/flash/flashWarningRecord/sensorWarningRecordPage',
    method: 'get',
    params: query,
    notVerifyDuplicates: true,
  })
}

// 处理
export function processFlash(query) {
  return request({
    url: '/jgjcadmin/api/flash/flash-generic-plan/process',
    method: 'post',
    data: query
  })
}

// 统计
export function statistics(query) {
  return request({
    url: '/jgjcadmin/api/flash/flash-generic-plan/statistics',
    method: 'post',
    data: query,
    notVerifyDuplicates: true,
  })
}

// 分页查询
export function listFlashGenericPlan(query) {
  return request({
    url: '/jgjcadmin/api/flash/flash-generic-plan/page',
    method: 'get',
    params: query,
    notVerifyDuplicates: true,
  })
}


// 删除
export function deleteFlashGenericPlan(query) {
  return request({
    url: '/jgjcadmin/api/flash/flash-generic-plan/delete',
    method: 'post',
    data: query
  })
}


// 查询视频列表
export function getVideoList(query) {
  return request({
    url: '/jgjcadmin/api/basedata/structurevideo/list',
    method: 'post',
    data: query
  })
}

// 获取中交建工设备状态
export function getZJGTStructDeviceStatus(query) {
  return request({
    url: '/jgjcadmin/api/flash/flash-generic-plan/getZJGTStructDeviceStatus',
    method: 'post',
    data: query
  })
}

// 获取三思设备状态
export function getSansiStructDeviceStatus(query) {
  return request({
    url: '/jgjcadmin/api/flash/flash-generic-plan/getSansiStructDeviceStatus',
    method: 'post',
    data: query
  })
}

// 获取WDM设备状态
export function getWdmStructDeviceStatus(query) {
  return request({
    url: '/jgjcadmin/api/flash/flash-generic-plan/getWdmStructDeviceStatus',
    method: 'post',
    data: query
  })
}



// 获取WDM设备状态
export function getThirdPartyDeviceStatus(query) {
  return request({
    url: '/jgjcadmin/api/flash/flash-generic-plan/getThirdPartyDeviceStatus',
    method: 'post',
    data: query
  })
}
