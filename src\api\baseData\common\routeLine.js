import request from '@/utils/request'

/**
 * 根据养护路段id获取路线数据（全量）
 * {maintenanceSectionId}
 */
export function listByMaintenanceSectionId(params) {
  return request({
    url: '/system/route/listByMaintenanceSectionId',
    method: 'get',
    params
  })
}

/**
 * 获取养护路段数据（全量）
 * @param {*} data  {departmentIdList	管理处idList}
 * @returns
 */
export function getMaintenanceSectionListAll(params) {
  return request({
    url: '/system/maintenanceSection/listAll',
    method: 'get',
    params,
  });
}

/**
 * 根据路由ID获取路由信息
 * 
 * 此函数向服务器发送一个GET请求，以获取特定路由的信息
 * 它主要用于需要根据路由ID检索路由详细信息的场景
 * 
 * @param {string} routeId - 要查询的路由的唯一标识符
 * @returns {Promise} 返回一个Promise对象，它在请求成功时解析为包含路由信息的对象，在请求失败时解析为错误对象
 */
export function getRouteInfo(routeId) {
  return request({
    url: `/oneMap/route/get/${routeId}`,
    method: 'get',
  })
}
