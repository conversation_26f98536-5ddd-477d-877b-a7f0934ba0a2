import request from '@/utils/request'

// 查询填报人员列表
export function listRepotePerson(query) {
  return request({
    url: '/repote/repotePerson/list',
    method: 'get',
    params: query
  })
}

// 查询填报人员详细
export function getRepotePerson(id) {
  return request({
    url: '/repote/repotePerson/get/' + id,
    method: 'get'
  })
}

// 新增填报人员
export function addRepotePerson(data) {
  return request({
    url: '/repote/repotePerson/add',
    method: 'post',
    data: data
  })
}

// 修改填报人员
export function updateRepotePerson(data) {
  return request({
    url: '/repote/repotePerson/edit',
    method: 'put',
    data: data
  })
}

// 删除填报人员
export function delRepotePerson(id) {
  return request({
    url: '/repote/repotePerson/delete/' + id,
    method: 'delete'
  })
}
