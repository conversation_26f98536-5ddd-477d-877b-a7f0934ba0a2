import request from '@/utils/request'

// 查询检查项排除列表
export function listPartsIgnore(query) {
  return request({
    url: '/patrol/partsIgnore/list',
    method: 'get',
    params: query
  })
}

// 查询检查项排除列表
export function listAllPartsIgnore(query) {
  return request({
    url: '/patrol/partsIgnore/listAll',
    method: 'get',
    params: query
  })
}

// 查询检查项排除详细
export function getPartsIgnore(id) {
  return request({
    url: '/patrol/partsIgnore/get/' + id,
    method: 'get'
  })
}

// 新增检查项排除
export function addPartsIgnore(data) {
  return request({
    url: '/patrol/partsIgnore/add',
    method: 'post',
    data: data
  })
}


// 批量新增检查项排除
export function addBatchPartsIgnore(data) {
  return request({
    url: '/patrol/partsIgnore/addBatch',
    method: 'post',
    data: data
  })
}

// 修改检查项排除
export function updatePartsIgnore(data) {
  return request({
    url: '/patrol/partsIgnore/edit',
    method: 'put',
    data: data
  })
}

// 删除检查项排除
export function delPartsIgnore(id) {
  return request({
    url: '/patrol/partsIgnore/delete/' + id,
    method: 'delete'
  })
}
// 根据条件删除检查项排除
export function delPartsIgnoreByCondition(data) {
  return request({
    url: '/patrol/partsIgnore/removeByCondition',
    method: 'delete',
    data: data
  })
}
