import request from '@/utils/request'

export function upload(params) {
  return request({
    url: '/ruoyi-file/upload',
    method: 'post',
    params: params
  })
}

export function findFiles(ownerId) {
  return request({
    url: '/ruoyi-file/findFiles',
    method: 'get',
    params: { ownerId }
  })
}


export function removeFile(fileId) {
  return request({
    url: '/ruoyi-file/removeFile?fileId=' + fileId,
    method: 'get',
  })
}


export function addUploadRecord(entity) {
  return request({
    url: '/system/SysUpload/saveUpload',
    method: 'post',
    data: entity
  })
}

export function removeUploadRecord(entity) {
  return request({
    url: '/system/SysUpload/deleteUpload',
    method: 'post',
    data: entity
  })
}

export function getListByEntity(entity) {
  return request({
    url: '/system/SysUpload/getListByEntity',
    method: 'post',
    data: entity
  })
}
