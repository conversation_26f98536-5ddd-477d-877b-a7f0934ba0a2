import request from '@/utils/request'

// 查询灾毁数据
export function queryPageDamage(query) {
  return request({
    url: '/disaster/submitted/list',
    method: 'get',
    params: query
  })
}

// 查询灾毁数据（ID）
export function queryDamageById(id) {
  return request({
    url: `/disaster/submitted/${id}`,
    method: 'get',
  })
}

// 新增灾毁数据
export function createDamage(query) {
  return request({
    url: '/disaster/submitted',
    method: 'post',
    data: query
  })
}

// 更新灾毁数据
export function updateDamage(query) {
  return request({
    url: '/disaster/submitted',
    method: 'put',
    data: query
  })
}

// 删除灾毁数据
export function deleteDamage(id) {
  return request({
    url: `/disaster/submitted/${id}`,
    method: 'delete',
  })
}

// 查询关联风险点数据
export function queryPageRisk(query) {
  return request({
    url: '/disaster/submitted/getDisasterRiskList',
    method: 'get',
    params: query
  })
}

// 查询关联风险点数据（ID）
export function queryPageRiskById(id) {
  return request({
    url: `/disaster/disaster-risk/getDataByBusinessId/${id}`,
    method: 'get',
  })
}

export function revokeProcess(params) {
  return request({
    url: `/disaster/disaster-process/revoke`,
    method: 'post',
    params:params
  })
}