import request from '@/utils/request';
// 查询物资报废申请列表(分页)
export function queryScrapListPage(data) {
  return request({
    url: '/manager/scrap/list',
    method: 'post',
    data,
  });
}

// 查询物资报废审核列表(分页)
export function queryPendingScrapList(data) {
  return request({
    url: '/manager/scrap/pending/list',
    method: 'post',
    data,
  });
}

// 查询物资报废查看列表(分页)
export function queryScrapViewList(data) {
  return request({
    url: '/manager/scrap/view',
    method: 'post',
    data,
  });
}

// 查询物资报废申请列表(不分页)
export function queryAllScrapList() {
  return request({
    url: '/manager/scrap/listAll',
    method: 'get',
  });
}

// 根据id查询物资报废申请数据
export function getScrapById(id) {
  return request({
    url: `/manager/scrap/get/${id}`,
    method: 'get',
  });
}

// 新增物资报废申请
export function addScrap(data) {
  return request({
    url: '/manager/scrap/add',
    method: 'post',
    data,
  });
}

// 修改物资报废申请
export function updateScrap(data) {
  return request({
    url: '/manager/scrap/edit',
    method: 'put',
    data,
  });
}

// 删除物资报废申请
export function deleteScrap(id) {
  return request({
    url: `/manager/scrap/delete/${id}`,
    method: 'delete',
  });
}


// 物资报废申请单审核
export function processScrap(data) {
  return request({
    url: '/manager/scrap/process',
    method: 'post',
    data,
  });
}

// 物资报废申请单审核节点信息
export function getNodeInfo(data) {
  return request({
    url: '/manager/scrap/node/info',
    method: 'post',
    data,
  });
}


// 物资报废申请单审核节点信息
export function visaPreview(data) {
  return request({
    url: '/manager/scrap/preview',
    method: 'get',
    params: data,
  });
}
