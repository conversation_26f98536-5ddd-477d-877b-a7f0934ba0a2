import request from '@/utils/request'

// 条件查询边坡基础数据列表(分页)
export function getListPage(data) {
  return request({
    url: "/baseData/sideslope/his/getListPage",
    method: "post",
    data,
  });
}


// 查询边坡基础数据
export function getSideSlope(id) {
  return request({
    url: `/baseData/sideslope/his/get/${id}`,
    method: "get",
  });
}

// 根据id查询边坡防护形式数据明细列表
export function getProtection(params) {
  return request({
    url: '/baseData/sideslope/his/getRecordList/'+params.slopeHisId,
    method: 'get',
    params
  })
}