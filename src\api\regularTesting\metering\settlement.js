import request from '@/utils/request'

// 结算库列表(分页)
export function otherRepositoryList(repositoryDTO) {
  return request({
    url: '/manager/calc/check/settle/list',
    method: 'post',
    data: repositoryDTO
  });
}

// 根据id查询子目列表（分页）
export function fetchMethodList(data) {
  return request({
    url: `/manager/calc/check/settle/method/list`,
    method: 'post',
    data
  });
}

// 根据id查询子目列表（不分页）
export function fetchMethodListAll(data) {
  return request({
    url: `/manager/calc/check/settle/method/listAll`,
    method: 'post',
    data
  });
}


export function updateOtherMethodList(data) {
  return request({
    url: `/manager/calc/check/settle/method/update/number`,
    method: 'put',
    data
  });
}


// 获取签证单预览信息
export function getVisaPreviewInfo(params) {
  return request({
    url: '/manager/calc/check/settle/visa/preview',
    method: 'get',
    params,
  });
}

// 获取维修档案预览信息
export function getMaintainPreviewInfo(params) {
  return request({
    url: '/manager/calc/check/settle/maintain/preview',
    method: 'get',
    params,
  });
}

// 获取修复反馈预览信息
export function getRepairPreviewInfo(params) {
  return request({
    url: '/manager/calc/check/settle/repair/preview',
    method: 'get',
    params,
  });
}

// 施工档案下载
export function downloadMaintainZip(params) {
  return request({
    url: '/manager/calc/check/settle/file/download',
    method: 'get',
    params,
  });
}

// 获取签证人员信息
export function getVisaCheck(constructionId) {
  return request({
    url: `/manager/calc/check/settle/get/visacheck/${constructionId}`,
    method: 'get',
  });
}

export function updateVisaCheck(data) {
  return request({
    url: `/manager/calc/check/settle/update/visacheck`,
    method: 'put',
    data
  });
}

export function withdraw(data) {
  return request({
    url: '/manager/check/construction/withdraw',
    method: 'post',
    data
  });
}
