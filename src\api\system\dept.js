import request from '@/utils/request'

// 查询部门列表
export function listDept(query) {
  return request({
    url: '/system/dept/list',
    method: 'get',
    params: query
  })
}

// 查询部门列表（排除节点）
export function listDeptExcludeChild(deptId) {
  return request({
    url: '/system/dept/list/exclude/' + deptId,
    method: 'get'
  })
}

// 查询子属部门树形列表（排除节点）
export function deptSubTree(deptId) {
  return request({
    url: '/system/dept/list/deptSubTree/' + deptId,
    method: 'get'
  })
}

// 查询部门下拉树结构
export function deptTreeSelect(data) {
  return request({
    url: '/system/user/deptTree',
    method: 'get',
    params: data
  })
}

// 根据部门ID查询子属部门列表
export function findCheckedArr(deptId) {
  return request({
    url: '/system/dept/findCheckedArr/' + deptId,
    method: 'get'
  })
}

// 保存关联子属部门数据
export function addDeptSub(params) {
  return request({
    url: '/system/dept/addDeptSub',
    method: 'post',
    params: params
  })
}

// 查询部门详细
export function getDept(deptId) {
  return request({
    url: '/system/dept/' + deptId,
    method: 'get'
  })
}

// 新增部门
export function addDept(data) {
  return request({
    url: '/system/dept',
    method: 'post',
    data: data
  })
}

// 修改部门
export function updateDept(data) {
  return request({
    url: '/system/dept',
    method: 'put',
    data: data
  })
}

// 删除部门
export function delDept(deptId) {
  return request({
    url: '/system/dept/' + deptId,
    method: 'delete'
  })
}

// 保存资产权限
export function addDeptAssetData(params) {
  return request({
    url: '/system/dept/addDeptAssetData',
    method: 'post',
    params: params
  })
}

export function syncDeptAsset(params) {
  return request({
    url: '/system/dept/syncDeptAsset',
    params,
    method: 'get'
  })
}

export function syncDeptType(params) {
  return request({
    url: '/system/dept/syncDeptType',
    params,
    method: 'get'
  })
}

// 清空部门缓存
export function deptCacheClear() {
  return request({
    url: '/baseData/common/cache/deptCacheClear',
    method: 'post'
  })
}