import request from '@/utils/request'

// 查询完工登记列表
export function queryList(data) {
    return request({
        url: '/manager/proj/construction/finished/list/data',
        method: 'post',
        data,
    });
}

// 查询完工登记列表
export function queryProcessList(data) {
    return request({
        url: '/manager/proj/construction/finished/list/process',
        method: 'post',
        data,
    });
}

// 根据任务单id查询养护工程施工单明细列表
export function getDetailList(data) {
    return request({
        url: '/manager/proj/construction/detail/list',
        method: 'post',
        data,
    });
}


// 根据任务单id查询养护工程施工单明细列表
export function getMethodsList(id) {
  return request({
    url: `/manager/proj/construction/detail/list/data?projConId=${id}`,
    method: 'get',
  });
}

// 新增养护工程完工登记签证单
export function addFinishedDetail(data) {
    return request({
        url: '/manager/proj/construction/finished/add',
        method: 'post',
        data,
    });
}

// 根据签证单查询详情数据
export function getFinishedInfo(id) {
    return request({
        url: `/manager/proj/construction/finished/get/${id}`,
        method: 'get',
    });
}

// 修改养护工程完工登记
export function editFinishedDetail(data) {
    return request({
        url: '/manager/proj/construction/finished/edit',
        method: 'put',
        data,
    });
}


// 删除养护工程完工登记
export function delFinishedDetail(id) {
    return request({
        url: `/manager/proj/construction/finished/delete/${id}`,
        method: 'delete',
    });
}

// 根据施工单ID查询养护工程签证单列表
export function getFinishedPendingList(data) {
    return request({
        url: '/manager/proj/construction/finished/pending/list',
        method: 'post',
        data,
    });
}

// 根据施工单ID查询养护工程签证单列表
export function getFinishedList(data) {
    return request({
        url: '/manager/proj/construction/finished/list',
        method: 'post',
        data,
    });
}


// 根据签证单id（完工登记id）查询养护工程施工登记方法列表(分页)
export function getMethodList(data) {
    return request({
        url: '/manager/proj/construction/finished/method/list',
        method: 'post',
        data,
    });
}

// 根据施工单id查询养护工程施工登记方法列表
export function getAllMethodList(data) {
    return request({
        url: '/manager/proj/construction/finished/method/list/data',
        method: 'post',
        data,
    });
}



// 养护工程签证单提交与审核接口
export function finishedProcess(data) {
    return request({
        url: '/manager/proj/construction/finished/process',
        method: 'post',
        data,
    });
}

// 撤回到待施工

export function withdraw(data) {
    return request({
        url: '/manager/proj/construction/register/withdraw',
        method: 'post',
        data,
    });
}

// 养护工程签证单审核节点信息
export function getNodeInfo(data) {
  return request({
    url: '/manager/proj/construction/finished/node/info',
    method: 'post',
    data,
  });
}


// 根据项目id查询构造物数据

export function getStructureByProjId(id) {
    return request({
        url: `/manager/project/structure/get/${id}`,
        method: 'get',
    });
}



/**
 * 签证单预览
 * @param {string} id - 签证单ID
 * @returns {Promise} - 返回 Promise 对象
 */
export function previewVisa(id) {
    return request({
        url: `/manager/proj/construction/finished/visa/preview?id=${id}`,
        method: 'get'
    });
}

/**
 * 维修档案预览
 * @param {string} id - 维修档案ID
 * @returns {Promise} - 返回 Promise 对象
 */
export function previewMaintain(id) {
    return request({
        url: `/manager/proj/construction/finished/maintain/preview?id=${id}`,
        method: 'get'
    });
}

/**
 * 修复反馈预览
 * @param {string} id - 修复反馈ID
 * @returns {Promise} - 返回 Promise 对象
 */
export function previewRepair(id) {
    return request({
        url: `/manager/proj/construction/finished/repair/preview?id=${id}`,
        method: 'get'
    });
}

// 养护工程签证单审核节点信息
export function getRecord(data) {
  return request({
    url: '/manager/proj/construction/finished/operation/record',
    method: 'post',
    data,
  });
}


// 根据任务单id校验任务单是否进行了监理接收
export function checkSupProcess(conId) {
    return request({
        url: `/manager/proj/construction/finished/checkSupProcess?conId=${conId}`,
        method: 'get'
    });
}


// 编辑签证单编码与计算式说明接口
export function updateBaseInfo(data) {
    return request({
        url: '/manager/proj/construction/finished/update/baseInfo',
        method: 'put',
        data,
    });
}

// 编辑签证单节点更改
export function updateAuthorizedNode(data) {
  return request({
      url: '/manager/proj/construction/finished/update/record',
      method: 'post',
      data,
  });
}

/**
 * 根据签证单id查询操作记录
 * @param {string} id - 修复反馈ID
 * @returns {Promise} - 返回 Promise 对象
 */
export function getRecordById(id) {
    return request({
        url: `/manager/proj/construction/finished/get/record/${id}`,
        method: 'get'
    });
}
