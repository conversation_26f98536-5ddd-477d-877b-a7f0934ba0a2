import request from "@/utils/request";

// 记录详情
export function dynamicGet(data) {
  return request({
    url: "/baseData/tunnel/dynamic/get/"+data.id,
    method: "get",
  });
}
// 记录列表
export function getListPage(params) {
  return request({
    url: `/baseData/tunnel/dynamic/getListPage`,
    method: "get",
    params
  });
}

// 修改记录
export function dynamicEdit(data) {
    return request({
      url: "/baseData/tunnel/dynamic/edit",
      method: "put",
      data
    });
  }

  // 添加记录
export function dynamicAdd(data) {
    return request({
      url: "/baseData/tunnel/dynamic/add",
      method: "post",
      data
    });
  }

    // 删除记录
export function dynamicDelete(data) {
    return request({
      url: "/baseData/tunnel/dynamic/delete/"+data.id,
      method: "delete",
    });
  }

  


