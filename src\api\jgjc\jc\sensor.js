import request from '@/utils/request'

// 查询传感器列表
export function listSensor(query) {
  return request({
    url: '/jgjc-manager/api/console/jc/sensor/findPage',
    method: 'post',
    data: query
  })
}

// 查询传感器详细
export function getSensor(id) {
  return request({
    url: '/jgjc-manager/api/console/jc/sensor/findById?id=' + id,
    method: 'get'
  })
}

// 新增传感器
export function addSensor(data) {
  return request({
    url: '/jgjc-manager/api/console/jc/sensor/create',
    method: 'post',
    data: data
  })
}

// 修改传感器
export function updateSensor(data) {
  return request({
    url: '/jgjc-manager/api/console/jc/sensor/modify',
    method: 'post',
    data: data
  })
}

// 删除传感器
export function delSensor(id) {
  return request({
    url: '/jgjc-manager/api/console/jc/sensor/removeById?id=' + id,
    method: 'delete'
  })
}
