import request from '@/utils/request'


// 查询日养施工通知单审核列表
export function listNoticeReview(data) {
    return request({
        url: '/manager/constructionReview/list',
        method: 'post',
        data: data
    });
}

// 查询日养施工通知单审核详情
export function getConstructionDetail(id) {
    return request({
        url: `/manager/constructionReview/getConstructionDetail/${id}`,
        method: 'get'
    });
}

// 审核
export function reviewNoticeReview(data) {
    return request({
        url: '/manager/constructionReview/review',
        method: 'put',
        data: data
    });
}

// 批量审核
export function batchReviewNoticeReview(data) {
    return request({
        url: '/manager/constructionReview/batchReview',
        method: 'put',
        data: data
    });
}

// 驳回
export function rejectNoticeReview(data) {
    return request({
        url: '/manager/constructionReview/reject',
        method: 'put',
        data: data
    });
}

// 批量驳回
export function batchRejectNoticeReview(data) {
    return request({
        url: '/manager/constructionReview/batchReject',
        method: 'put',
        data: data
    });
}

// 导出日养施工通知单审核列表
export function exportList() {
    return request({
        url: '/manager/constructionReview/export',
        method: 'post'
    });
}
