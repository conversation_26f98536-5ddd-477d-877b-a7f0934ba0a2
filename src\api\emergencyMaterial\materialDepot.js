import request from '@/utils/request'


export function getNodeInfo(materialId) {
  return request({
    url: `/manager/emergency/material/detail/listAll/${materialId}`,
    method: 'get',
  })
}

// 获取物资库列表
export function getMaterialDepotList(data) {
  return request({
    url: '/manager/emergency/material/list',
    method: 'post',
    data
  })
}

// 获取物资类型列表
export function getMaterialTypeList(data) {
  return request({
    url: '/manager/emergency/material/type/list',
    method: 'post',
    data
  })
}

// 新增物资库
export function addMaterialDepot(data) {
  return request({
    url: '/manager/emergency/material/add',
    method: 'post',
    data
  })
}

// 根据id查询应急物资库数据
export function getDetailMaterialList(id) {
  return request({
    url: `/manager/emergency/material/get/${id}`,
    method: 'get'
  })
}

// 编辑物资库
export function editMaterialDepot(data) {
  return request({
    url: '/manager/emergency/material/edit',
    method: 'put',
    data
  })
}

// 删除物资库
export function delMaterialDepot(id) {
  return request({
    url: `/manager//emergency/material/delete/${id}`,
    method: 'delete',
  })
}

// 查询应急物资库明细列表
export function getMaterialDetailList(data) {
  return request({
    url: '/manager/emergency/material/detail/list',
    method: 'post',
    data
  })
}

// 查询应急物资库所有明细
export function getAllMaterialDetailList(materialId) {
  return request({
    url: `/manager/emergency/material/detail/listAll/${materialId}`,
    method: 'get',
  })
}

// 新增应急物资库明细
export function addMaterialDetail(data) {
  return request({
    url: '/manager/emergency/material/detail/add',
    method: 'post',
    data
  })
}

// 修改应急物资库明细
export function editMaterialDetail(data) {
  return request({
    url: '/manager/emergency/material/detail/edit',
    method: 'put',
    data
  })
}

// 删除应急物资库明细
export function delMaterialDetail(id) {
  return request({
    url: `/manager/emergency/material/detail/delete/${id}`,
    method: 'delete',
  })
}

// 查询物资库明细下操作数据
export function materialDetailListEvent(data) {
  return request({
    url: '/manager/emergency/material/detail/list/data',
    method: 'post',
    data
  })
}

// 查询物资库明细下操作节点
export function getDetailNodeInfo(data) {

}


export function getOperationRecord(materialId) {
  return request({
    url: `/manager/emergency/material/operation/record?id=${materialId}`,
    method: 'get',
  })
}
