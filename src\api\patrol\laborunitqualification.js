import request from '@/utils/request'

// 查询劳务单位资质情况列表
export function listLaborunitqualification(query) {
  return request({
    url: '/manager/laborunitqualification/list',
    method: 'get',
    params: query
  })
}

// 查询劳务单位资质情况详细
export function getLaborunitqualification(id) {
  return request({
    url: '/manager/laborunitqualification/get/' + id,
    method: 'get'
  })
}

// 新增劳务单位资质情况
export function addLaborunitqualification(data) {
  return request({
    url: '/manager/laborunitqualification/add',
    method: 'post',
    data: data
  })
}

// 修改劳务单位资质情况
export function updateLaborunitqualification(data) {
  return request({
    url: '/manager/laborunitqualification/edit',
    method: 'put',
    data: data
  })
}

// 删除劳务单位资质情况
export function delLaborunitqualification(id) {
  return request({
    url: '/manager/laborunitqualification/delete/' + id,
    method: 'delete'
  })
}
