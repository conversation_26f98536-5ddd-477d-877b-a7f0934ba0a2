import request from '@/utils/request'


// 查询上传附件
export function listEngineeringAttachment(query) {
  return request({
    url: '/repote/engineeringAttachment/list',
    method: 'get',
    params: query
  })
}

// 修改上传附件
export function updateTransportationAttachment(data) {
  return request({
    url: '/repote/engineeringAttachment/edit',
    method: 'put',
    data: data
  })
}


// 新增上传附件
export function addTransportationAttachment(data) {
  return request({
    url: '/repote/engineeringAttachment/add',
    method: 'post',
    data: data
  })
}

// 删除上传附件
export function deleteTransportationAttachment(id) {
  return request({
    url: '/repote/engineeringAttachment/delete/' + id,
    method: 'delete'
  })
}


// 查询大件运输附件详细
export function getTransportationAttachment(id) {
  return request({
    url: '/repote/engineeringAttachment/get/' + id,
    method: 'get'
  })
}


