import request from '@/utils/request'

// 应急物资入库单审核节点信息
export function getNodeInfo(data) {
  return request({
    url: '/manager/emergency/material/supplement/node/info',
    method: 'post',
    data
  })
}

// 应急物资补充申请单列表
export function getReplenishApplicitionList(data) {
  return request({
    url: '/manager/emergency/material/supplement/list',
    method: 'post',
    data
  })
}

// 删除补充申请单
export function delMaterialReplenish(id) {
  return request({
    url: `/manager/emergency/material/supplement/delete/${id}`,
    method: 'delete',
  })
}

//  补充单审核
export function submitReplenish(data) {
  return request({
    url: '/manager/emergency/material/supplement/process',
    method: 'post',
    data
  })
}

// 新增补充单时查询入库单记录
export function getInboundRecord(data) {
  return request({
    url: '/manager/emergency/material/inbound/data',
    method: 'post',
    data
  })
}

// 新增补充单
export function addReplenishApplicition(data) {
  return request({
    url: '/manager/emergency/material/supplement/add',
    method: 'post',
    data
  })
}

// 修改补充单
export function editReplenishApplicition(data) {
  return request({
    url: '/manager/emergency/material/supplement/edit',
    method: 'put',
    data
  })
}

// 根据id查询应急物资补充单数据
export function getDetailInList(id) {
  return request({
    url: `/manager/emergency/material/supplement/get/${id}`,
    method: 'get'
  })
}

// 根据物资库id与入库单id查询应急物资入库单明细列表
export function getReplenishDetailList(data) {
  return request({
    url: '/manager/emergency/material/inbound/detail/list',
    method: 'post',
    data
  })
}

// 获取补充单审核列表
export function getReplenishAuditList (data) {
  return request({
    url: '/manager/emergency/material/supplement/pending/list',
    method: 'post',
    data
  })
}

// 获取补充单结果列表
export function getReplenishResultList(data) {
  return request({
    url: '/manager/emergency/material/supplement/view',
    method: 'post',
    data
  })
}


// 报表
export function previewReport(id) {
  return request({
    url: `/manager/emergency/material/supplement/report/preview?id=${id}`,
    method: 'get',
  })
}
