import request from '@/utils/request'

// 查询汛期巡查记录列表
export function listXqinspection(query) {
  return request({
    url: '/middleData/xqinspection/list',
    method: 'get',
    params: query
  })
}

// 查询汛期巡查记录详细
export function getXqinspection(id) {
  return request({
    url: '/middleData/xqinspection/get/' + id,
    method: 'get'
  })
}

// 新增汛期巡查记录
export function addXqinspection(data) {
  return request({
    url: '/middleData/xqinspection/add',
    method: 'post',
    data: data
  })
}

// 修改汛期巡查记录
export function updateXqinspection(data) {
  return request({
    url: '/middleData/xqinspection/edit',
    method: 'put',
    data: data
  })
}

// 删除汛期巡查记录
export function delXqinspection(id) {
  return request({
    url: '/middleData/xqinspection/delete/' + id,
    method: 'delete'
  })
}
// 查询汛期巡查记录列表
export function listXqHazard(query) {
  return request({
    url: '/middleData/xqHazard/list',
    method: 'get',
    params: query
  })
}


export function queryXqHazard(query) {
  return request({
    url: '/middleData/xqHazard/query',
    method: 'get',
    params: query
  })
}


export function countXqHazard(query) {
  return request({
    url: '/middleData/xqHazard/count',
    method: 'get',
    params: query
  })
}

