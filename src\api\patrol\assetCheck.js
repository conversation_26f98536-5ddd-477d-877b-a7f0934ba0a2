import request from '@/utils/request';

const checkType = {
  1: 'patrolBridgeCheck',
  2: 'patrolBridgeCheck',
  3: 'patrolCulvertCheck',
  4: 'patrolCulvertCheck',
  5: 'patrolTunnelCheck',
  6: 'patrolTunnelCheck',
};

// 查询资产寻检查主列表
export function listByDistance(query) {
  return request({
    url: `/patrol/assetCheck/listByDistance`,
    method: 'post',
    data: query,
    params: (function () {
      let temp = {};
      temp.pageNum = query.pageNum;
      temp.pageSize = query.pageSize;
      return temp;
    })(),
  });
}

// 查询资产寻检查主列表
export function getAssetTotalCount(query) {
  return request({
    url: `/patrol/assetCheck/getAssetTotalCount`,
    method: 'post',
    data: query,
  });
}

// 查询资产寻检查主列表
export function getTotalCount(query) {
  return request({
    url: `/patrol/assetCheck/getTotalCount`,
    method: 'post',
    data: query,
  });
}

// 获取生成日志
export function setGenerateLog(query) {
  return request({
    url: `/patrol/assetCheck/setGenerateLog`,
    method: 'post',
    data: query,
  });
}

// 生成资产巡查记录
export function setGenerateCheck(query) {
  return request({
    url: `/patrol/assetCheck/setGenerateCheck`,
    method: 'post',
    data: query,
  });
}

// 获取进度
export function getProgress(query) {
  return request({
    url: `/patrol/assetCheck/getGeneratingCheck`,
    method: 'post',
    data: query,
  });
}

// 查询检查列表
export function selectAssetCheckData(query) {
  return request({
    url: `/patrol/assetCheck/selectAssetCheckData`,
    method: 'post',
    data: query,
    params: {
      pageNum: query.pageNum,
      pageSize: query.pageSize,
    },
  });
}

// 条件查询统计详情列表
export function selectAssetDetail(query) {
  return request({
    url: `/patrol/assetCheck/selectAssetDetail`,
    method: 'post',
    data: query,
    params: {
      pageNum: query.pageNum,
      pageSize: query.pageSize,
    },
  });
}

// 条件查询统计详情列表
export function getCheckTimesPage(query) {
  return request({
    url: `/patrol/assetCheck/getCheckTimesPage`,
    method: 'post',
    data: query,
    params: {
      pageNum: query.pageNum,
      pageSize: query.pageSize,
    },
  });
}

// 已巡查时间
export function getMergedDays(params) {
  return request({
    url: `/patrol/assetCheck/getMergedDays`,
    method: 'get',
    params: params,
  });
}

// 查询资产寻检查主列表
export function listAssetCheck(query) {
  return request({
    url: `/patrol/${checkType[query.type]}/selectAll`,
    method: 'post',
    data: query,
    params: (function () {
      let temp = {};
      temp.pageNum = query.pageNum;
      temp.pageSize = query.pageSize;
      return temp;
    })(),
  });
}

// 查询资产寻检查主详细
export function getAssetCheck(data) {
  return request({
    url: `/patrol/${checkType[data.type]}/selectOne/${data.id}`,
    method: 'get',
  });
}

// 新增资产寻检查主
export function addAssetCheck(data) {
  return request({
    url: `/patrol/${checkType[data.type]}/insert`,
    method: 'post',
    data: data,
  });
}

// 修改资产寻检查主
export function updateAssetCheck(data) {
  return request({
    url: `/patrol/${checkType[data.type]}/update`,
    method: 'put',
    data: data,
  });
}

// 删除资产寻检查主
export function delAssetCheck(data) {
  return request({
    url: `/patrol/${checkType[data.type]}/delete`,
    method: 'delete',
    data: data.idList,
  });
}

// 批量设置状态
export function updateStatusByIds(data) {
  return request({
    url: `/patrol/assetCheck/updateStatusByIds`,
    method: 'post',
    data: data,
  });
}
// 批量审核
export function batchComplete(data) {
  return request({
    url: `/patrol/assetCheck/batchComplete`,
    method: 'post',
    data: data,
  });
}
