import request from '@/utils/request'

// 查询预算计划明细（专项）列表
export function listProj(query) {
  return request({
    url: '/budget/proj/list',
    method: 'get',
    params: query
  })
}

// 查询预算计划明细（专项）详细
export function getProj(id) {
  return request({
    url: '/budget/proj/get/' + id,
    method: 'get'
  })
}

// 新增预算计划明细（专项）
export function addProj(data) {
  return request({
    url: '/budget/proj/add',
    method: 'post',
    data: data
  })
}

// 修改预算计划明细（专项）
export function updateProj(data) {
  return request({
    url: '/budget/proj/edit',
    method: 'put',
    data: data
  })
}

// 删除预算计划明细（专项）
export function delProj(id) {
  return request({
    url: '/budget/proj/delete/' + id,
    method: 'delete'
  })
}
