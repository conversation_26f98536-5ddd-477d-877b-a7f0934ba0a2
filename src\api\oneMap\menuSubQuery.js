import request from '@/utils/request'

// 查询查询项设置名存储的子列表
export function getListPage(query) {
  return request({
    url: '/oneMap/menuSubQueryDetail/getListPage',
    method: 'get',
    params: query
  })
}

// 查询查询项设置名存储的子详细
export function getMenuSubQuery(id) {
  return request({
    url: '/oneMap/menuSubQueryDetail/getInfoById',
    method: 'get',
    params: { id }
  })
}

// 新增查询项设置名存储的子
export function addMenuSubQuery(data) {
  return request({
    url: '/oneMap/menuSubQueryDetail/add',
    method: 'post',
    data
  })
}

// 修改查询项设置名存储的子
export function updateMenuSubQuery(data) {
  return request({
    url: '/oneMap/menuSubQueryDetail/edit',
    method: 'post',
    data
  })
}

// 删除查询项设置名存储的子
export function delMenuSubQuery(ids) {
  return request({
    url: '/oneMap/menuSubQueryDetail/delete',
    method: 'delete',
    data: ids
  })
}
