import request from '@/utils/request'

// 查询风险点巡查频率配置列表
export function listFrequencySetting(query) {
  return request({
    url: '/disaster/frequencySetting/list',
    method: 'get',
    params: query
  })
}

// 查询风险点巡查频率配置详细
export function getFrequencySetting(id) {
  return request({
    url: '/disaster/frequencySetting/get/' + id,
    method: 'get'
  })
}

// 新增风险点巡查频率配置
export function addFrequencySetting(data) {
  return request({
    url: '/disaster/frequencySetting/add',
    method: 'post',
    data: data
  })
}

// 修改风险点巡查频率配置
export function updateFrequencySetting(data) {
  return request({
    url: '/disaster/frequencySetting/edit',
    method: 'put',
    data: data
  })
}

// 删除风险点巡查频率配置
export function delFrequencySetting(id) {
  return request({
    url: '/disaster/frequencySetting/delete/' + id,
    method: 'delete'
  })
}
