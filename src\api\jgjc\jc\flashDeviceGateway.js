import request from '@/utils/request'

// 查询爆闪装置网关列表
export function listFlashDeviceGateway(query) {
  return request({
    url: '/jgjc-manager/api/console/jc/flashDeviceGateway/findPage',
    method: 'post',
    data: query
  })
}

// 查询爆闪装置网关详细
export function getFlashDeviceGateway(id) {
  return request({
    url: '/jgjc-manager/api/console/jc/flashDeviceGateway/findById?id=' + id,
    method: 'get'
  })
}

// 新增爆闪装置网关
export function addFlashDeviceGateway(data) {
  return request({
    url: '/jgjc-manager/api/console/jc/flashDeviceGateway/create',
    method: 'post',
    data: data
  })
}

// 修改爆闪装置网关
export function updateFlashDeviceGateway(data) {
  return request({
    url: '/jgjc-manager/api/console/jc/flashDeviceGateway/modify',
    method: 'post',
    data: data
  })
}

// 删除爆闪装置网关
export function delFlashDeviceGateway(id) {
  return request({
    url: '/jgjc-manager/api/console/jc/flashDeviceGateway/removeById?id=' + id,
    method: 'delete'
  })
}
// 批量删除
export function delDeviceTypeBatch(ids) {
  return request({
    url: '/jgjc-manager/api/console/jc/flashDeviceGateway/batchRemove',
    method: 'post',
    data: ids
  })
}
