import request from '@/utils/request'

// 查询列表
export function getListPage(data) {
  return request({
    url: '/baseData/pavement/assess/getListPage',
    method: 'post',
    data
  })
}

///pavement/assess/get
// 查询详情
export function getRoadbedAssess(data) {
  return request({
    url: '/baseData/pavement/assess/get/'+data,
    method: 'get',
  
  })
}

// 删除
export function deleteByIds(data) {
  return request({
    url: '/baseData/pavement/assess/delete',
    method: 'delete',
    data: data
  })
}

// 新增
export function addRoadbedAssess(data) {
  return request({
    url: '/baseData/pavement/assess/add',
    method: 'post',
    data: data
  })
}



// 修改
export function updateRoadbedAssess(data) {
  return request({
    url: '/baseData/pavement/assess/edit',
    method: 'put',
    data: data
  })
}


///pavement/maintenance/manage/getListPage
// 查询列表
export function getMaintainListPage(data) {
  return request({
    url: '/baseData/pavement/maintenance/manage/getListPage',
    method: 'post',
    data
  })
}

///pavement/maintenance/manage/edit
// 修改
export function updateMaintain(data) {
  return request({
    url: '/baseData/pavement/maintenance/manage/edit',
    method: 'put',
    data: data
  })
}

// /pavement/maintenance/manage/delete/{ids}
// 删除
export function deleteMaintain(data) {
  return request({
    url: '/baseData/pavement/maintenance/manage/delete/'+data,
    method: 'delete',
   
  })
}

///pavement/maintenance/manage/add
// 新增
export function addMaintain(data) {
  return request({
    url: '/baseData/pavement/maintenance/manage/add',
    method: 'post',
    data: data
  })
}

