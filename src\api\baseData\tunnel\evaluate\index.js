import request from '@/utils/request'

// 查询列表
export function getListPage(data) {
  return request({
    url: '/baseData/tunnel/evaluate/getListPage',
    method: 'post',
    data
  })
}

// 删除
export function deleteByIds(data) {
  return request({
    url: '/baseData/tunnel/evaluate/delete',
    method: 'delete',
    data: data
  })
}

// 新增
export function addTunnelEvaluateBridge(data) {
  return request({
    url: '/baseData/tunnel/evaluate/add',
    method: 'post',
    data: data
  })
}



// 修改
export function updateTunnelEvaluateBridge(data) {
  return request({
    url: '/baseData/tunnel/evaluate/edit',
    method: 'put',
    data: data
  })
}

