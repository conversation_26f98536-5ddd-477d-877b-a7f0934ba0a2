import { param } from '@/utils/index';
import request from '@/utils/request'

// 查询列表
export function getListPage(data) {
  return request({
    url: '/baseData/pavement/construction/getListPage',
    method: 'post',
    data
  })
}

// 增
export function addConstruction(data) {
  return request({
    url: "/baseData/pavement/construction/add",
    method: "post",
    data,
  });
}

// 删
export function delConstruction(ids) {
  return request({
    url: `/baseData/pavement/construction/delete/${ids}`,
    method: 'delete',
  })
}

// 改
export function updateConstruction(data) {
  return request({
    url: '/baseData/pavement/construction/edit',
    method: 'put',
    data: data
  })
}

// 查
export function getConstruction(id) {

  return request({
    url: `/baseData/pavement/construction/get/${id}`,
    method: "get",
  });
}


// ​/pavement​/construction​/temp​/add
// 增
export function tempConstruction(data) {
  return request({
    url: "/baseData/pavement/construction/temp/add",
    method: "post",
    data,
  });
}

