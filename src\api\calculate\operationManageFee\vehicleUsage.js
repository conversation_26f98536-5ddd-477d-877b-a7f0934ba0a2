import request from '@/utils/request'

// 查询车辆使用费列表(分页)
export function listCarusePage(data) {
    return request({
        url: 'manager/operate/caruse/list',
        method: 'post',
        data
    });
}

// 查询车辆使用费列表(不分页)
export function listCaruseAll() {
    return request({
        url: 'manager/operate/caruse/listAll',
        method: 'get'
    });
}

// 根据id查询车辆使用费数据
export function getCaruseById(id) {
    return request({
        url: `manager/operate/caruse/get/${id}`,
        method: 'get'
    });
}

// 新增车辆使用费
export function addCaruse(data) {
    return request({
        url: 'manager/operate/caruse/add',
        method: 'post',
        data
    });
}

// 修改车辆使用费
export function editCaruse(data) {
    return request({
        url: 'manager/operate/caruse/edit',
        method: 'put',
        data
    });
}

// 删除车辆使用费
export function deleteCaruse(id) {
    return request({
        url: `manager/operate/caruse/delete/${id}`,
        method: 'delete'
    });
}
