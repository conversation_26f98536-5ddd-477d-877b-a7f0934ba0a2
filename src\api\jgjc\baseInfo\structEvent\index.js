import request from '@/utils/request'

/**
 * 事件记录分页查询
 * @param {Object} data 查询参数
 * @returns {Promise}
 */
export function getIncidentRecordPage(data) {
  return request({
    url: '/jgjcadmin/api/basedata/incident-record/page',
    method: 'post',
    data
  })
}

/**
 * 删除事件记录
 * @param {Object} data 删除参数（通常包含id数组）
 * @returns {Promise}
 */
export function deleteIncidentRecord(data) {
  return request({
    url: '/jgjcadmin/api/basedata/incident-record/delete',
    method: 'post',
    data
  })
}

/**
 * 新增事件记录
 * @param {Object} data 事件记录数据
 * @returns {Promise}
 */
export function saveIncidentRecord(data) {
  return request({
    url: '/jgjcadmin/api/basedata/incident-record/save',
    method: 'post',
    data
  })
}

/**
 * 更新事件记录
 * @param {Object} data 事件记录数据（需包含id）
 * @returns {Promise}
 */
export function updateIncidentRecord(data) {
  return request({
    url: '/jgjcadmin/api/basedata/incident-record/update',
    method: 'post',
    data
  })
}
