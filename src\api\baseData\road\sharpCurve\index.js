import request from '@/utils/request'

// 查询列表
export function getListPage(data) {
  return request({
    url: '/baseData/route/sharpCurve/getListPage',
    method: 'post',
    data
  })
}

// 增
export function addIceCovered(data) {
  return request({
    url: "/baseData/route/sharpCurve/add",
    method: "post",
    data,
  });
}

// 暂存
export function tempIceCovered(data) {
  return request({
    url: "/baseData/route/sharpCurve/tempAdd",
    method: "post",
    data,
  });
}

// 删
export function delIceCovered(ids) {
  return request({
    url: `/baseData/route/sharpCurve/delete/${ids}`,
    method: 'delete',
  })
}

// 改
export function updateIceCovered(data) {
  return request({
    url: '/baseData/route/sharpCurve/edit',
    method: 'put',
    data: data
  })
}

// 查
export function getIceCovered(id) {

  return request({
    url: `/baseData/route/sharpCurve/get/${id}`,
    method: "get",
  });
}

