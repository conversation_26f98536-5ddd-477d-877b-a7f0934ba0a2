import request from '@/utils/request'

export function listTaskExt(query) {
  return request({
    url: '/workFlow/taskExt/list',
    method: 'get',
    params: query
  })
}


export function listByInstanceId(processInstanceId) {
  return request({
    url: '/workFlow/taskExt/listByInstanceId?processInstanceId='+processInstanceId,
    method: 'get',
  })
}

export function saveTaskExt(data) {
  return request({
    url: '/workFlow/taskExt/save',
    method: 'post',
    data
  })
}
