import request from '@/utils/request'

// 系统使用情况
export function getDiseaseEventList(repositoryDTO) {
	return request({
		url: '/manager/systemUse/GetDiseaseEventList',
		method: 'get',
		params: repositoryDTO
	});
}



// 运营管理费台账
export function getOperateStatistics(repositoryDTO) {
	return request({
		url: '/manager/operate/metering/detail/statistics',
		method: 'post',
		data: repositoryDTO
	});
}


// 施工单台账
export function getProjectWorkCalcReport(repositoryDTO) {
	return request({
		url: '/manager/calcreportdata/getProjectWorkCalcReport',
		method: 'post',
		data: repositoryDTO
	});
}



// 施工单台账明细
export function getProjectWorkCalcDetailReport(repositoryDTO) {
	return request({
		url: '/manager/calcreportdata/getProjectWorkCalcDetailReport',
		method: 'post',
		data: repositoryDTO
	});
}


// 用电户号统计

export function getElectricityNumber(data) {
  return request({
    url: '/manager/electricity/number/report',
    method: 'post',
    data
  });
}
