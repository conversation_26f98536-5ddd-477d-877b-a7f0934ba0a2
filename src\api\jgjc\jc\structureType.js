import request from '@/utils/request'

// 查询结构物类型列表
export function listStructureType(query) {
  return request({
    url: '/jgjc-manager/api/console/jc/structureType/findPage',
    method: 'post',
    data: query
  })
}

// 查询结构物类型详细
export function getStructureType(id) {
  return request({
    url: '/jgjc-manager/api/console/jc/structureType/findById?id=' + id,
    method: 'get'
  })
}

// 新增结构物类型
export function addStructureType(data) {
  return request({
    url: '/jgjc-manager/api/console/jc/structureType/create',
    method: 'post',
    data: data
  })
}

// 修改结构物类型
export function updateStructureType(data) {
  return request({
    url: '/jgjc-manager/api/console/jc/structureType/modify',
    method: 'post',
    data: data
  })
}

// 删除结构物类型
export function delStructureType(id) {
  return request({
    url: '/jgjc-manager/api/console/jc/structureType/removeById?id=' + id,
    method: 'delete'
  })
}
// 批量删除结构物类型
export function delStructureTypeBatch(ids) {
  return request({
    url: '/jgjc-manager/api/console/jc/structureType/batchRemove',
    method: 'post',
    data: ids
  })
}
// 获取所有结构类型列表
export function findAllStructureType() {
  return request({
    url: '/jgjc-manager/api/console/jc/structureType/findAll',
    method: 'get'
  })
}
