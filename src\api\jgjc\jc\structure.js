import request from '@/utils/request'

// 查询结构物列表
export function listStructure(query) {
  return request({
    url: '/jgjc-manager/api/console/jc/structure/findPage',
    method: 'post',
    data: query
  })
}

// 查询结构物详细
export function getStructure(id) {
  return request({
    url: '/jgjc-manager/api/console/jc/structure/findById?id=' + id,
    method: 'get'
  })
}

// 新增结构物
export function addStructure(data) {
  return request({
    url: '/jgjc-manager/api/console/jc/structure/create',
    method: 'post',
    data: data
  })
}

// 修改结构物
export function updateStructure(data) {
  return request({
    url: '/jgjc-manager/api/console/jc/structure/modify',
    method: 'post',
    data: data
  })
}

// 删除结构物
export function delStructure(id) {
  return request({
    url: '/jgjc-manager/api/console/jc/structure/removeById?id=' + id,
    method: 'delete'
  })
}
// 批量删除结构物类型
export function delStructureBatch(ids) {
  return request({
    url: '/jgjc-manager/api/console/jc/structure/batchRemove',
    method: 'post',
    data: ids
  })
}
// 获取所有结构物树型数据
export function getFindTreeStructure() {
  return request({
    url: '/jgjc-manager/api/console/jc/structure/findTree',
    method: 'get'
  })
}
