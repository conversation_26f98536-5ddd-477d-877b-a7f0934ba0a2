import request from "@/utils/request";

/**
 * 互通管理相关接口
 */

// 查询匝道列表
export function culvertList(data) {
  return request({
    url: "/baseData/culvert/list",
    method: "post",
    data,
  });
}

// 添加涵洞信息
export function culvertAdd(data) {
  return request({
    url: "/baseData/culvert/add",
    method: "post",
    data,
  });
}

// 修改涵洞信息
export function culvertEdit(data) {
  return request({
    url: "/baseData/culvert/edit",
    method: "put",
    data,
  });
}

// 删除涵洞信息
export function culvertDelete(id) {
  return request({
    url: `/baseData/culvert/delete`,
    method: "delete",
    data: id,
  });
}

/**
 * 暂存涵洞数据
 * @param {*} data
 * @returns
 */
export function tempAdd(data) {
  return request({
    url: "/baseData/culvert/tempAdd",
    method: "post",
    data,
  });
}

/**
 * 详情获取接口
 * @param {*} id 
 * @returns 
 */
export function getInfoById(id) {
  return request({
    url: `/baseData/culvert/getInfoById`,
    method: "get",
    params: { id },
  });
}

/**
 * 更新锁定数据状态
 * @param {*} ids 
 * @returns 
 */
export function updateLocked(ids) {
  return request({
    url: `/baseData/culvert/updateLocked`,
    method: "post",
    data: ids,
  });
}


export function culvertListPage(data) {
  return request({
      url: "/baseData/api/culvert/getListPage",
    method: "post",
    data,
  });
}

// 获取涵洞卡片信息
export function getCard(data) {
  return request({
    url: "/baseData/culvert/getCard",
    method: "post",
    data,
  });
}