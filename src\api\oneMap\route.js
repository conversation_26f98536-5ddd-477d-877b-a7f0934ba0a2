import request from "@/utils/request";

/**
 * 发送 GET 请求以获取路线信息
 * @param {Object} query - 查询参数对象
 * @returns {Promise} - 包含响应数据的 Promise
 */
export function getRouteInfo(data,params) {
  return request({
    url: "/oneMap/route/getRouteInfo",
    method: "post",
    data,
    params,
    notVerifyDuplicates: true,
    isLoading: false,
  });
}

/**
 * 获取养护记录
 * @param {Object} data - 查询参数对象
 * @returns {Promise} - 包含响应数据的 Promise
 */
export function getMRecord(data) {
  return request({
    url: "/oneMap/route/getPavementMaintenanc",
    method: "post",
    data,
  });
}
