import request from '@/utils/request'

// 查询基础保障设施隐患-防洪标识列表
export function listFloodControlSignHazards(query) {
  return request({
    url: '/middleData/floodControlSignHazards/list',
    method: 'post',
    params: { pageNum: query.pageNum, pageSize: query.pageSize },
    data: query
  })
}

// 查询基础保障设施隐患-防洪标识详细
export function getFloodControlSignHazards(id) {
  return request({
    url: '/middleData/floodControlSignHazards/get/' + id,
    method: 'get'
  })
}

// 新增基础保障设施隐患-防洪标识
export function addFloodControlSignHazards(data) {
  return request({
    url: '/middleData/floodControlSignHazards/add',
    method: 'post',
    data: data
  })
}

// 修改基础保障设施隐患-防洪标识
export function updateFloodControlSignHazards(data) {
  return request({
    url: '/middleData/floodControlSignHazards/edit',
    method: 'put',
    data: data
  })
}

// 删除
export function delData(data) {
  return request({
    url: `/middleData/floodControlSignHazards/delete`,
    method: 'delete',
    data: data.idList,
  });
}

// 批量审核
export function batchAudit(data) {
  return request({
    url: '/middleData/floodControlSignHazards/batchAudit',
    method: 'post',
    data: data
  })
}
//资产查询
export function listAssetNew(data) {
  return request({
    url: `/patrol/assetCheck/listByDistance?pageSize=${data.pageSize}&pageNum=${data.pageNum}`,
    method: "post",
    data,
  });
}

