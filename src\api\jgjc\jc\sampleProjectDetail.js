import request from '@/utils/request'

// 查询采样方案明细列表
export function listSampleProjectDetail(query) {
  return request({
    url: '/jgjc-manager/api/console/jc/sampleProjectDetail/findPage',
    method: 'post',
    data: query
  })
}

// 查询采样方案明细详细
export function getSampleProjectDetail(id) {
  return request({
    url: '/jgjc-manager/api/console/jc/sampleProjectDetail/findById?id=' + id,
    method: 'get'
  })
}

// 新增采样方案明细
export function addSampleProjectDetail(data) {
  return request({
    url: '/jgjc-manager/api/console/jc/sampleProjectDetail/create',
    method: 'post',
    data: data
  })
}

// 修改采样方案明细
export function updateSampleProjectDetail(data) {
  return request({
    url: '/jgjc-manager/api/console/jc/sampleProjectDetail/modify',
    method: 'post',
    data: data
  })
}

// 删除采样方案明细
export function delSampleProjectDetail(id) {
  return request({
    url: '/jgjc-manager/api/console/jc/sampleProjectDetail/removeById?id=' + id,
    method: 'delete'
  })
}
// 批量删除采样方案明细
export function delSampleProjectDetailBatch(ids) {
  return request({
    url: '/jgjc-manager/api/console/jc/sampleProjectDetail/batchRemove',
    method: 'post',
    data: ids
  })
}
