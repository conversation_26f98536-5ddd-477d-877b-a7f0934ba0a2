import request from '@/utils/request'

// 查询风险路段和山洪淹没路段列表
export function listAssessment(query) {
  return request({
    url: '/middleData/roadRiskAssessment/list',
    method: 'get',
    params: query
  })
}

// 查询风险路段和山洪淹没路段详细
export function getAssessment(id) {
  return request({
    url: '/middleData/roadRiskAssessment/get/' + id,
    method: 'get'
  })
}

// 新增风险路段和山洪淹没路段
export function addAssessment(data) {
  return request({
    url: '/middleData/roadRiskAssessment/add',
    method: 'post',
    data: data
  })
}

// 修改风险路段和山洪淹没路段
export function updateAssessment(data) {
  return request({
    url: '/middleData/roadRiskAssessment/edit',
    method: 'put',
    data: data
  })
}

// 删除风险路段和山洪淹没路段
export function delAssessment(id) {
  return request({
    url: '/middleData/roadRiskAssessment/delete/' + id,
    method: 'delete'
  })
}

// 批量删除
export function delAssessmentBatch(id) {
  return request({
    url: '/middleData/roadRiskAssessment/deleteBatch',
    method: 'post',
    data: id
  })
}
