import request from '@/utils/request'

// 查询预算类型配置列表(分页)
export function listtypeconfigPage(data) {
    return request({
        url: '/manager/typeconfig/list',
        method: 'post',
        data
    });
}

// 查询预算类型配置列表(不分页)
export function listAlltypeconfig() {
    return request({
        url: '/manager/typeconfig/listAll',
        method: 'get'
    });
}

// 根据id查询预算类型配置数据
export function gettypeconfigById(id) {
    return request({
        url: `/manager/typeconfig/get/${id}`,
        method: 'get'
    });
}

// 新增预算类型配置
export function addtypeconfig(data) {
    return request({
        url: '/manager/typeconfig/add',
        method: 'post',
        data
    });
}

// 修改预算类型配置
export function updatetypeconfig(data) {
    return request({
        url: '/manager/typeconfig/edit',
        method: 'put',
        data
    });
}

// 删除预算类型配置
export function deletetypeconfig(id) {
    return request({
        url: `/manager/typeconfig/delete/${id}`,
        method: 'delete'
    });
}

// 导出预算类型配置列表
export function exporttypeconfig() {
    return request({
        url: '/manager/typeconfig/export',
        method: 'post'
    });
}

// 获取管理处及以下机构的路段
export function getMianSectionData() {
    return request({
        url: '/manager/typeconfig/getMianSectionData',
        method: 'get'
    });
}
// 获取管理处及以下机构的路段
export function findByYear(year) {
    return request({
        url: `/manager/typeconfig/findByYear/${year}`,
        method: 'get'
    });
}
// 获取年份列表
export function findByYearList() {
    return request({
        url: `/manager/yearconfig/getYears`,
        method: 'get'
    });
}
// 获取管理处及以下机构的路段
export function findShowDataByPlanId(planid) {
    return request({
        url: `/manager/plandetail/findTypeConfig/${planid}`,
        method: 'get'
    });
}
// 获取预算计划列表
export function GetPlan(params) {
    return request({
        url: `/manager/plan/getplan`,
        method: 'get',
        params
    });
}
// 获取预算计划列表
export function GetPlanlist(data) {
    return request({
        url: `/manager/plan/list`,
        method: 'post',
        data
    });
}

// 新增方案数据
export function addPlanData(data) {
    return request({
        url: `/manager/plan/add`,
        method: 'post',
        data
    });
}

// 获取调整计划列表
export function getAdjustData(data) {
    return request({
        url: `/manager/plan/adjustlist`,
        method: 'post',
        data
    });
}

// 新增调整
export function addadjust(id) {
    return request({
        url: `/manager/plan/adjust/${id}`,
        method: 'get'
    });
}
// 获取可以调整计划列表
export function getAdjustPlans(id) {
    return request({
        url: `/manager/plan/getAdjustPlans/${id}`,
        method: 'get'
    });
}

// 提交计划
export function submitplan(data) {
    return request({
        url: `/manager/plan/submitplan`,
        method: 'post',
        data
    });
}
// 提交调整
export function submitplanadjust(data) {
    return request({
        url: `/manager/plan/submitplanadjust`,
        method: 'post',
        data
    });
}
// 审核
export function process(data) {
    return request({
        url: `/manager/plan/process`,
        method: 'post',
        data
    });
}
// 调整审核
export function processadjust(data) {
    return request({
        url: `/manager/plan/processadjust`,
        method: 'post',
        data
    });
}

// 获取预算计划明细分页列表
export function GetPlanDetail(data) {
    return request({
        url: `/manager/plandetail/getlistbyparam`,
        method: 'post',
        data
    });
}

// 新增预算明细
export function AddPlanDetail(data) {
    return request({
        url: `/manager/plandetail/add`,
        method: 'post',
        data
    });
}
// 新增预算明细 费用表格部分
export function AddCondetail(data) {
    return request({
        url: `/manager/condetail/addlist/1`,
        method: 'post',
        data
    });
}

// 修改预算明细合同明细
export function EditCondetail(data) {
    return request({
        url: `/manager/condetail/edit`,
        method: 'put',
        data
    });
}

// 修改预算明细
export function EditPlanDetail(data) {
    return request({
        url: `/manager/plandetail/edit`,
        method: 'put',
        data
    });
}

// 删除预算明细
export function DelPlanDetail(id) {
    return request({
        url: `/manager/plandetail/delete/${id}`,
        method: 'delete',
    });
}

// 查询预算明细中的合同明细
export function GetCondetail(data) {
    return request({
        url: `/manager/condetail/list`,
        method: 'post',
        data
    });
}

// 更换公司类型
export function ChangeCompany(params) {
    return request({
        url: `/manager/plandetail/maintchange`,
        method: 'get',
        params
    });
}

// 获取单个预算明细列表
export function GetSinglePlanDetailList(id) {
  return request({
      url: `/manager/plan/funddetail/${id}`,
      method: 'get',
  });
}

// 获取审核历史
export function GetReviewList(id) {
  return request({
      url: `/manager/plan/getnodeinfos/${id}`,
      method: 'get',
  });
}


// 删除预算计划
export function DeletePlan(id) {
    return request({
        url: `/manager/plan/delete/${id}`,
        method: 'delete',
    });
}
