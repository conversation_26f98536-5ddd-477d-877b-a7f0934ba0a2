import request from '@/utils/request'

// 查询档墙列表
export function listRetainingWallRecords(data) {
  return request({
    url: '/baseData/retainingWall/getListPage',
    method: 'post',
    data:data
  })
}

// 查询档墙详细
export function getRetainingWall(detectionRecordsId) {
  return request({
    url: '/baseData/retainingWall/get/' + detectionRecordsId,
    method: 'get'
  })
}

// 新增档墙
export function addRetainingWall(data) {
  return request({
    url: '/baseData/retainingWall/add',
    method: 'post',
    data: data
  })
}

// 暂存档墙
export function tempaddRetainingWall(data) {
  return request({
    url: '/baseData/retainingWall/tempAdd',
    method: 'post',
    data: data
  })
}

// 修改档墙
export function updateRetainingWall(data) {
  return request({
    url: '/baseData/retainingWall/edit',
    method: 'put',
    data: data
  })
}

// 删除档墙
export function delRetainingWall(detectionRecordsIds) {
  return request({
    url: '/baseData/retainingWall/delete',
    method: 'delete',
    data:detectionRecordsIds
  })
}

// 获取最后一次档墙数据详细信息
export function getDetectionGetLast(bridgeStaticId) {
  return request({
    url: '/baseData/retainingWall/get/last/'+ bridgeStaticId,
    method: 'get',

  })
}

// /retainingWall/add/export
export function retainingWallExport(data) {
  return request({
    url: '/baseData/retainingWall/export',
    method: 'post',
    data: data
  })
}


