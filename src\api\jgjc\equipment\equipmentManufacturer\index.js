import request from '@/utils/request';

export function addManufacturer(data) {
  return request({
    url: '/jgjcadmin/api/basedata/manufacturer/save',
    method: 'post',
    data
  });
}

export function updateManufacturer(data) {
  return request({
    url: '/jgjcadmin/api/basedata/manufacturer/update',
    method: 'post',
    data
  });
}

export function deleteManufacturer(data) {
  return request({
    url: '/jgjcadmin/api/basedata/manufacturer/delete',
    method: 'post',
    data
  });
}

export function listManufacturers(data) {
  return request({
    url: '/jgjcadmin/api/basedata/manufacturer/page',
    method: 'post',
    data
  });
}

export function batchAddManufacturers(data) {
  return request({
    url: '/jgjcadmin/api/basedata/manufacturer/batchAdd',
    method: 'post',
    data
  });
}

export function getManufacturerSelectData(data) {
  return request({
    url: '/jgjcadmin/api/basedata/manufacturer/selectData',
    method: 'post',
    data
  });
}
