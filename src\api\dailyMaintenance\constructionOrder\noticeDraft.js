import request from '@/utils/request'

// 查询日养施工通知单拟定列表(分页)
export function list(data) {
  return request({
    url: '/manager/construction/list',
    method: 'post',
    data: data
  });
}

// 查询日养施工通知单拟定详情
export function getConstructionDetail(id) {
  return request({
    url: `/manager/construction/getConstructionDetail/${id}`,
    method: 'get'
  });
}

// 新增日养施工通知单拟定
export function addNoticeDraft(data) {
  return request({
    url: '/manager/construction/add',
    method: 'post',
    data: data
  });
}

// 施工单复制
export function copyNoticeDraft(data) {
  return request({
    url: '/manager/construction/copy',
    method: 'post',
    data: data
  });
}

// 修改日养施工通知单拟定
export function editNoticeDraft(data) {
  return request({
    url: '/manager/construction/edit',
    method: 'put',
    data: data
  });
}

// 删除日养施工通知单拟定
export function removeNoticeDraft(id) {
  return request({
    url: `/manager/construction/delete/${id}`,
    method: 'delete'
  });
}

// 批量删除日养施工通知单拟定
export function removeBatchNoticeDraft(ids) {
  return request({
    url: '/manager/construction/removeBatchByIds',
    method: 'delete',
    data: ids
  });
}

// 提交
export function submitNoticeDraft(data) {
  return request({
    url: '/manager/construction/submit',
    method: 'delete',
    data: data
  });
}

// 导出日养施工通知单拟定列表
export function exportListNoticeDraft() {
  return request({
    url: '/manager/construction/export',
    method: 'post'
  });
}


// 事件明细
export function eventInfoByConId(data) {
  return request({
    url: '/manager/detail/list',
    method: 'post',
    data: data
  });
}


// 附件查询
export function listFiles(data) {
  return request({
    url: '/manager/file/noticeDraft',
    method: 'post',
    data: data
  });
}

// 新增附件
export function addFiles(data) {
  return request({
    url: '/manager/file/add',
    method: 'post',
    data: data
  });
}

// 删除附件
export function deleteFiles(id) {
  return request({
    url: `/manager/file/delete/${id}`,
    method: 'delete'
  });
}


// 日养施工通知单审核节点信息
export function nodeInfo(data) {
  return request({
    url: '/manager/construction/node/info',
    method: 'post',
    data: data
  });
}


// 施工单预览
export function getPreviewUrl(id, isSupDomain) {
  return request({
    url: `/manager/construction/preview/${id}${isSupDomain ? '?isSupDomain=' + isSupDomain : '?isSupDomain=否'}`,
    method: 'get'
  });
}


// 新增日养施工通知单拟定
export function updateConstruction(data) {
  return request({
    url: '/manager/construction/updateConstruction',
    method: 'put',
    data: data
  });
}


// 删除附件
export function deleteEvent(id) {
  return request({
    url: `/manager/construction/deleteConstructionDisease/${id}`,
    method: 'delete'
  });
}
