import request from '@/utils/request'

// 查询合同列表
export function getList(query) {
  return request({
    url: '/manager/contract/list',
    method: 'post',
    data: query,
    notVerifyDuplicates: true
  })
}

// 查询所有合同列表
export function getListAll(query) {
  return request({
    url: '/manager/contract/listAll',
    method: 'post',
    data: query,
    notVerifyDuplicates: true
  })
}


// 新增数据
export function addContract(data) {
  return request({
    url: '/manager/contract/add',
    method: 'post',
    data: data
  })
}

// 修改数据
export function saveContract(data) {
  return request({
    url: '/manager/contract/edit',
    method: 'put',
    data: data
  })
}

// 查询详情
export function getDetail(id) {
  return request({
    url: `/manager/contract/getContractDetail/${id}`,
    method: 'get'
  })
}

// 删除合同数据
export function deleteContract(ids) {
  return request({
    url: '/manager/contract/removeBatchByIds',
    method: 'delete',
    data: ids
  })
}

// 查询附件列表
export function listFiles(query) {
  return request({
    url: '/manager/annex/list',
    method: 'post',
    data: query
  })
}

// 删除附件
export function deleteFiles(ids) {
  return request({
    url: '/manager/annex/delete',
    method: 'delete',
    data: ids
  })
}

// 新增附件
export async function addFiles(data) {
  return request({
    url: '/manager/annex/add',
    method: 'post',
    data: data
  })
}


