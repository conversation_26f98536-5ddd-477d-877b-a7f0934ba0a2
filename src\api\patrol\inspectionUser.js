import request from '@/utils/request'

// 查询巡查人员关联列表
export function listInspectionUser(query) {
  return request({
    url: '/patrol/inspectionUser/list',
    method: 'get',
    params: query
  })
}

// 查询巡查人员关联详细
export function getInspectionUser(id) {
  return request({
    url: '/patrol/inspectionUser/get/' + id,
    method: 'get'
  })
}

// 新增巡查人员关联
export function addInspectionUser(data) {
  return request({
    url: '/patrol/inspectionUser/add',
    method: 'post',
    data: data
  })
}

// 修改巡查人员关联
export function updateInspectionUser(data) {
  return request({
    url: '/patrol/inspectionUser/edit',
    method: 'put',
    data: data
  })
}

// 删除巡查人员关联
export function delInspectionUser(id) {
  return request({
    url: '/patrol/inspectionUser/delete/' + id,
    method: 'delete'
  })
}
