import request from '@/utils/request'

// J01报表
export function j01report(repositoryDTO) {
	return request({
		url: '/manager/budget/report/j01',
		method: 'post',
		data: repositoryDTO
	});
}

// J02报表
export function j02report(repositoryDTO) {
	return request({
		url: '/manager/budget/report/j02',
		method: 'post',
		data: repositoryDTO
	});
}

// J04报表
export function j04report(repositoryDTO) {
	return request({
		url: '/manager/budget/report/j04',
		method: 'post',
		data: repositoryDTO
	});
}

// J05报表
export function j05report(repositoryDTO) {
	return request({
		url: '/manager/budget/report/j05',
		method: 'post',
		data: repositoryDTO
	});
}

// J06报表
export function j06report(repositoryDTO) {
	return request({
		url: '/manager/budget/report/j06',
		method: 'post',
		data: repositoryDTO
	});
}

// J07报表
export function j07report(repositoryDTO) {
	return request({
		url: '/manager/budget/report/j07',
		method: 'post',
		data: repositoryDTO
	});
}
