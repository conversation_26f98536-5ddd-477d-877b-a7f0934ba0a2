import request from '@/utils/request'

// 查询报价体系子目数据列表
export function listLibschprice(query) {
  return request({
    url: '/patrol/libschprice/list',
    method: 'get',
    params: query
  })
}

// 查询报价体系子目数据详细
export function getLibschprice(id) {
  return request({
    url: '/patrol/libschprice/get/' + id,
    method: 'get'
  })
}

// 新增报价体系子目数据
export function addLibschprice(data) {
  return request({
    url: '/patrol/libschprice/add',
    method: 'post',
    data: data
  })
}

// 修改报价体系子目数据
export function updateLibschprice(data) {
  return request({
    url: '/patrol/libschprice/edit',
    method: 'put',
    data: data
  })
}

// 删除报价体系子目数据
export function delLibschprice(id) {
  return request({
    url: '/patrol/libschprice/delete/' + id,
    method: 'delete'
  })
}
