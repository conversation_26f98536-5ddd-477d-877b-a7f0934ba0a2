import request from '@/utils/request'

// 查询单位人员持证情况列表
export function listLaborcertificate(query) {
  return request({
    url: '/manager/laborcertificate/list',
    method: 'get',
    params: query
  })
}

// 查询单位人员持证情况详细
export function getLaborcertificate(id) {
  return request({
    url: '/manager/laborcertificate/get/' + id,
    method: 'get'
  })
}

// 新增单位人员持证情况
export function addLaborcertificate(data) {
  return request({
    url: '/manager/laborcertificate/add',
    method: 'post',
    data: data
  })
}

// 修改单位人员持证情况
export function updateLaborcertificate(data) {
  return request({
    url: '/manager/laborcertificate/edit',
    method: 'put',
    data: data
  })
}

// 删除单位人员持证情况
export function delLaborcertificate(id) {
  return request({
    url: '/manager/laborcertificate/delete/' + id,
    method: 'delete'
  })
}
