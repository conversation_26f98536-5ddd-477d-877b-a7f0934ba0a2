import request from '@/utils/request'

// 查询部门养护路段关联列表
export function listMaintenance(query) {
  return request({
    url: '/system/maintenance/list',
    method: 'get',
    params: query
  })
}

// 查询部门养护路段树形列表
export function findTreeList(deptId) {
  return request({
    url: '/system/maintenance/findTreeList/' + deptId,
    method: 'get'
  })
}

// 查询部门养护路段关联详细
export function getMaintenance(deptId) {
  return request({
    url: '/system/maintenance/' + deptId,
    method: 'get'
  })
}

// 新增部门养护路段关联
export function addMaintenance(data) {
  return request({
    url: '/system/maintenance',
    method: 'post',
    data: data
  })
}

// 修改部门养护路段关联
export function updateMaintenance(data) {
  return request({
    url: '/system/maintenance',
    method: 'put',
    data: data
  })
}

// 删除部门养护路段关联
export function delMaintenance(deptId) {
  return request({
    url: '/system/maintenance/' + deptId,
    method: 'delete'
  })
}

// 删除部门养护路段关联
export function delteDeptMaintenance(param) {
  return request({
    url: '/system/maintenance/delteDeptMaintenance',
    method: 'get',
    params: param
  })
}

// 保存关联子属部门数据
export function addDeptMaintenance(param) {
  return request({
    url: '/system/maintenance/addDeptMaintenance',
    method: 'post',
    data: param
  })
}

