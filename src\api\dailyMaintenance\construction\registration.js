import request from '@/utils/request'

// 结构树
export function getUserTreeStructure(data) {
    return request({
        url: '/manager/register/getUserTreeStructure',
        method: 'post',
        data: data
    });
}

// 查询施工登记列表(分页)
export function list(data) {
    return request({
        url: '/manager/register/list',
        method: 'post',
        data: data
    });
}

// 查询施工登记列表(不分页)
export function listAll() {
    return request({
        url: '/manager/register/listAll',
        method: 'get'
    });
}

// 根据id查询施工登记数据
export function getRegisterDetail(id) {
    return request({
        url: `/manager/register/getRegister/${id}`,
        method: 'get'
    });
}

// 根据id查询施工单事件明细
export function getDisDetail(id) {
    return request({
        url: `/manager/register/getDisDetail/${id}`,
        method: 'get'
    });
}

// 保存
export function save(data) {
    return request({
        url: '/manager/register/save',
        method: 'put',
        data: data
    });
}

// 施工登记提交
export function review(data) {
    return request({
        url: '/manager/register/review',
        method: 'put',
        data: data
    });
}

// 暂不处理
export function notYetDispose(data) {
    return request({
        url: '/manager/register/notYetDispose',
        method: 'put',
        data: data
    });
}

// 修改施工登记
export function edit(data) {
    return request({
        url: '/manager/register/edit',
        method: 'put',
        data: data
    });
}

// 导出施工登记列表
export function exportList() {
    return request({
        url: '/manager/register/export',
        method: 'post'
    });
}

// 查询施工单列表
export function pageList(data) {
  return request({
    url: '/manager/register/pageList',
    method: 'post',
    data
  });
}

// 日养施工通知单事件节点信息
export function disNodeInfo(data) {
    return request({
        url: '/manager/register/node/info',
        method: 'post',
        data: data
    });
}

// 根据病害id获取完工登记id
export function getFinishedId(id) {
  return request({
    url: `/manager/construction/getFinishedId/${id}`,
    method: 'get',
  });
}

// 根据病害id获取施工登记id
export function getRegisterId(id) {
  return request({
    url: `/manager/construction/getRegisterId/${id}`,
    method: 'get',
  });
}
