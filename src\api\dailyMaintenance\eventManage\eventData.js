import request from '@/utils/request'

// 查询病害数据列表（不分页）
export function listDiseaseData() {
  return request({
    url: '/manager/disease/listAll',
    method: 'get'
  });
}

// 查询病害数据列表（分页）
export function findDiseaseDataList(query) {
  return request({
    url: '/manager/disease/list',
    method: 'post',
    data: query,
    notVerifyDuplicates: true
  });
}

// 根据id查询病害数据
export function getDiseaseDataById(id) {
  return request({
    url: `/manager/disease/get/${id}`,
    method: 'get'
  });
}

// 新增病害数据
export function addDiseaseData(data) {
  return request({
    url: '/manager/disease/add',
    method: 'post',
    data: data
  });
}

// 修改病害数据
export function updateDiseaseData(data) {
  return request({
    url: '/manager/disease/edit',
    method: 'put',
    data: data
  });
}

// 删除病害数据
export function deleteDiseaseData(id) {
  return request({
    url: `/manager/disease/delete/${id}`,
    method: 'delete'
  });
}

// 批量删除病害数据
export function batchDeleteDiseaseData(ids) {
  return request({
    url: '/manager/disease/removeBatchByIds',
    method: 'delete',
    data: ids
  });
}


// 提交到被损被盗
export function saveToTheft(data) {
  return request({
    url: '/manager/disease/saveToTheft',
    method: 'post',
    data: data
  });
}

// 查询病害数据列表（分页）
export function findRoaddiseaseList(query) {
  return request({
    url: '/manager/roaddisease/list',
    method: 'post',
    data: query,
    notVerifyDuplicates: true
  });
}
