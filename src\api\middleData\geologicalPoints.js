import request from '@/utils/request'

// 查询路域外地质灾害隐患点清单列表
export function listGeologicalPoints(query) {
  return request({
    url: '/middleData/geologicalPoints/list',
    method: 'get',
    params: query
  })
}

// 查询路域外地质灾害隐患点清单详细
export function getGeologicalPoints(id) {
  return request({
    url: '/middleData/geologicalPoints/get/' + id,
    method: 'get'
  })
}

// 新增路域外地质灾害隐患点清单
export function addGeologicalPoints(data) {
  return request({
    url: '/middleData/geologicalPoints/add',
    method: 'post',
    data: data
  })
}

// 修改路域外地质灾害隐患点清单
export function updateGeologicalPoints(data) {
  return request({
    url: '/middleData/geologicalPoints/edit',
    method: 'put',
    data: data
  })
}

// 删除
export function delData(data) {
  return request({
    url: `/middleData/geologicalPoints/delete`,
    method: 'delete',
    data: data.idList,
  });
}

// 批量审核
export function batchAudit(data) {
  return request({
    url: '/middleData/geologicalPoints/batchAudit',
    method: 'post',
    data: data
  })
}


