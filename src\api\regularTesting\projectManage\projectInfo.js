import request from '@/utils/request'

// 定期检测项目信息列表
export function queryList(data) {
  return request({
    url: '/manager/check/project/list',
    method: 'post',
    data,
  });
}

// 新增定检项目
export function addProject(data) {
  return request({
    url: '/manager/check/project/add',
    method: 'post',
    data,
  });
}


// 修改定检项目
export function editProject(data) {
  return request({
    url: '/manager/check/project/edit',
    method: 'put',
    data,
  });
}


// 更新项目状态
export function updateProjectStatus(data) {
  return request({
    url: '/manager/check/project/edit/status',
    method: 'put',
    data,
  });
}

// 删除定检项目
export function deleteProject(id) {
  return request({
    url: `/manager/check/project/delete/${id}`,
    method: 'delete',
  });
}

// 查询项目资料
export function projectFileListAll(query) {
  return request({
    url: `/manager/check/project/file/listAll`,
    method: 'get',
    params: query
  });
}
