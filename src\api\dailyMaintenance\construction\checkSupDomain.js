import request from '@/utils/request'


/**
 * 查询施工单位审核列表（分页）
 * @param {Object} data - 请求参数
 * @returns {Promise} - 返回 Promise 对象
 */
export function queryConstructionAuditList(data) {
  return request({
    url: '/manager/checkSupDomain/pageList',
    method: 'post',
    data: data
  });
}

/**
 * 查询施工单位审核列表（分页）
 * @param {Object} data - 请求参数
 * @returns {Promise} - 返回 Promise 对象
 */
export function queryConstructionAuditListPaged(data) {
  return request({
    url: '/manager/checkSupDomain/list',
    method: 'post',
    data: data
  });
}

/**
 * 查询施工单位审核列表（不分页）
 * @returns {Promise} - 返回 Promise 对象
 */
export function queryConstructionAuditListAll() {
  return request({
    url: '/manager/checkSupDomain/listAll',
    method: 'get'
  });
}

/**
 * 根据 ID 查询施工单位审核数据
 * @param {string} id - 施工单位审核 ID
 * @returns {Promise} - 返回 Promise 对象
 */
export function getConstructionAuditById(id) {
  return request({
    url: `/manager/checkSupDomain/get/${id}`,
    method: 'get'
  });
}

/**
 * 根据 ID 查询施工单事件明细
 * @param {string} id - 施工单位审核 ID
 * @returns {Promise} - 返回 Promise 对象
 */
export function getConstructionDetailById(id) {
  return request({
    url: `/manager/checkSupDomain/getDisDetail/${id}`,
    method: 'get'
  });
}

/**
 * 保存施工单位审核数据
 * @param {Object} data - 施工单位审核数据
 * @returns {Promise} - 返回 Promise 对象
 */
export function saveConstructionAudit(data) {
  return request({
    url: '/manager/checkSupDomain/save',
    method: 'put',
    data: data
  });
}

/**
 * 提交施工单位审核数据
 * @param {Object} data - 施工单位审核数据
 * @returns {Promise} - 返回 Promise 对象
 */
export function submitConstructionAudit(data) {
  return request({
    url: '/manager/checkSupDomain/review',
    method: 'put',
    data: data
  });
}

/**
 * 驳回施工单位审核数据
 * @param {Object} data - 施工单位审核数据
 * @returns {Promise} - 返回 Promise 对象
 */
export function rejectConstructionAudit(data) {
  return request({
    url: '/manager/checkSupDomain/reject',
    method: 'put',
    data: data
  });
}

/**
 * 导出施工单位审核列表
 * @param {Object} data - 请求参数
 * @returns {Promise} - 返回 Promise 对象
 */
export function exportConstructionAuditList(data) {
  return request({
    url: '/manager/checkSupDomain/export',
    method: 'post',
    data: data,
    responseType: 'blob' // 设置响应类型为 blob 以处理文件下载
  });
}
