import request from '@/utils/request'

// 查询设备类型树
export function getLabordevicetypeTree() {
  return request({
    url: '/manager/labordevicetype/getTree',
    method: 'get'
  })
}


// 查询设备类型列表
export function listLabordevicetype(query) {
  return request({
    url: '/manager/labordevicetype/list',
    method: 'get',
    params: query
  })
}

// 查询设备类型详细
export function getLabordevicetype(id) {
  return request({
    url: '/manager/labordevicetype/get/' + id,
    method: 'get'
  })
}

// 新增设备类型
export function addLabordevicetype(data) {
  return request({
    url: '/manager/labordevicetype/add',
    method: 'post',
    data: data
  })
}

// 修改设备类型
export function updateLabordevicetype(data) {
  return request({
    url: '/manager/labordevicetype/edit',
    method: 'put',
    data: data
  })
}

// 删除设备类型
export function delLabordevicetype(id) {
  return request({
    url: '/manager/labordevicetype/delete/' + id,
    method: 'delete'
  })
}
