import request from '@/utils/request'

// 查询灾害风险路段列表
export function listRiskSections(query) {
  return request({
    url: '/middleData/xqRiskSections/list',
    method: 'post',
    params: {pageNum: query.pageNum, pageSize: query.pageSize},
    data: query
  })
}

// 查询灾害风险路段详细
export function getRiskSections(id) {
  return request({
    url: '/middleData/xqRiskSections/get/' + id,
    method: 'get'
  })
}

// 新增灾害风险路段
export function addRiskSections(data) {
  return request({
    url: '/middleData/xqRiskSections/add',
    method: 'post',
    data: data
  })
}

// 修改灾害风险路段
export function updateRiskSections(data) {
  return request({
    url: '/middleData/xqRiskSections/edit',
    method: 'put',
    data: data
  })
}

// 删除
export function delData(data) {
  return request({
    url: `/middleData/xqRiskSections/delete`,
    method: 'delete',
    data: data.idList,
  });
}

// 批量审核
export function batchAudit(data) {
  return request({
    url: '/middleData/xqRiskSections/batchAudit',
    method: 'post',
    data: data
  })
}
