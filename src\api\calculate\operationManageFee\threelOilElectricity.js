import request from '@/utils/request'

// 查询三大系统发电油费列表(分页)
export function listOilcostPage(data) {
    return request({
        url: 'manager/operate/oilcost/list',
        method: 'post',
        data
    });
}

// 查询三大系统发电油费列表(不分页)
export function listOilcostAll() {
    return request({
        url: 'manager/operate/oilcost/listAll',
        method: 'get'
    });
}

// 根据id查询三大系统发电油费数据
export function getOilcostById(id) {
    return request({
        url: `manager/operate/oilcost/get/${id}`,
        method: 'get'
    });
}

// 新增三大系统发电油费
export function addOilcost(data) {
    return request({
        url: 'manager/operate/oilcost/add',
        method: 'post',
        data
    });
}

// 修改三大系统发电油费
export function editOilcost(data) {
    return request({
        url: 'manager/operate/oilcost/edit',
        method: 'put',
        data
    });
}

// 删除三大系统发电油费
export function deleteOilcost(id) {
    return request({
        url: `manager/operate/oilcost/delete/${id}`,
        method: 'delete'
    });
}
