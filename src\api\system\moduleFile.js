import request from '@/utils/request'

export function getListByEntity(params) {
    return request({
        url: '/system/sysModuleFile/getListByEntity',
        method: 'post',
        data: params
    })
}
export function saveModuleFile(params) {
    return request({
        url: '/system/sysModuleFile/saveModule',
        method: 'post',
        data: params
    })
}
export function deleteModule(id) {
    return request({
        url: '/system/sysModuleFile/deleteModule/' + id,
        method: 'post'
    })
}

export function selectVoByTypeId(typeId) {
    return request({
        url: '/system/sysModuleFile/selectVoByTypeId/' + typeId,
        method: 'get',
    })
}

// 获取各管理处图纸文件数量
export function getModuleFileCount(params) {
    return request({
        url: `/system/sysModuleFileDetails/count`,
        method: 'get',
        params
    })
}

// 修改是否有图纸状态
export function isExistFileDraw(data) {
    return request({
        url: `/baseData/assetInfo/isExistFileDraw`,
        method: 'post',
        data: data
    })
}