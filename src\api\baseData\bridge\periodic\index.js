import request from '@/utils/request'

// 查询桥梁定期检查列表
export function listBridgePeriodicRecords(data) {
  return request({
    url: '/baseData/bridge/periodic/detection/getListPage',
    method: 'post',
    data
  })
}

// 导出
export function periodicExport(data) {
  return request({
    url: '/baseData/bridge/periodic/export',
    method: 'get',
    data: data
  })
}

// 复核
export function detectionRecheck(data) {
  return request({
    url: '/baseData/bridge/periodic/detection/recheck',
    method: 'put',
    data: data
  })
}

// 复核回退
export function detectionRollback(data) {
  return request({
    url: '/baseData/bridge/periodic/detection/rollback',
    method: 'put',
    data: data.periodicDetectionIds
  })
}

// 新增桥梁特殊检查
export function addPeriodicDetectionBridge(data) {
  return request({
    url: '/baseData/bridge/periodic/detection/add',
    method: 'post',
    data: data
  })
}



// 修改桥梁特殊检查
export function updatePeriodicDetectionBridge(data) {
  return request({
    url: '/baseData/bridge/periodic/detection/edit',
    method: 'put',
    data: data
  })
}

// 删除桥梁特殊检查
export function delPeriodicDetectionBridge(data) {
  return request({
    url: '/baseData/bridge/periodic/detection/delete',
    method: 'delete',
    data
  })
}


///bridge/periodic/detection/get
// 查询桥梁特殊检查
export function getPeriodicDetectionBridge(params) {
  return request({
    url: '/baseData/bridge/periodic/detection/get/'+params,
    method: 'get',
  })
}



//导入评定结果附件
export function periodicAdd(data) {
  return request({
    url: '/baseData/bridge/periodic/detection/import',
    method: 'post',
    data: data
  })
}