// 图层个性化设置 - 接口
import request from '@/utils/request'

/**
 * 新增图层个性化设置
 * @param {*} data 
 * @returns 
 */
export function addMenuSubSet(data) {
  return request({
    url: '/oneMap/menuSubSet/add',
    method: 'post',
    data
  })
}
/**
 * 修改图层个性化设置
 * @param {*} data 
 * @returns 
 */
export function updateMenuSubSet(data) {
  return request({
    url: '/oneMap/menuSubSet/edit',
    method: 'post',
    data
  })
}

/**
 * 根据id删除图层个性化设置
 * @param {*} ids 
 * @returns 
 */
export function delMenuSubSet(ids) {
  return request({
    url: '/oneMap/menuSubSet/delete',
    method: 'delete',
    data: ids
  })
}

/**
 * 条件分页查询图层个性化设置列表
 * @param {*} params 
 * @returns 
 */
export function getSubSetList(params) {
  return request({
    url: '/oneMap/menuSubSet/getListPage',
    method: 'get',
    params
  })
}

/**
 * 获取图层个性化设置详细信息
 * @param {*} id 
 * @returns 
 */
export function getMenuSubSet(id) {
  return request({
    url: '/oneMap/menuSubSet/getInfoById',
    method: 'get',
    params: {id}
  })
}