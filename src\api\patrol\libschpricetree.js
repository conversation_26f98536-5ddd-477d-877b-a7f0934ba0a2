import request from '@/utils/request'

// 查询合同数据列表
export function listLibschpricetree(query) {
  return request({
    url: '/patrol/libschpricetree/list',
    method: 'get',
    params: query
  })
}

// 查询合同数据详细
export function getLibschpricetree(id) {
  return request({
    url: '/patrol/libschpricetree/get/' + id,
    method: 'get'
  })
}

// 新增合同数据
export function addLibschpricetree(data) {
  return request({
    url: '/patrol/libschpricetree/add',
    method: 'post',
    data: data
  })
}

// 修改合同数据
export function updateLibschpricetree(data) {
  return request({
    url: '/patrol/libschpricetree/edit',
    method: 'put',
    data: data
  })
}

// 删除合同数据
export function delLibschpricetree(id) {
  return request({
    url: '/patrol/libschpricetree/delete/' + id,
    method: 'delete'
  })
}
