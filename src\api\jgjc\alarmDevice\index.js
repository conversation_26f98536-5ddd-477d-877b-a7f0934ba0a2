import request from '@/utils/request'

// 分页查询报警器设备列表
export function listVoiceAlarm(query) {
  return request({
    url: '/jgjcadmin/api/voiceAlarm/page',
    method: 'get',
    params: query
  })
}

// 新增报警器设备
export function saveVoiceAlarm(data) {
  return request({
    url: '/jgjcadmin/api/voiceAlarm/save',
    method: 'post',
    data: data
  })
}

// 修改报警器设备
export function updateVoiceAlarm(data) {
  return request({
    url: '/jgjcadmin/api/voiceAlarm/update',
    method: 'post',
    data: data
  })
}

// 删除报警器设备
export function deleteVoiceAlarm(data) {
  return request({
    url: '/jgjcadmin/api/voiceAlarm/delete',
    method: 'post',
    data: data
  })
}

// 开启声光报警器设备
export function startVoiceAlarm(data) {
  return request({
    url: '/jgjcadmin/api/voiceAlarm/startVoiceAlarm',
    method: 'post',
    data: data
  })
}

// 关闭声光报警器设备
export function endVoiceAlarm(data) {
  return request({
    url: '/jgjcadmin/api/voiceAlarm/endVoiceAlarm',
    method: 'post',
    data: data
  })
}

// 延时播报声光报警器设备
export function delayVoiceAlarm(data) {
  return request({
    url: '/jgjcadmin/api/voiceAlarm/delayVoiceAlarm',
    method: 'post',
    data: data
  })
}
