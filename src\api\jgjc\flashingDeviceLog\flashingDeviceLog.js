import request from '@/utils/request'

// 查询
export function listLog(data) {
  return request({
    url: '/jgjcadmin/api/flash/flashdevicelog/page',
    method: 'post',
    data
  })
}

// 删除
export function delLog(data) {
  return request({
    url: '/jgjcadmin/api/flash/flashdevicelog/delete',
    method: 'post',
    data
  })
}

// 查询
export function listLogDetail(data) {
  return request({
    url: '/jgjcadmin/api/flash/flashdevicelog/pageDetail',
    method: 'post',
    data
  })
}

// 删除
export function delLogDetail(data) {
  return request({
    url: '/jgjcadmin/api/flash/flashdevicelog/deleteDetail',
    method: 'post',
    data
  })
}
