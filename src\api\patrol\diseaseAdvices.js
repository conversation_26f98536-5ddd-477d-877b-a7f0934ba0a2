import request from '@/utils/request'

// 查询病害处置列表
export function listDiseaseAdvices(query) {
  return request({
    url: '/patrol/diseaseAdvices/list',
    method: 'get',
    params: query
  })
}

// 查询病害处置详细
export function getDiseaseAdvices(id) {
  return request({
    url: '/patrol/diseaseAdvices/get/' + id,
    method: 'get'
  })
}

// 新增病害处置
export function addDiseaseAdvices(data) {
  return request({
    url: '/patrol/diseaseAdvices/add',
    method: 'post',
    data: data
  })
}

// 修改病害处置
export function updateDiseaseAdvices(data) {
  return request({
    url: '/patrol/diseaseAdvices/edit',
    method: 'put',
    data: data
  })
}

// 删除病害处置
export function delDiseaseAdvices(id) {
  return request({
    url: '/patrol/diseaseAdvices/delete/' + id,
    method: 'delete'
  })
}
