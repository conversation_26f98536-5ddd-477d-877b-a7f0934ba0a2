import request from '@/utils/request'

// 查询涉灾隐患点-边坡列表
export function listSlopeHazards(query) {
  return request({
    url: '/middleData/slopeHazards/list',
    method: 'post',
    params: {pageNum: query.pageNum, pageSize: query.pageSize},
    data: query
  })
}

// 查询涉灾隐患点-边坡详细
export function getSlopeHazards(id) {
  return request({
    url: '/middleData/slopeHazards/get/' + id,
    method: 'get'
  })
}

// 新增涉灾隐患点-边坡
export function addSlopeHazards(data) {
  return request({
    url: '/middleData/slopeHazards/add',
    method: 'post',
    data: data
  })
}

// 修改涉灾隐患点-边坡
export function updateSlopeHazards(data) {
  return request({
    url: '/middleData/slopeHazards/edit',
    method: 'put',
    data: data
  })
}

// 删除
export function delData(data) {
  return request({
    url: `/middleData/slopeHazards/delete`,
    method: 'delete',
    data: data.idList,
  });
}

// 批量审核
export function batchAudit(data) {
  return request({
    url: '/middleData/slopeHazards/batchAudit',
    method: 'post',
    data: data
  })
}
