import request from '@/utils/request'

// 查询图层地图服务设置列表
export function getListPage(query) {
  return request({
    url: '/oneMap/baseLayer/getListPage',
    method: 'get',
    params: query,
    notVerifyDuplicates: true
  })
}

// 查询图层地图服务设置详细
export function getBaseLayerById(id) {
  return request({
    url: '/oneMap/baseLayer/getInfoById',
    method: 'get',
    params: {id},
    notVerifyDuplicates: true
  })
}

// 新增图层地图服务设置
export function addBaseLayer(data) {
  return request({
    url: '/oneMap/baseLayer/add',
    method: 'post',
    data,
    notVerifyDuplicates: true
  })
}

// 修改图层地图服务设置
export function updateBaseLayer(data) {
  return request({
    url: '/oneMap/baseLayer/edit',
    method: 'post',
    data,
    notVerifyDuplicates: true
  })
}

// 删除图层地图服务设置
export function delBaseLayer(ids) {
  return request({
    url: '/oneMap/baseLayer/delete',
    method: 'delete',
    data: ids,
    notVerifyDuplicates: true
  })
}
