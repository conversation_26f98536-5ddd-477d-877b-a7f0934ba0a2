import request from '@/utils/request'

// 查询日养施工通知单现场审核列表(分页)
export function queryList(data) {
    return request({
        url: '/manager/constructionSiteReview/list',
        method: 'post',
        data: data
    });
}

// 查询日养施工通知单现场审核详情
export function getConstructionDetail(id) {
    return request({
        url: `/manager/constructionSiteReview/getConstructionDetail/${id}`,
        method: 'get'
    });
}

// 审核
export function reviewExamine(data) {
    return request({
        url: '/manager/constructionSiteReview/review',
        method: 'put',
        data: data
    });
}

// 批量审核
export function batchReviewExamine(data) {
    return request({
        url: '/manager/constructionSiteReview/batchReview',
        method: 'put',
        data: data
    });
}

// 驳回
export function rejectExamine(data) {
    return request({
        url: '/manager/constructionSiteReview/reject',
        method: 'put',
        data: data
    });
}

// 批量驳回
export function batchRejectExamine(data) {
    return request({
        url: '/manager/constructionSiteReview/batchReject',
        method: 'put',
        data: data
    });
}

// 导出日养施工通知单审核列表
export function exportListExamine() {
    return request({
        url: '/manager/constructionSiteReview/export',
        method: 'post'
    });
}
