import request from '@/utils/request'
// 查询采样方案列表
export function listSampleProject(query) {
  return request({
    url: '/jgjc-manager/api/console/jc/sampleProject/findPage',
    method: 'post',
    data: query
  })
}
// 查询采样方案详细
export function getSampleProject(id) {
  return request({
    url: '/jgjc-manager/api/console/jc/sampleProject/findById?id=' + id,
    method: 'get'
  })
}
// 新增采样方案
export function addSampleProject(data) {
  return request({
    url: '/jgjc-manager/api/console/jc/sampleProject/create',
    method: 'post',
    data: data
  })
}
// 修改采样方案
export function updateSampleProject(data) {
  return request({
    url: '/jgjc-manager/api/console/jc/sampleProject/modify',
    method: 'post',
    data: data
  })
}
// 删除采样方案
export function delSampleProject(id) {
  return request({
    url: '/jgjc-manager/api/console/jc/sampleProject/removeById?id=' + id,
    method: 'delete'
  })
}
// 批量删除采样方案
export function delSampleProjectBatch(ids) {
  return request({
    url: '/jgjc-manager/api/console/jc/sampleProject/batchRemove',
    method: 'post',
    data: ids
  })
}
// 获取所有采样方案
export function getFindAllSampleProject() {
  return request({
    url: '/jgjc-manager/api/console/jc/sampleProject/findAll',
    method: 'get'
  })
}
