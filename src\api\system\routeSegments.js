import request from '@/utils/request'

// 查询养护子段管理列表
export function listRouteSegments(query) {
  return request({
    url: '/system/routeSegments/list',
    method: 'get',
    params: query
  })
}

// 查询总养护里程
export function getTotalMileage(query) {
  return request({
    url: '/system/routeSegments/getTotalMileage',
    method: 'get',
    params: query
  })
}

// 查询养护子段管理列表(全量)
export function listAllRouteSegments(query) {
  return request({
    url: '/system/routeSegments/listAll',
    method: 'get',
    params: query
  })
}

// 查询子段列表
export function findByDeptSegmentsList(query) {
  return request({
    url: '/system/routeSegments/findByDeptSegmentsList',
    method: 'get',
    params: query
  })
}

// 根据条件查询已关联子段数据
export function getDefaultSelected(query) {
  return request({
    url: '/system/routeSegments/getDefaultSelected',
    method: 'get',
    params: query
  })
}

// 查询养护子段管理详细
export function getRouteSegments(routeSegmentsId) {
  return request({
    url: '/system/routeSegments/' + routeSegmentsId,
    method: 'get'
  })
}

// 新增养护子段管理
export function addRouteSegments(data) {
  return request({
    url: '/system/routeSegments',
    method: 'post',
    data: data
  })
}

// 修改养护子段管理
export function updateRouteSegments(data) {
  return request({
    url: '/system/routeSegments',
    method: 'put',
    data: data
  })
}

// 删除养护子段管理
export function delRouteSegments(routeSegmentsId) {
  return request({
    url: '/system/routeSegments/' + routeSegmentsId,
    method: 'delete'
  })
}
