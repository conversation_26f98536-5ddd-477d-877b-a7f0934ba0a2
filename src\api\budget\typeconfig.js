import request from '@/utils/request'

// 查询预算类型配置列表
export function listTypeconfig(query) {
  return request({
    url: '/budget/typeconfig/list',
    method: 'get',
    params: query
  })
}

// 查询预算类型配置详细
export function getTypeconfig(id) {
  return request({
    url: '/budget/typeconfig/get/' + id,
    method: 'get'
  })
}

// 新增预算类型配置
export function addTypeconfig(data) {
  return request({
    url: '/budget/typeconfig/add',
    method: 'post',
    data: data
  })
}

// 修改预算类型配置
export function updateTypeconfig(data) {
  return request({
    url: '/budget/typeconfig/edit',
    method: 'put',
    data: data
  })
}

// 删除预算类型配置
export function delTypeconfig(id) {
  return request({
    url: '/budget/typeconfig/delete/' + id,
    method: 'delete'
  })
}
