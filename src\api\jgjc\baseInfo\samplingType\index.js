import request from '@/utils/request';
export function addSampleType(data) {
	return request({
		url: '/jgjcadmin/api/basedata/sampletype/save',
		method: 'post',
		data
	});
}

export function updateSampleType(data) {
	return request({
		url: '/jgjcadmin/api/basedata/sampletype/update',
		method: 'post',
		data
	});
}

export function deleteSampleType(data) {
	return request({
		url: '/jgjcadmin/api/basedata/sampletype/delete',
		method: 'post',
		data
	});
}

export function listSampleTypes(data) {
	return request({
		url: '/jgjcadmin/api/basedata/sampletype/page',
		method: 'post',
		data,
    notVerifyDuplicates: true
	});
}

export function batchAddSampleTypes(data) {
	return request({
		url: '/jgjcadmin/api/basedata/sampletype/batchAdd',
		method: 'post',
		data
	});
}

export function getSampleTypeSelectData(data) {
	return request({
		url: '/jgjcadmin/api/basedata/sampletype/selectData',
		method: 'post',
		data
	});
}
