import request from "@/utils/request";

// 养护路段
export function maintenanceSectionList() {
  return request({
    url: "/system/maintenanceSection/listAll",
    method: "get",
  });
}
// 路线编号
export function routeListAll(id = []) {
  return request({
    url: `/baseData/common/server/getListRoute`,
    method: "post",
    data: id
  });
}
// 管理处、管养分处
export function deptTree(data) {
  return request({
    url: "/system/user/deptTree",
    method: "get",
    params: data,
  });
}
// 查询字典数据列表
export function listData(query) {
  return request({
    url: "/system/dict/data/list",
    method: "get",
    params: query,
  });
}

// 查询养护子段数据
export function segmentsList(query) {
  return request({
    url: "/baseData/route/segments/getList",
    method: "get",
    params: query,
  });
}
