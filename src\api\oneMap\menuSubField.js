import request from '@/utils/request'

// 查询查询列头显示字段列表
export function getListPage(query) {
  return request({
    url: '/oneMap/menuSubFieldDetail/getListPage',
    method: 'get',
    params: query
  })
}

// 查询查询列头显示字段详细
export function getMenuSubField(id) {
  return request({
    url: '/oneMap/menuSubFieldDetail/getInfoById',
    method: 'get',
    params: { id }
  })
}

// 新增查询列头显示字段
export function addMenuSubField(data) {
  return request({
    url: '/oneMap/menuSubFieldDetail/add',
    method: 'post',
    data
  })
}

// 修改查询列头显示字段
export function updateMenuSubField(data) {
  return request({
    url: '/oneMap/menuSubFieldDetail/edit',
    method: 'post',
    data
  })
}

// 删除查询列头显示字段
export function delMenuSubField(ids) {
  return request({
    url: '/oneMap/menuSubFieldDetail/delete',
    method: 'delete',
    data: ids,
  })
}
