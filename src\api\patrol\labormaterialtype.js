import request from '@/utils/request'

// 查询设备类型树
export function getLabormaterialtypeTree() {
  return request({
    url: '/manager/labormaterialtype/getTree',
    method: 'get'
  })
}

// 查询物资类型列表
export function listLabormaterialtype(query) {
  return request({
    url: '/manager/labormaterialtype/list',
    method: 'get',
    params: query
  })
}

// 查询物资类型详细
export function getLabormaterialtype(id) {
  return request({
    url: '/manager/labormaterialtype/get/' + id,
    method: 'get'
  })
}

// 新增物资类型
export function addLabormaterialtype(data) {
  return request({
    url: '/manager/labormaterialtype/add',
    method: 'post',
    data: data
  })
}

// 修改物资类型
export function updateLabormaterialtype(data) {
  return request({
    url: '/manager/labormaterialtype/edit',
    method: 'put',
    data: data
  })
}

// 删除物资类型
export function delLabormaterialtype(id) {
  return request({
    url: '/manager/labormaterialtype/delete/' + id,
    method: 'delete'
  })
}
