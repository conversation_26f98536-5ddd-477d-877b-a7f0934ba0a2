import request from '@/utils/request'

// 查询桥梁特殊检查列表
export function listBridgeDetectionRecords(data) {
  return request({
    url: '/baseData/bridge/special/detection/getListPage',
    method: 'post',
    data
  })
}

// 查询桥梁特殊检查详细
export function getDetectionBridge(detectionRecordsId) {
  return request({
    url: '/baseData/bridge/special/detection/get/' + detectionRecordsId,
    method: 'get'
  })
}

// 新增桥梁特殊检查
export function addDetectionBridge(data) {
  return request({
    url: '/baseData/bridge/special/detection/add',
    method: 'post',
    data: data
  })
}

// 暂存桥梁特殊检查
export function tempaddDetectionBridge(data) {
  return request({
    url: '/baseData/bridge/special/detection/temp/add',
    method: 'post',
    data: data
  })
}

// 修改桥梁特殊检查
export function updateDetectionBridge(data) {
  return request({
    url: '/baseData/bridge/special/detection/edit',
    method: 'put',
    data: data
  })
}

// 删除桥梁特殊检查
export function delDetectionBridge(detectionRecordsId) {
  return request({
    url: '/baseData/bridge/special/detection/delete/' + detectionRecordsId,
    method: 'delete'
  })
}

// 获取最后一次桥梁特殊检查数据详细信息
export function getDetectionGetLast(bridgeStaticId) {
  return request({
    url: '/baseData/bridge/special/detection/get/last/'+ bridgeStaticId,
    method: 'get',
  
  })
}

// /bridge/special/detection/export
export function detectionExport(data) {
  return request({
    url: '/baseData/bridge/special/detection/export',
    method: 'post',
    data: data
  })
}


