import request from '@/utils/request'

/**
 * 地图渲染数据查询
 * @param {*} data 
 * @returns 
 */
export function getShapeList(data) {
  return request({
    url: '/oneMap/layerData/getShapeList',
    method: 'post',
    data,
    notVerifyDuplicates: true
  })
}

/**
 * 查询图层下级数据统计
 * @param {*} data 
 * @returns 
 */
export function getClassifyDataStat(data) {
  return request({
    url: '/oneMap/layerData/getClassifyDataStat',
    method: 'post',
    data,
    notVerifyDuplicates: true
  })
}

/**
 * 根据类型获取字段值
 * @param {*} params 
 * @returns 
 */
export function getDictData(params) {
  return request({
    url: '/oneMap/layerData/getDictData',
    method: 'get',
    params,
    notVerifyDuplicates: true
  })
}

// 地图数据详情
export function getDataList(data) {
  return request({
    url: '/oneMap/layerData/getDataList',
    method: 'post',
    data,
    notVerifyDuplicates: true
  })
}
// 条件分页查询查询列头显示字段列表
export function getTableHeader(params) {
  return request({
    url: '/oneMap/menuSubFieldDetail/getListPage',
    method: 'get',
    params,
    notVerifyDuplicates: true
  })
}

// 公路技术状况获取总数
export function selectSubLayerTotal(data) {
  return request({
    url: '/oneMap/layerData/selectSubLayerTotal',
    method: 'post',
    data,
    notVerifyDuplicates: true
  })
}