import request from '@/utils/request'

/**
 * 查询部门范围线
 * @param {*} params {sysDeptId:系统部门id} 
 * @returns 
 */
export function getShapeList(data) {
  return request({
    url: '/oneMap/deptInfo/getShapeList',
    method: 'post',
    data
  })
}

/**
 * 查询部门合并范围线
 * @param {*} params {sysDeptId:系统部门id}
 * @returns
 */
export function getMergeShape(data) {
  return request({
    url: '/oneMap/deptInfo/getMergeShape',
    method: 'post',
    data,
  })
}