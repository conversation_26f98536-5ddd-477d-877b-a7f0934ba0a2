import request from '@/utils/request'

// 查询采样类型列表
export function listSampleType(query) {
  return request({
    url: '/jgjc-manager/api/console/jc/sampleType/findPage',
    method: 'post',
    data: query
  })
}

// 查询采样类型详细
export function getSampleType(id) {
  return request({
    url: '/jgjc-manager/api/console/jc/sampleType/findById?id=' + id,
    method: 'get'
  })
}

// 新增采样类型
export function addSampleType(data) {
  return request({
    url: '/jgjc-manager/api/console/jc/sampleType/create',
    method: 'post',
    data: data
  })
}

// 修改采样类型
export function updateSampleType(data) {
  return request({
    url: '/jgjc-manager/api/console/jc/sampleType/modify',
    method: 'post',
    data: data
  })
}

// 删除采样类型
export function delSampleType(id) {
  return request({
    url: '/jgjc-manager/api/console/jc/sampleType/removeById?id=' + id,
    method: 'delete'
  })
}
// 批量删除采样类型
export function delSampleTypeBatch(ids) {
  return request({
    url: '/jgjc-manager/api/console/jc/sampleType/batchRemove',
    method: 'post',
    data: ids
  })
}
// 获取所有采样类型
export function getFindAllSampleType() {
  return request({
    url: '/jgjc-manager/api/console/jc/sampleType/findAll',
    method: 'get'
  })
}
