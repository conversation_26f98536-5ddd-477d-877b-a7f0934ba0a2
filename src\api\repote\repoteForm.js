import request from '@/utils/request'

// 查询填报格规范列表
export function listRepoteForm(query) {
    return request({
        url: '/repote/repoteForm/list',
        method: 'get',
        params: query
    })
}

// 查询填报格规范详细
export function getRepoteForm(id) {
    return request({
        url: '/repote/repoteForm/get/' + id,
        method: 'get'
    })
}

// 新增填报格规范
export function addRepoteForm(data) {
    return request({
        url: '/repote/repoteForm/add',
        method: 'post',
        data: data
    })
}

// 修改填报格规范
export function updateRepoteForm(data) {
    return request({
        url: '/repote/repoteForm/edit',
        method: 'put',
        data: data
    })
}

// 删除填报格规范
export function delRepoteForm(id) {
    return request({
        url: '/repote/repoteForm/delete/' + id,
        method: 'delete'
    })
}

// 合并填报格规范
export function mergeRepoteForm(formId) {
    return request({
        url: '/repote/repoteForm/mergeExcel',
        method: 'post',
        params: { formId: formId }
    })
}

// 根据url获取Excel文件的最后一个有值的行号
export function getLastRow(fileUrl) {
    return request({
        url: '/repote/repoteForm/getLastRow',
        method: 'get',
        params: { fileUrl: fileUrl }
    })
}

// 下载并压缩文件
export function downloadAndZipFiles(params) {
    return request({
        url: '/repote/repoteForm/download',
        method: 'get',
        params: params,
        responseType: 'blob' // 确保返回的是二进制数据
    })
}
