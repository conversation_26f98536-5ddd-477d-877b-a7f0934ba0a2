import request from '@/utils/request'

// 查询方案明细扩展列表
export function listPlandetailex(query) {
  return request({
    url: '/budget/plandetailex/list',
    method: 'get',
    params: query
  })
}

// 查询方案明细扩展详细
export function getPlandetailex(id) {
  return request({
    url: '/budget/plandetailex/get/' + id,
    method: 'get'
  })
}

// 新增方案明细扩展
export function addPlandetailex(data) {
  return request({
    url: '/budget/plandetailex/add',
    method: 'post',
    data: data
  })
}

// 修改方案明细扩展
export function updatePlandetailex(data) {
  return request({
    url: '/budget/plandetailex/edit',
    method: 'put',
    data: data
  })
}

// 删除方案明细扩展
export function delPlandetailex(id) {
  return request({
    url: '/budget/plandetailex/delete/' + id,
    method: 'delete'
  })
}
