import request from "@/utils/request";

// 查询图层目录设置列表
export function getListPage(query) {
  return request({
    url: "/oneMap/layerMenu/getListPage",
    method: "get",
    params: query,
  });
}

// 查询图层目录设置详细
export function getLayerMenu(id) {
  return request({
    url: "/oneMap/layerMenu/getInfoById",
    method: "get",
    params: { id },
  });
}

// 新增图层目录设置
export function addLayerMenu(data) {
  return request({
    url: "/oneMap/layerMenu/add",
    method: "post",
    data: data,
  });
}

// 修改图层目录设置
export function updateLayerMenu(data) {
  return request({
    url: "/oneMap/layerMenu/edit",
    method: "post",
    data: data,
  });
}

// 删除图层目录设置
export function delLayerMenu(ids) {
  return request({
    url: "/oneMap/layerMenu/delete",
    method: "delete",
    data: ids,
  });
}

/**
 * 查询图层目录设置树结构
 * @param {*} params
 * @returns
 */
export function getListTree(params) {
  return request({
    url: "/oneMap/layerMenu/getListTree",
    method: "get",
    params,
  });
}

/**
 * 一张图目录
 * @param {*} params  {deptIds:部门ids ,menuShowType: 目录展示类型 1树形 2数据总览, oneMapShow: 一张图是否显示：1显示0不显示}
 * @returns
 */

export function geOneMapMenuTree(data = {}) {
  return request({
    url: "/oneMap/layerMenu/oneMapMenu",
    method: "post",
    data,
    notVerifyDuplicates: true
  });
}
