import request from '@/utils/request'

// 查询填报任务列表
export function listRepoteMission(query) {
  return request({
    url: '/repote/repoteMission/list',
    method: 'get',
    params: query
  })
}

// 查询填报任务详细
export function getRepoteMission(id) {
  return request({
    url: '/repote/repoteMission/get/' + id,
    method: 'get'
  })
}

// 新增填报任务
export function addRepoteMission(data) {
  return request({
    url: '/repote/repoteMission/add',
    method: 'post',
    data: data
  })
}

// 修改填报任务
export function updateRepoteMission(data) {
  return request({
    url: '/repote/repoteMission/edit',
    method: 'put',
    data: data
  })
}

// 删除填报任务
export function delRepoteMission(id) {
  return request({
    url: '/repote/repoteMission/delete/' + id,
    method: 'delete'
  })
}
