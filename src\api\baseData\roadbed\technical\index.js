import { param } from '@/utils/index'
import request from '@/utils/request'

// 查询列表
export function getListPage(data) {
  return request({
    url: '/baseData/pavement/technical/results/getListPage',
    method: 'post',
    data
  })
}

// 删除
export function deleteByIds(data) {
  return request({
    url: '/baseData/pavement/technical/results/delete',
    method: 'delete',
    data: data
  })
}

// 新增
export function addRoadbedTechnical(data) {
  return request({
    url: '/baseData/pavement/technical/results/add',
    method: 'post',
    data: data
  })
}



// 修改
export function updateRoadbedTechnical(data) {
  return request({
    url: '/baseData/pavement/technical/results/edit',
    method: 'put',
    data: data
  })
}

// 获取项目
export function getAllProjects(params) {
  return request({
    url: '/baseData/pavement/assessment/project/getAllProjects',
    method: 'get',
    params
  })
}





