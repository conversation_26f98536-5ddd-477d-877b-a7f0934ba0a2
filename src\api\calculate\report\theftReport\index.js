import request from '@/utils/request'

// 被损修复台账
export function getTheftLedger(repositoryDTO) {
	return request({
		url: '/manager/calcreportdata/theftledger',
		method: 'post',
		data: repositoryDTO
	});
}


// 系统使用情况
export function getTheftdetail(repositoryDTO) {
	return request({
		url: '/manager/calcreportdata/theftdetail',
		method: 'post',
		data: repositoryDTO
	});
}

// 系统使用情况
export function getTheftSum(repositoryDTO) {
	return request({
		url: '/manager/calcreportdata/theftsum',
		method: 'post',
		data: repositoryDTO
	});
}

