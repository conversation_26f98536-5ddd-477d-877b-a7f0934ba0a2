import request from "@/utils/request";

/**
 * 结构物清单
 * @param {*} params 
 * @returns 
 */
export function getAllStructureInfoGroupByDomain() {
  return request({
    url: "https://jkjc.yciccloud.com:8000/xboot/displayScreen/getAllStructureInfoGroupByDomain",
    method: "get",
    notVerifyDuplicates: true
  });
}

/**
 * 健康检测 传感器运营状态
 * @param {*} params 
 * @returns 
 */
export function getWarningList(params) {
  return request({
    url: "https://jkjc.yciccloud.com:8000/xboot/data/getDataInfo",
    method: "get",
    params,
    notVerifyDuplicates: true
  });
}