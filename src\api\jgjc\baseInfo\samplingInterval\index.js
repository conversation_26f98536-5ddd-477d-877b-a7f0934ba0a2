import request from '@/utils/request';
export function addSampleInterval(data) {
	return request({
		url: '/jgjcadmin/api/basedata/sampleinterval/save',
		method: 'post',
		data
	});
}

export function updateSampleInterval(data) {
	return request({
		url: '/jgjcadmin/api/basedata/sampleinterval/update',
		method: 'post',
		data
	});
}

export function deleteSampleInterval(data) {
	return request({
		url: '/jgjcadmin/api/basedata/sampleinterval/delete',
		method: 'post',
		data
	});
}

export function listSampleIntervals(data) {
	return request({
		url: '/jgjcadmin/api/basedata/sampleinterval/page',
		method: 'post',
		data,
    notVerifyDuplicates: true
	});
}

export function batchAddSampleIntervals(data) {
	return request({
		url: '/jgjcadmin/api/basedata/sampleinterval/batchAdd',
		method: 'post',
		data
	});
}

export function getSampleIntervalSelectData(data) {
	return request({
		url: '/jgjcadmin/api/basedata/sampleinterval/selectData',
		method: 'post',
		data
	});
}
