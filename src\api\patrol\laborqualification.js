import request from '@/utils/request'

// 查询施工单位资质情况列表
export function listLaborqualification(query) {
  return request({
    url: '/manager/laborqualification/list',
    method: 'get',
    params: query
  })
}

// 查询获取施工单位树
export function getConstructionTree(id) {
  return request({
    url: '/manager/laborqualification/getConstructionTree',
    method: 'get'
  })
}

// 获取施工单位的人员
export function getConstructUserList(data) {
  return request({
    url: '/manager/laborqualification/getConstructUserList',
    method: 'post',
    data: data
  })
}

// 查询施工单位资质情况详细
export function getLaborqualification(id) {
  return request({
    url: '/manager/laborqualification/get/' + id,
    method: 'get'
  })
}

// 新增施工单位资质情况
export function addLaborqualification(data) {
  return request({
    url: '/manager/laborqualification/add',
    method: 'post',
    data: data
  })
}

// 修改施工单位资质情况
export function updateLaborqualification(data) {
  return request({
    url: '/manager/laborqualification/edit',
    method: 'put',
    data: data
  })
}

// 删除施工单位资质情况
export function delLaborqualification(id) {
  return request({
    url: '/manager/laborqualification/delete/' + id,
    method: 'delete'
  })
}
