import request from '@/utils/request'

// 主体模板管理新增
export function saveReportBodyTemplate(query) {
  return request({
    url: '/jgjcadmin/api/evaluationReport/reportBodyTemplate/save',
    method: 'post',
    data: query
  })
}

// 主体模板管理编辑
export function updateReportBodyTemplate(query) {
  return request({
    url: '/jgjcadmin/api/evaluationReport/reportBodyTemplate/update',
    method: 'post',
    data: query
  })
}

// 主体模板管理删除
export function deleteReportBodyTemplate(query) {
  return request({
    url: '/jgjcadmin/api/evaluationReport/reportBodyTemplate/delete',
    method: 'post',
    data: query
  })
}

// 主体模板管理查询
export function getReportBodyTemplatePage(query) {
  return request({
    url: '/jgjcadmin/api/evaluationReport/reportBodyTemplate/page',
    method: 'get',
    params: query
  })
}
