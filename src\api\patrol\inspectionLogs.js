import request from '@/utils/request';

// 查询巡查日志列表
export function listInspectionLogs(query) {
  return request({
    url: '/patrol/inspectionLogs/list',
    method: 'get',
    params: query,
  });
}

// 查询巡查日志详细
export function getInspectionLogs(id) {
  return request({
    url: '/patrol/inspectionLogs/get/' + id,
    method: 'get',
  });
}

// 新增巡查日志
export function addInspectionLogs(data) {
  return request({
    url: '/patrol/inspectionLogs/add',
    method: 'post',
    data: data,
  });
}

// 修改巡查日志
export function updateInspectionLogs(data) {
  return request({
    url: '/patrol/inspectionLogs/edit',
    method: 'put',
    data: data,
  });
}

// 删除巡查日志
export function delInspectionLogs(id) {
  return request({
    url: '/patrol/inspectionLogs/delete/' + id,
    method: 'delete',
  });
}

// 查询巡查日志列表
export function getDaysWithData(query) {
  return request({
    url: '/patrol/inspectionLogs/daysWithData',
    method: 'get',
    params: query,
  });
}

// 批量添加巡查日志
export function batchAddInspectionLogs(data) {
  return request({
    url: '/patrol/inspectionLogs/addBatch',
    method: 'post',
    data: data,
  });
}

// 批量删除巡查日志
export function batchDeleteInspectionLogs(data) {
  return request({
    url: '/patrol/inspectionLogs/deleteByIds',
    method: 'post',
    data: data,
  });
}

//获取巡查轨迹
export function GetPatrolGeoById(data) {
  return request({
    url: '/patrol/inspectionGeo/getGeo/'+data,
    method: 'get',
    data: data,
  })
}

export function countByPatrolUnit(query) {
  return request({
    url: '/patrol/inspectionLogs/countByPatrolUnit',
    method: 'post',
    params: query,
    data: query
  });
}
export function countByMaintenanceSubUnit(query) {
  return request({
    url: '/patrol/inspectionLogs/countByMaintenanceSubUnit',
    method: 'post',
    params: query,
    data: query
  });
}
export function countByMaintenanceUnit(query) {
  return request({
    url: '/patrol/inspectionLogs/countByMaintenanceUnit',
    method: 'post',
    params: query,
    data: query
  });
}
