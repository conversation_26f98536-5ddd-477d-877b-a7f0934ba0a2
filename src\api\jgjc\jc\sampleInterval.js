import request from '@/utils/request'

// 查询采样间隔列表
export function listSampleInterval(query) {
  return request({
    url: '/jgjc-manager/api/console/jc/sampleInterval/findPage',
    method: 'post',
    data: query
  })
}

// 查询采样间隔详细
export function getSampleInterval(id) {
  return request({
    url: '/jgjc-manager/api/console/jc/sampleInterval/findById?id=' + id,
    method: 'get'
  })
}

// 新增采样间隔
export function addSampleInterval(data) {
  return request({
    url: '/jgjc-manager/api/console/jc/sampleInterval/create',
    method: 'post',
    data: data
  })
}

// 修改采样间隔
export function updateSampleInterval(data) {
  return request({
    url: '/jgjc-manager/api/console/jc/sampleInterval/modify',
    method: 'post',
    data: data
  })
}

// 删除采样间隔
export function delSampleInterval(id) {
  return request({
    url: '/jgjc-manager/api/console/jc/sampleInterval/removeById?id=' + id,
    method: 'delete'
  })
}
// 批量删除采样间隔
export function delSampleIntervalBatch(ids) {
  return request({
    url: '/jgjc-manager/api/console/jc/sampleInterval/batchRemove',
    method: 'post',
    data: ids
  })
}

// 获取所有采样间隔
export function getFindAllSampleInterval() {
  return request({
    url: '/jgjc-manager/api/console/jc/sampleInterval/findAll',
    method: 'get'
  })
}
