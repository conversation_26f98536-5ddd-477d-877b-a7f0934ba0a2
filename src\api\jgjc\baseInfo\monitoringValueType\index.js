import request from '@/utils/request';

// 值类型相关接口
export function addValueType(data) {
  return request({
    url: '/jgjcadmin/api/basedata/valuetype/save',
    method: 'post',
    data
  });
}

export function updateValueType(data) {
  return request({
    url: '/jgjcadmin/api/basedata/valuetype/update',
    method: 'post',
    data
  });
}

export function deleteValueType(data) {
  return request({
    url: '/jgjcadmin/api/basedata/valuetype/delete',
    method: 'post',
    data
  });
}

export function listValueTypes(data) {
  return request({
    url: '/jgjcadmin/api/basedata/valuetype/page',
    method: 'post',
    data
  });
}

export function batchAddValueTypes(data) {
  return request({
    url: '/jgjcadmin/api/basedata/valuetype/batchAdd',
    method: 'post',
    data
  });
}
