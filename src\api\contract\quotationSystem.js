import request from '@/utils/request'

// 查询报价体系基本信息列表
export function listByParam(query) {
  return request({
    url: '/manager/lib/listByParam',
    method: 'post',
    data: query
  })
}

// 查询报价体系基本信息详细
export function getLib(id) {
  return request({
    url: '/manager/lib/get/' + id,
    method: 'get'
  })
}

// 新增报价体系基本信息
export function addLib(data) {
  return request({
    url: '/manager/lib/add',
    method: 'post',
    data: data
  })
}

// 修改报价体系基本信息
export function updateLib(data) {
  return request({
    url: '/manager/lib/edit',
    method: 'put',
    data: data
  })
}

// 修改报价体系基本信息
export function copyLib(data) {
  return request({
    url: '/manager/lib/copy',
    method: 'post',
    data: data
  })
}

// 删除报价体系基本信息
export function delLib(id) {
  return request({
    url: '/manager/lib/delete/' + id,
    method: 'delete'
  })
}


// 查询列表树
export function getTreeData(data) {
  return request({
    url:'/manager/libschpricetree/detailList',
    method: 'post',
    data,
    notVerifyDuplicates: true
  })
}

// 新增类型
export function addLibschpricetree(data) {
  return request({
    url: '/manager/libschpricetree/add',
    method: 'post',
    data: data
  })
}

// 修改类型
export function updateLibschpricetree(data) {
  return request({
    url: '/manager/libschpricetree/edit',
    method: 'put',
    data: data
  })
}

// 删除类型
export function delLibschpricetree(id) {
  return request({
    url: '/manager/libschpricetree/delete/' + id,
    method: 'delete'
  })
}

// 批量新增报价体系类型
export function saveBatch(data) {
  return request({
    url: '/manager/libschpricetree/saveBatch',
    method: 'post',
    data: data
  })
}


//
export function getSafetyFeeList(conId) {
  return request({
    url: `/manager/libschpricetree/getSafetyFeeList/${conId}`,
    method: 'get',
  })
}
