import request from '@/utils/request'

// 查询被损被盗索赔记录列表(分页)
export function queryList(data) {
  return request({
    url: '/manager/theft/construction/claim/list',
    method: 'post',
    data,
  });
}

// 查询被损被盗索赔记录列表(不分页)
export function queryListAll() {
  return request({
    url: '/manager/theft/construction/claim/listAll',
    method: 'get',
  });
}

// 根据id查询被损被盗索赔记录数据
export function getClaimById(id) {
  return request({
    url: `/manager/theft/construction/claim/get/${id}`,
    method: 'get',
  });
}

// 新增被损被盗索赔记录
export function addClaim(data) {
  return request({
    url: '/manager/theft/construction/claim/add',
    method: 'post',
    data,
  });
}

// 修改被损被盗索赔记录
export function editClaim(data) {
  return request({
    url: '/manager/theft/construction/claim/edit',
    method: 'put',
    data,
  });
}

// 删除被损被盗索赔记录
export function deleteClaim(id) {
  return request({
    url: `/manager/theft/construction/claim/delete/${id}`,
    method: 'delete',
  });
}
