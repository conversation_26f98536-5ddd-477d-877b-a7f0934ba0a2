import request from '@/utils/request'

// 查询设备类型列表
export function listDeviceType(query) {
  return request({
    url: '/jgjc-manager/api/console/jc/deviceType/findPage',
    method: 'post',
    data: query
  })
}
// 查询设备类型详细
export function getDeviceType(id) {
  return request({
    url: '/jgjc-manager/api/console/jc/deviceType/findById?id=' + id,
    method: 'get'
  })
}
// 新增设备类型
export function addDeviceType(data) {
  return request({
    url: '/jgjc-manager/api/console/jc/deviceType/create',
    method: 'post',
    data: data
  })
}

// 修改设备类型
export function updateDeviceType(data) {
  return request({
    url: '/jgjc-manager/api/console/jc/deviceType/modify',
    method: 'post',
    data: data
  })
}

// 删除设备类型
export function delDeviceType(id) {
  return request({
    url: '/jgjc-manager/api/console/jc/deviceType/removeById?id=' + id,
    method: 'delete'
  })
}
// 批量删除设备类型
export function delDeviceTypeBatch(ids) {
  return request({
    url: '/jgjc-manager/api/console/jc/deviceType/batchRemove',
    method: 'post',
    data: ids
  })
}
// 获取设备类型列表
export function findAllDeviceType() {
  return request({
    url: '/jgjc-manager/api/console/jc/deviceType/findAll',
    method: 'get'
  })
}
