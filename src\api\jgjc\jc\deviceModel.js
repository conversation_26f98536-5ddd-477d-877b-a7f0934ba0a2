import request from '@/utils/request'

// 查询设备型号列表
export function listDeviceModel(query) {
  return request({
    url: '/jgjc-manager/api/console/jc/deviceModel/findPage',
    method: 'post',
    data: query
  })
}

// 查询设备型号详细
export function getDeviceModel(id) {
  return request({
    url: '/jgjc-manager/api/console/jc/deviceModel/findById?id=' + id,
    method: 'get'
  })
}

// 新增设备型号
export function addDeviceModel(data) {
  return request({
    url: '/jgjc-manager/api/console/jc/deviceModel/create',
    method: 'post',
    data: data
  })
}

// 修改设备型号
export function updateDeviceModel(data) {
  return request({
    url: '/jgjc-manager/api/console/jc/deviceModel/modify',
    method: 'post',
    data: data
  })
}

// 删除设备型号
export function delDeviceModel(id) {
  return request({
    url: '/jgjc-manager/api/console/jc/deviceModel/removeById?id=' + id,
    method: 'delete'
  })
}
// 批量删除设备型号
export function delDeviceModelBatch(ids) {
  return request({
    url: '/jgjc-manager/api/console/jc/deviceModel/batchRemove',
    method: 'post',
    data: ids
  })
}
