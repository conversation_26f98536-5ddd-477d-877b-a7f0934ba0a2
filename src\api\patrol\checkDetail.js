import request from '@/utils/request'

// 查询资产寻检查子列表
export function listCheckDetail(query) {
  return request({
    url: '/patrol/checkDetail/list',
    method: 'get',
    params: query
  })
}

// 查询资产寻检查子详细
export function getCheckDetail(id) {
  return request({
    url: '/patrol/checkDetail/get/' + id,
    method: 'get'
  })
}

// 新增资产寻检查子
export function addCheckDetail(data) {
  return request({
    url: '/patrol/checkDetail/add',
    method: 'post',
    data: data
  })
}

// 修改资产寻检查子
export function updateCheckDetail(data) {
  return request({
    url: '/patrol/checkDetail/edit',
    method: 'put',
    data: data
  })
}

// 删除资产寻检查子
export function delCheckDetail(id) {
  return request({
    url: '/patrol/checkDetail/delete/' + id,
    method: 'delete'
  })
}
