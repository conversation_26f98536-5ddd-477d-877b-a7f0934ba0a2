import request from '@/utils/request'

// 查询资产病害信息列表
export function listDiseases(query) {
  return request({
    url: '/patrol/diseases/list',
    method: 'get',
    params: query
  })
}

export function listAllDiseases(query) {
  return request({
    url: '/patrol/diseases/listAll',
    method: 'get',
    params: query
  })
}

// 查询资产病害信息详细
export function getDiseases(id) {
  return request({
    url: '/patrol/diseases/get/' + id,
    method: 'get'
  })
}

// 新增资产病害信息
export function addDiseases(data) {
  return request({
    url: '/patrol/diseases/add',
    method: 'post',
    data: data
  })
}

// 修改资产病害信息
export function updateDiseases(data) {
  return request({
    url: '/patrol/diseases/edit',
    method: 'put',
    data: data
  })
}

// 删除资产病害信息
export function delDiseases(id) {
  return request({
    url: '/patrol/diseases/delete/' + id,
    method: 'delete'
  })
}
