import request from '@/utils/request';

export function addDeviceType(data) {
  return request({
    url: '/jgjcadmin/api/basedata/devicetype/save',
    method: 'post',
    data
  });
}

export function updateDeviceType(data) {
  return request({
    url: '/jgjcadmin/api/basedata/devicetype/update',
    method: 'post',
    data
  });
}

export function deleteDeviceType(data) {
  return request({
    url: '/jgjcadmin/api/basedata/devicetype/delete',
    method: 'post',
    data
  });
}

export function listDeviceTypes(data) {
  return request({
    url: '/jgjcadmin/api/basedata/devicetype/page',
    method: 'post',
    data
  });
}

export function batchAddDeviceTypes(data) {
  return request({
    url: '/jgjcadmin/api/basedata/devicetype/batchAdd',
    method: 'post',
    data
  });
}

export function getDeviceTypeSelectData(data) {
  return request({
    url: '/jgjcadmin/api/basedata/devicetype/selectData',
    method: 'post',
    data
  });
}
