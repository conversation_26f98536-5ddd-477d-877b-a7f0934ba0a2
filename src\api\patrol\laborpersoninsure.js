import request from '@/utils/request'

// 查询劳务人员参保情况列表
export function listLaborpersoninsure(query) {
  return request({
    url: '/manager/laborpersoninsure/list',
    method: 'get',
    params: query
  })
}

// 获取劳务人员保险统计信息
export function getStatistics(query) {
  return request({
    url: '/manager/laborpersoninsure/getStatistics',
    method: 'post',
    data: query
  })
}

// 查询参保信息
export function getInsureAllList(query) {
  return request({
    url: '/manager/laborpersoninsure/getInsureAllList',
    method: 'post',
    data: query
  })
}

// 查询参保信息(分页)
export function getInsureByPage(query, page) {
  return request({
    url: '/manager/laborpersoninsure/getInsureByPage',
    method: 'post',
    data: query,
    params: page
  })
}


// 查询劳务人员参保情况详细
export function getLaborpersoninsure(id) {
  return request({
    url: '/manager/laborpersoninsure/get/' + id,
    method: 'get'
  })
}

// 新增劳务人员参保情况
export function addLaborpersoninsure(data) {
  return request({
    url: '/manager/laborpersoninsure/add',
    method: 'post',
    data: data
  })
}

// 修改劳务人员参保情况
export function updateLaborpersoninsure(data) {
  return request({
    url: '/manager/laborpersoninsure/edit',
    method: 'put',
    data: data
  })
}

// 删除劳务人员参保情况
export function delLaborpersoninsure(id) {
  return request({
    url: '/manager/laborpersoninsure/delete/' + id,
    method: 'delete'
  })
}
