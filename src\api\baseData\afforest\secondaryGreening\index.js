import request from '@/utils/request'

// 查询列表
export function getListPage(data) {
  return request({
    url: '/baseData/secondaryGreening/getListPage',
    method: 'post',
    data
  })
}

// 增
export function addSecondaryGreening(data) {
  return request({
    url: "/baseData/secondaryGreening/add",
    method: "post",
    data,
  });
}

// 删
export function delSecondaryGreening(ids) {
  return request({
    url: `/baseData/secondaryGreening/delete/${ids}`,
    method: 'delete',
  })
}

// 改
export function updateSecondaryGreening(data) {
  return request({
    url: '/baseData/secondaryGreening/edit',
    method: 'put',
    data: data
  })
}

// 查
export function getSecondaryGreening(id) {

  return request({
    url: `/baseData/secondaryGreening/get/${id}`,
    method: "get",
  });
}

// 暂存
export function tempSecondaryGreening(data) {
  return request({
    url: `/baseData/secondaryGreening/tempAdd`,
    method: "post",
    data
  });
}

