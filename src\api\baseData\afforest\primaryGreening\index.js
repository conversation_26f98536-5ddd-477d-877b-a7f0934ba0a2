import request from '@/utils/request'

// 查询列表
export function getListPage(data) {
  return request({
    url: '/baseData/primaryGreening/getListPage',
    method: 'post',
    data
  })
}

// 增
export function addPrimaryGreening(data) {
  return request({
    url: "/baseData/primaryGreening/add",
    method: "post",
    data,
  });
}

// 删
export function delPrimaryGreening(ids) {
  return request({
    url: `/baseData/primaryGreening/delete/${ids}`,
    method: 'delete',
  })
}

// 改
export function updatePrimaryGreening(data) {
  return request({
    url: '/baseData/primaryGreening/edit',
    method: 'put',
    data: data
  })
}

// 查
export function getPrimaryGreening(id) {

  return request({
    url: `/baseData/primaryGreening/get/${id}`,
    method: "get",
  });
}

// 暂存
export function tempPrimaryGreening(data) {
  return request({
    url: `/baseData/primaryGreening/tempAdd`,
    method: "post",
    data
  });
}
