import request from '@/utils/request'

// 应急物资入库单审核节点信息
export function getNodeInfo(data) {
  return request({
    url: '/manager/emergency/material/inbound/node/info',
    method: 'post',
    data
  })
}

// 应急物资申请入库单列表
export function getMaterialInApplicitionList(data) {
  return request({
    url: '/manager/emergency/material/inbound/list',
    method: 'post',
    data
  })
}

// 删除申请入库单
export function delMaterialIn(id) {
  return request({
    url: `/manager/emergency/material/inbound/delete/${id}`,
    method: 'delete',
  })
}

//  应急物资入库单审核
export function submitInbound(data) {
  return request({
    url: '/manager/emergency/material/inbound/process',
    method: 'post',
    data
  })
}

// 新增入库单时查询出库单记录
export function getOutboundRecord(data) {
  return request({
    url: '/manager/emergency/material/outbound/data',
    method: 'post',
    data
  })
}

// 新增入库单
export function addMaterialInApplicition(data) {
  return request({
    url: '/manager/emergency/material/inbound/add',
    method: 'post',
    data
  })
}

// 修改入库单
export function editMaterialInApplicition(data) {
  return request({
    url: '/manager/emergency/material/inbound/edit',
    method: 'put',
    data
  })
}

// 根据id查询应急物资入库单数据
export function getDetailInList(id) {
  return request({
    url: `/manager/emergency/material/inbound/get/${id}`,
    method: 'get'
  })
}

// 根据物资库id与入库单id查询应急物资入库单明细列表
export function getInDetailList(data) {
  return request({
    url: '/manager/emergency/material/inbound/detail/list',
    method: 'post',
    data
  })
}

// 获取入库单审核列表
export function getMaterialInAuditList (data) {
  return request({
    url: '/manager/emergency/material/inbound/pending/list',
    method: 'post',
    data
  })
}

// 获取入库单结果列表
export function getMaterialInResultList(data) {
  return request({
    url: '/manager/emergency/material/inbound/view',
    method: 'post',
    data
  })
}

// 报表
export function previewReport(id) {
  return request({
    url: `/manager/emergency/material/inbound/report/preview?id=${id}`,
    method: 'get',
  })
}
