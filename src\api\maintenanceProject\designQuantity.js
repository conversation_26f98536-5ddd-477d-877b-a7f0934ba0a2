import request from '@/utils/request'
export function listProject(data) {
    return request({
        url: '/manager/proj/design/detail/list',
        method: 'post',
        data: data
    });
}
export function pendingList(data) {
    return request({
        url: '/manager/proj/design/detail/pending/list',
        method: 'post',
        data: data
    });
}
export function view(data) {
    return request({
        url: '/manager/proj/design/detail/view',
        method: 'post',
        data: data
    });
}
export function get(projId) {
    return request({
        url: `/manager/proj/design/detail/get/${projId}`,
        method: 'get'
    });
}
export function add(designDetailDTO) {
    return request({
        url: '/manager/proj/design/detail/add',
        method: 'post',
        data: designDetailDTO
    });
}
export function edit(designDetailDTO) {
    return request({
        url: '/manager/proj/design/detail/edit',
        method: 'put',
        data: designDetailDTO
    });
}
export function remove(id) {
    return request({
        url: `/manager/proj/design/detail/delete/${id}`,
        method: 'delete'
    });
}
export function process(processReviewDTO) {
    return request({
        url: '/manager/proj/design/detail/process',
        method: 'post',
        data: processReviewDTO
    });
}
export function getNodeInfo(processReviewDTO) {
    return request({
        url: '/manager/proj/design/detail/node/info',
        method: 'post',
        data: processReviewDTO
    });
}

