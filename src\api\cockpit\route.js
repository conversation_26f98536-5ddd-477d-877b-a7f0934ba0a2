import request from "@/utils/request";

/**
 * 路网专题-路网统计
 * @returns 
 */
export function getRouteCount() {
  return request({
    url: "/system/count/baseCount",
    method: "get",
    notVerifyDuplicates: true,
  });
}

/**
 * 路网专题-路线性质统计
 * @returns 
 */
export function getRouteTypeCount() {
  return request({
    url: "/system/count/routeTypeCount",
    method: "get",
    notVerifyDuplicates: true,
  });
}

/**
 * 路网专题-路线技术等级统计
 * @returns 
 */
export function getRouteGradeCount() {
  return request({
    url: "/system/count/routeGradeCount",
    method: "get",
    notVerifyDuplicates: true,
  });
}

/**
 * 路网专题-通车里程趋势
 * @returns 
 */
export function getRouteMileageTrend() {
  return request({
    url: "/system/count/trendOpenMileage",
    method: "get",
    notVerifyDuplicates: true,
  });
}

/**
 * 路网专题-路线统计
 * @returns 
 */
export function getRouteStatistics() {
  return request({
    url: "/system/count/routeCountAll",
    method: "get",
    notVerifyDuplicates: true,
  });
}

/**
 * 路网专题-管理处路段统计
 * @returns 
 */
export function getManageStatistics() {
  return request({
    url: "/system/count/deptRouteCount",
    method: "get",
    notVerifyDuplicates: true,
  });
}

/**
 * 路网专题-优等路率
 * @returns 
 */
export function getExcellentRoadRate() {
  return request({
    url: "/baseData/pavement/technical/results/getStatistics",
    method: "get",
    notVerifyDuplicates: true,
  });
}

/**
 * 路网专题-路面养护经费投入情况
 * @returns 
 */
export function getRoadMaintenanceExpenditure() {
  return request({
    url: "/manager/constructionStat/getProjectRoadPlanAndUseFund",
    method: "get",
    notVerifyDuplicates: true,
  });
}

/**
 * 路网专题-路面养护工程实施情况
 * @returns 
 */

export function getRoadMaintenanceImplementation() {
  return request({
    url: "/manager/constructionStat/getLastYearFundByProjectType",
    method: "get",
    notVerifyDuplicates: true,
  }); 
}