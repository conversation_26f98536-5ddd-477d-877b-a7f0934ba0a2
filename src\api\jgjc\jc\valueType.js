import request from '@/utils/request'

// 查询值类型列表
export function listValueType(query) {
  return request({
    url: '/jgjc-manager/api/console/jc/valueType/findPage',
    method: 'post',
    data: query
  })
}

// 查询值类型详细
export function getValueType(id) {
  return request({
    url: '/jgjc-manager/api/console/jc/valueType/findById?id=' + id,
    method: 'get'
  })
}

// 新增值类型
export function addValueType(data) {
  return request({
    url: '/jgjc-manager/api/console/jc/valueType/create',
    method: 'post',
    data: data
  })
}

// 修改值类型
export function updateValueType(data) {
  return request({
    url: '/jgjc-manager/api/console/jc/valueType/modify',
    method: 'post',
    data: data
  })
}

// 删除值类型
export function delValueType(id) {
  return request({
    url: '/jgjc-manager/api/console/jc/valueType/removeById?id=' + id,
    method: 'delete'
  })
}
// 批量删除值类型
export function delValueTypeBatch(ids) {
  return request({
    url: '/jgjc-manager/api/console/jc/valueType/batchRemove',
    method: 'post',
    data: ids
  })
}
