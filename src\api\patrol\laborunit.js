import request from '@/utils/request'

// 查询劳务单位信息列表
export function listLaborunit(query) {
  return request({
    url: '/manager/laborunit/list',
    method: 'get',
    params: query
  })
}

// 查询劳务单位树
export function getLaborUnitTree(id) {
  return request({
    url: '/manager/laborunit/getLaborUnitTree',
    method: 'get'
  })
}

// 查询劳务单位信息详细
export function getLaborunit(id) {
  return request({
    url: '/manager/laborunit/get/' + id,
    method: 'get'
  })
}

// 新增劳务单位信息
export function addLaborunit(data) {
  return request({
    url: '/manager/laborunit/add',
    method: 'post',
    data: data
  })
}

// 修改劳务单位信息
export function updateLaborunit(data) {
  return request({
    url: '/manager/laborunit/edit',
    method: 'put',
    data: data
  })
}

// 删除劳务单位信息
export function delLaborunit(id) {
  return request({
    url: '/manager/laborunit/delete/' + id,
    method: 'delete'
  })
}
