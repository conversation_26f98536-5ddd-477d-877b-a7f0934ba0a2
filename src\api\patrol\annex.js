import request from '@/utils/request'

// 查询合同关联的路段及路段编码列表
export function listAnnex(query) {
  return request({
    url: '/patrol/annex/list',
    method: 'get',
    params: query
  })
}

// 查询合同关联的路段及路段编码详细
export function getAnnex(id) {
  return request({
    url: '/patrol/annex/get/' + id,
    method: 'get'
  })
}

// 新增合同关联的路段及路段编码
export function addAnnex(data) {
  return request({
    url: '/patrol/annex/add',
    method: 'post',
    data: data
  })
}

// 修改合同关联的路段及路段编码
export function updateAnnex(data) {
  return request({
    url: '/patrol/annex/edit',
    method: 'put',
    data: data
  })
}

// 删除合同关联的路段及路段编码
export function delAnnex(id) {
  return request({
    url: '/patrol/annex/delete/' + id,
    method: 'delete'
  })
}
