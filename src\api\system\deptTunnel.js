import request from '@/utils/request'

// 查询隧道资产权限列表
export function listDeptTunnel(query) {
  return request({
    url: '/system/deptTunnel/list',
    method: 'get',
    params: query
  })
}

// 查询隧道资产权限详细
export function getDeptTunnel(id) {
  return request({
    url: '/system/deptTunnel/get/' + id,
    method: 'get'
  })
}

// 根据条件查询已关联桥梁ID
export function getDefaultSelected(query) {
  return request({
    url: '/system/deptTunnel/getDefaultSelected',
    method: 'get',
    params: query
  })
}

// 批量新增
export function addAllDeptTunnel(data) {
  return request({
    url: '/system/deptTunnel/addAllDeptTunnel',
    method: 'post',
    data: data
  })
}

// 新增隧道资产权限
export function addDeptTunnel(data) {
  return request({
    url: '/system/deptTunnel/add',
    method: 'post',
    data: data
  })
}

// 修改隧道资产权限
export function updateDeptTunnel(data) {
  return request({
    url: '/system/deptTunnel/edit',
    method: 'put',
    data: data
  })
}

// 删除隧道资产权限
export function delDeptTunnel(id) {
  return request({
    url: '/system/deptTunnel/delete/' + id,
    method: 'delete'
  })
}
