import request from '@/utils/request';

/**
 * 流量卡管理分页查询
 * @param {Object} data 查询参数
 * @returns {Promise} 请求Promise
 */
export function getSimManagementPage(data) {
  return request({
    url: '/jgjcadmin/api/basedata/sim-management/page',
    method: 'post',
    data
  });
}

/**
 * 流量卡管理删除
 * @param {Object} data 删除参数，通常包含id
 * @returns {Promise} 请求Promise
 */
export function deleteSimManagement(data) {
  return request({
    url: '/jgjcadmin/api/basedata/sim-management/delete',
    method: 'post',
    data
  });
}

/**
 * 流量卡管理新增
 * @param {Object} data 新增的流量卡数据
 * @returns {Promise} 请求Promise
 */
export function saveSimManagement(data) {
  return request({
    url: '/jgjcadmin/api/basedata/sim-management/save',
    method: 'post',
    data
  });
}

/**
 * 流量卡管理编辑
 * @param {Object} data 更新的流量卡数据
 * @returns {Promise} 请求Promise
 */
export function updateSimManagement(data) {
  return request({
    url: '/jgjcadmin/api/basedata/sim-management/update',
    method: 'post',
    data
  });
}
