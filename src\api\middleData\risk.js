import request from '@/utils/request'

// 查询公路灾害风险信息列表
export function listRisk(query) {
  return request({
    url: '/middleData/roadHazardRisk/list',
    method: 'get',
    params: query
  })
}

// 查询公路灾害风险信息详细
export function getRisk(id) {
  return request({
    url: '/middleData/roadHazardRisk/get/' + id,
    method: 'get'
  })
}

// 新增公路灾害风险信息
export function addRisk(data) {
  return request({
    url: '/middleData/roadHazardRisk/add',
    method: 'post',
    data: data
  })
}

// 修改公路灾害风险信息
export function updateRisk(data) {
  return request({
    url: '/middleData/roadHazardRisk/edit',
    method: 'put',
    data: data
  })
}

// 删除公路灾害风险信息
export function delRisk(id) {
  return request({
    url: '/middleData/roadHazardRisk/delete/' + id,
    method: 'delete'
  })
}

// 批量删除
export function delRiskBatch(id) {
  return request({
    url: '/middleData/roadHazardRisk/deleteBatch',
    method: 'post',
    data: id
  })
}
