import request from '@/utils/request'

// 结构树
export function getUserTreeStructure(data) {
  return request({
    url: '/manager/finished1/getDaliyTreeStructure',
    method: 'post',
    data: data
  });
}

// 查询施工登记列表(分页)
export function list(data) {
  return request({
    url: '/manager/finished1/list',
    method: 'post',
    data: data
  });
}

// 查询完工登记列表(不分页)
export function listAll() {
  return request({
    url: '/manager/finished1/listAll',
    method: 'get'
  });
}

// 根据id查询完工登记数据
export function getDetil(id) {
  return request({
    url: `/manager/finished1/get/${id}`,
    method: 'get'
  });
}

// 根据id查询施工单事件明细
export function getDisDetail(id) {
  return request({
    url: `/manager/finished1/getDisDetail/${id}`,
    method: 'get'
  });
}

// 保存
export function save(data) {
  return request({
    url: '/manager/finished1/save',
    method: 'put',
    data: data
  });
}


// 修改完工登记
export function edit(data) {
  return request({
    url: '/manager/finished1/edit',
    method: 'put',
    data: data
  });
}

// 完工登记
export function review(data) {
  return request({
    url: '/manager/finished1/review',
    method: 'put',
    data: data
  });
}

// 撤回到待施工
export function rejectToRegister(data) {
  return request({
    url: '/manager/finished1/rejectToRegister',
    method: 'put',
    data: data
  });
}

// 导出完工登记列表
export function exportList() {
  return request({
    url: '/manager/finished1/export',
    method: 'post'
  });
}

// 查询施工单列表
export function pageList(data) {
  return request({
    url: '/manager/finished1/pageList',
    method: 'post',
    data
  });
}



// 获取施工单子目信息
export function getMethodList(data) {
  return request({
    url: '/manager/finished1/getMethodList',
    method: 'post',
    data
  });
}
