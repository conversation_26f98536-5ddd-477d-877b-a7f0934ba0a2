import request from '@/utils/request'


export function getListPage(data) {
  return request({
    url: '/baseData/pavement/assessment/project/getListPage',
    method: 'post',
    data
  })
}

// 删除
export function deleteByIds(data) {
  return request({
    url: '/baseData/pavement/assessment/project/delete/'+data,
    method: 'delete',
   
  })
}

// 新增
export function addAssessment(data) {
  return request({
    url: '/baseData/pavement/assessment/project/add',
    method: 'post',
    data: data
  })
}



// 修改
export function updateAssessment(data) {
  return request({
    url: '/baseData/pavement/assessment/project/edit',
    method: 'put',
    data: data
  })
}


