import request from '@/utils/request'

// 查询列表
export function getListPage(data) {
  return request({
    url: '/baseData/tunnel/detection/getListPage',
    method: 'post',
    data
  })
}

// 删除
export function deleteByIds(data) {
  return request({
    url: '/baseData/tunnel/detection/delete',
    method: 'delete',
    data: data
  })
}

// 新增
export function add(data) {
  return request({
    url: '/baseData/tunnel/detection/add',
    method: 'post',
    data: data
  })
}

// 修改
export function edit(data) {
  return request({
    url: '/baseData/tunnel/detection/edit',
    method: 'put',
    data: data
  })
}

// 修改
export function getInfoById(id) {
  return request({
    url: `/baseData/tunnel/detection/get/${id}`,
    method: 'get',
  })
}


// 复核
export function detectionRecheck(data) {
  return request({
    url: '/baseData/tunnel/detection/recheck',
    method: 'put',
    data: data
  })
}

// 复核回退
export function detectionRollback(data) {
  return request({
    url: '/baseData/tunnel/detection/rollback',
    method: 'put',
    data: data.periodicDetectionIds
  })
}

//条件分页查询隧道定期检查列表
export function getTunnelDetectionListPage(params) {
  return request({
    url: '/baseData/tunnel/detection/details/getListPage',
    method: 'get',
    params
  })
}

//删除隧道定期检查
export function deleteTunnelDetectionByIds(data) {
  return request({
    url: '/baseData/tunnel/detection/details/delete',
    method: 'delete',
    data: data
  })
}


//导入评定结果附件
export function periodicAdd(data) {
  return request({
    url: '/baseData/tunnel/detection/import',
    method: 'post',
    data: data
  })
}

