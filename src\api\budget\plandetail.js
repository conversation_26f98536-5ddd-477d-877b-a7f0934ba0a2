import request from '@/utils/request'

// 查询预算计划明细列表
export function listPlandetail(query) {
  return request({
    url: '/budget/plandetail/list',
    method: 'get',
    params: query
  })
}

// 查询预算计划明细详细
export function getPlandetail(id) {
  return request({
    url: '/budget/plandetail/get/' + id,
    method: 'get'
  })
}

// 新增预算计划明细
export function addPlandetail(data) {
  return request({
    url: '/budget/plandetail/add',
    method: 'post',
    data: data
  })
}

// 修改预算计划明细
export function updatePlandetail(data) {
  return request({
    url: '/budget/plandetail/edit',
    method: 'put',
    data: data
  })
}

// 删除预算计划明细
export function delPlandetail(id) {
  return request({
    url: '/budget/plandetail/delete/' + id,
    method: 'delete'
  })
}
